import 'package:dartz/dartz.dart';
import 'package:drift/drift.dart' as db;
import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';

import '../../../../core/datasources/app_database.dart' as db;
import '../../../../core/errors/exceptions.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/services/sync/sync_operations.dart';
import '../../../../core/services/sync/sync_service.dart';
import '../../domain/entities/replacement_history.dart';
import '../../domain/entities/spare_part.dart';
import '../../domain/repositories/spare_parts_repository.dart';

class EnhancedSparePartsRepositoryImpl implements EnhancedSparePartsRepository {
  final db.AppDatabase database;
  final SyncService syncService;

  const EnhancedSparePartsRepositoryImpl({
    required this.database,
    required this.syncService,
  });

  @override
  Future<Either<Failure, List<EnhancedSparePart>>> getAllSpareParts() async {
    try {
      final sparePartsList = await database.getAllSpareParts();

      return Right(sparePartsList
          .map((data) => _mapToEnhancedSparePart(data))
          .toList());
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, EnhancedSparePart>> getSparePartById(int id) async {
    try {
      final query = database.select(database.spareParts)
        ..where((tbl) => tbl.id.equals(id));

      final sparePartData = await query.getSingleOrNull();

      if (sparePartData == null) {
        return const Left(Failure.notFound(message: 'Spare part not found'));
      }

      return Right(_mapToEnhancedSparePart(sparePartData));
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, EnhancedSparePart>> saveSparePart(EnhancedSparePart sparePart) async {
    try {
      final companion = _mapToSparePartCompanion(sparePart); // For new records, no need to pass uuid or originalCreatedAt
      final id = await database.insertSparePart(companion);

      // Create a new EnhancedSparePart with the new ID
      final savedPart = EnhancedSparePart(
        id: id,
        partName: sparePart.partName,
        partType: sparePart.partType,
        price: sparePart.price,
        mileageLimit: sparePart.mileageLimit,
        initialMileage: sparePart.initialMileage,
        installationDate: sparePart.installationDate,
        currentMileage: sparePart.currentMileage,
        warningStatus: sparePart.warningStatus,
        usageMileage: sparePart.usageMileage,
        daysInUse: sparePart.daysInUse,
        usagePercent: sparePart.usagePercent,
        // Include additional fields
        replacementCount: sparePart.replacementCount,
        notes: sparePart.notes,
        createdAt: sparePart.createdAt,
        updatedAt: sparePart.updatedAt,
      );

      return Right(savedPart);
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, EnhancedSparePart>> updateSparePart(EnhancedSparePart sparePart) async {
    try {
      if (sparePart.id == null) {
        return const Left(Failure.invalidInput(message: 'Spare part ID cannot be null for update'));
      }

      // Get the existing record to preserve the UUID
      final query = database.select(database.spareParts)
        ..where((tbl) => tbl.id.equals(sparePart.id!));
      final existingRecord = await query.getSingleOrNull();

      if (existingRecord == null) {
        return const Left(Failure.notFound(message: 'Spare part not found'));
      }

      // Map to data with the existing UUID and original createdAt
      final data = _mapToSparePartDataWithUuid(
        sparePart,
        existingRecord.uuid,
        originalCreatedAt: existingRecord.createdAt
      );
      final success = await database.updateSparePart(data);

      if (!success) {
        return const Left(Failure.notFound(message: 'Spare part not found'));
      }

      return Right(sparePart);
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> deleteSparePart(int id) async {
    try {
      // Use soft delete instead of hard delete
      final success = await database.softDeleteSparePart(id);

      if (!success) {
        return const Left(Failure.notFound(message: 'Spare part not found'));
      }

      // Trigger sync after deleting a record
      syncService.syncData(SyncOperation.upload);

      return const Right(true);
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<EnhancedReplacementHistory>>> getReplacementHistoryForPart(int sparePartId) async {
    try {
      // Get all history entries for this part
      final historyList = await database.getHistoryForSparePart(sparePartId);

      // Debug logging
      debugPrint('Fetching history for spare part ID: $sparePartId');
      debugPrint('History entries found: ${historyList.length}');

      if (historyList.isNotEmpty) {
        debugPrint('First history entry: ${historyList.first.toString()}');
      }

      if (historyList.isEmpty) {
        return const Right([]);
      }

      // Sort by date, oldest first (for processing)
      historyList.sort((a, b) => a.replacementDate.compareTo(b.replacementDate));

      // Create a list to hold our enhanced history entries
      final enhancedHistoryList = <EnhancedReplacementHistory>[];

      // Process each history entry
      for (int i = 0; i < historyList.length; i++) {
        final currentHistory = historyList[i];

        // With our updated database schema, we can now directly use the installation date
        // and initial mileage from the history record itself

        // Create enhanced history entry with all the fields from the database
        final enhancedHistory = EnhancedReplacementHistory.withComputedValues(
          id: currentHistory.id,
          partName: currentHistory.partName,
          partType: currentHistory.partType,
          price: currentHistory.price,
          replacementDate: currentHistory.replacementDate,
          mileageAtReplacement: currentHistory.mileageAtReplacement,
          sparePartId: currentHistory.sparePartId,
          // Use the actual data from the database
          installationDate: currentHistory.installationDate,
          initialMileage: currentHistory.initialMileage,
          // New fields
          replacementReason: currentHistory.replacementReason,
          replacedByPartId: currentHistory.replacedByPartId,
          replacementCount: currentHistory.replacementCount,
          notes: currentHistory.notes,
          createdAt: currentHistory.createdAt,
        );

        enhancedHistoryList.add(enhancedHistory);
      }

      // Sort by date, most recent first (for display)
      enhancedHistoryList.sort((a, b) => b.replacementDate.compareTo(a.replacementDate));

      // Debug logging
      debugPrint('Enhanced history entries created: ${enhancedHistoryList.length}');

      return Right(enhancedHistoryList);
    } on DatabaseException catch (e) {
      debugPrint('Database exception in getReplacementHistoryForPart: ${e.message}');
      return Left(Failure.database(message: e.message));
    } catch (e) {
      debugPrint('Unexpected error in getReplacementHistoryForPart: $e');
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, EnhancedReplacementHistory>> saveReplacementHistory({
    required String partName,
    required String partType,
    required double price,
    required DateTime replacementDate,
    required int mileageAtReplacement,
    required int sparePartId,
    required DateTime installationDate,
    required int initialMileage,
    String replacementReason = 'Regular maintenance',
    int? replacedByPartId,
    int replacementCount = 1,
    String notes = '',
  }) async {
    try {
      // Calculate usage statistics
      final usageDays = replacementDate.difference(installationDate).inDays;
      final usageMileage = mileageAtReplacement - initialMileage;

      // Create the companion object with all the new fields
      final companion = db.SparePartsHistoryCompanion(
        partName: db.Value(partName),
        partType: db.Value(partType),
        price: db.Value(price),
        replacementDate: db.Value(replacementDate),
        mileageAtReplacement: db.Value(mileageAtReplacement),
        sparePartId: db.Value(sparePartId),
        // New fields
        installationDate: db.Value(installationDate),
        initialMileage: db.Value(initialMileage),
        replacementReason: db.Value(replacementReason),
        replacedByPartId: replacedByPartId != null ? db.Value(replacedByPartId) : const db.Value.absent(),
        replacementCount: db.Value(replacementCount),
        usageDays: db.Value(usageDays),
        usageMileage: db.Value(usageMileage),
        notes: db.Value(notes),
        createdAt: db.Value(DateTime.now()),
      );

      final id = await database.insertSparePartHistory(companion);

      // Create enhanced history entry with all the new fields
      final enhancedHistory = EnhancedReplacementHistory.withComputedValues(
        id: id,
        partName: partName,
        partType: partType,
        price: price,
        replacementDate: replacementDate,
        mileageAtReplacement: mileageAtReplacement,
        sparePartId: sparePartId,
        installationDate: installationDate,
        initialMileage: initialMileage,
        replacementReason: replacementReason,
        replacedByPartId: replacedByPartId,
        replacementCount: replacementCount,
        notes: notes,
        createdAt: DateTime.now(),
      );

      return Right(enhancedHistory);
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, int>> getHighestMileage() async {
    try {
      // Use a custom query to get the maximum final_mileage value
      final result = await database.customSelect(
        'SELECT MAX(final_mileage) as maxMileage FROM income',
      ).getSingleOrNull();

      if (result == null || result.data['maxMileage'] == null) {
        return const Right(0); // Default to 0 if no records found
      }

      final maxMileage = result.data['maxMileage'] as int;
      return Right(maxMileage);
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> updateAllPartsMileage(int latestMileage) async {
    try {
      await database.updateSparePartsMileage(latestMileage);
      return const Right(true);
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, ReplacementResult>> replaceSparePart({
    required EnhancedSparePart oldPart,
    required DateTime replacementDate,
    required int currentMileage,
    required double newPartPrice,
    String? newPartName,
    String? newPartType,
    int? newMileageLimit,
    String replacementReason = 'Regular maintenance',
    String notes = '',
  }) async {
    try {
      if (oldPart.id == null) {
        return const Left(Failure.invalidInput(message: 'Spare part ID cannot be null for replacement'));
      }

      // 1. Save the replacement history with all the new fields
      final historyResult = await saveReplacementHistory(
        partName: oldPart.partName,
        partType: oldPart.partType,
        price: oldPart.price,
        replacementDate: replacementDate,
        mileageAtReplacement: currentMileage,
        sparePartId: oldPart.id!,
        installationDate: oldPart.installationDate,
        initialMileage: oldPart.initialMileage,
        replacementReason: replacementReason, // Use the provided reason
        replacedByPartId: null, // Will be updated after creating the new part
        replacementCount: oldPart.replacementCount + 1, // Increment the replacement count
        notes: oldPart.notes, // Use the notes from the old part being replaced
      );

      return historyResult.fold(
        (failure) => Left(failure),
        (savedHistory) async {
          // 2. Create a new spare part with the updated information
          // For the new part, the installation date is the replacement date
          // and the initial mileage is the current mileage
          final updatedPart = EnhancedSparePart.withComputedValues(
            id: oldPart.id,
            partName: newPartName ?? oldPart.partName,
            partType: newPartType ?? oldPart.partType,
            price: newPartPrice,
            mileageLimit: newMileageLimit ?? oldPart.mileageLimit,
            initialMileage: currentMileage,
            installationDate: replacementDate,
            currentMileage: currentMileage,
            warningStatus: false,
            // New fields
            replacementCount: oldPart.replacementCount + 1, // Increment the replacement count
            notes: notes, // Use the provided notes
            createdAt: oldPart.createdAt, // Keep the original creation date
            updatedAt: DateTime.now(), // Update the timestamp
          );

          // 3. Save the updated spare part
          // We're using updateSparePart which will preserve the UUID
          final partResult = await updateSparePart(updatedPart);

          return partResult.fold(
            (failure) => Left(failure),
            (updatedSparePart) => Right(
              ReplacementResult(
                sparePart: updatedSparePart,
                replacementHistory: savedHistory,
              ),
            ),
          );
        },
      );
    } catch (e) {
      return Left(Failure.businessLogic(message: 'Error replacing spare part: $e'));
    }
  }

  // Helper methods to map between entity and database models
  EnhancedSparePart _mapToEnhancedSparePart(db.SparePart data) {
    // Use actual values from the database
    return EnhancedSparePart.withComputedValues(
      id: data.id,
      partName: data.partName,
      partType: data.partType,
      price: data.price,
      mileageLimit: data.mileageLimit,
      initialMileage: data.initialMileage,
      installationDate: data.installationDate,
      currentMileage: data.currentMileage,
      warningStatus: data.warningStatus,
      // Use actual values from the database
      replacementCount: data.replacementCount,
      notes: data.notes,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt,
    );
  }

  // Map to SparePart data with a specific UUID (for updates)
  db.SparePart _mapToSparePartDataWithUuid(EnhancedSparePart sparePart, String uuid, {DateTime? originalCreatedAt}) {
    // The generated code has been updated to include the new fields
    return db.SparePart(
      id: sparePart.id!,
      uuid: uuid,
      partName: sparePart.partName,
      partType: sparePart.partType,
      price: sparePart.price,
      mileageLimit: sparePart.mileageLimit,
      initialMileage: sparePart.initialMileage,
      installationDate: sparePart.installationDate,
      currentMileage: sparePart.currentMileage,
      warningStatus: sparePart.warningStatus,
      replacementCount: sparePart.replacementCount,
      notes: sparePart.notes,
      createdAt: originalCreatedAt ?? sparePart.createdAt, // Preserve original createdAt if provided
      updatedAt: DateTime.now(),
      syncStatus: db.SyncStatus.pendingUpload,
    );
  }

  db.SparePartsCompanion _mapToSparePartCompanion(EnhancedSparePart sparePart, {String? uuid, DateTime? originalCreatedAt}) {
    // The generated code has been updated to include the new fields
    return db.SparePartsCompanion(
      id: sparePart.id == null ? const db.Value.absent() : db.Value(sparePart.id!),
      uuid: db.Value(uuid ?? const Uuid().v4()),
      partName: db.Value(sparePart.partName),
      partType: db.Value(sparePart.partType),
      price: db.Value(sparePart.price),
      mileageLimit: db.Value(sparePart.mileageLimit),
      initialMileage: db.Value(sparePart.initialMileage),
      installationDate: db.Value(sparePart.installationDate),
      currentMileage: db.Value(sparePart.currentMileage),
      warningStatus: db.Value(sparePart.warningStatus),
      replacementCount: db.Value(sparePart.replacementCount),
      notes: db.Value(sparePart.notes),
      createdAt: db.Value(originalCreatedAt ?? sparePart.createdAt), // Preserve original createdAt if provided
      updatedAt: db.Value(DateTime.now()),
      syncStatus: const db.Value(db.SyncStatus.pendingUpload),
    );
  }
}

import 'package:flutter/material.dart';

/// Widget for displaying a section header with an icon and title
class SectionHeader extends StatelessWidget {
  final String title;

  const SectionHeader({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              _getSectionIcon(),
              size: 18,
              color: Theme.of(context).primaryColor,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Divider(color: Colors.grey.withAlpha(30), thickness: 1),
      ],
    );
  }

  /// Get the appropriate icon for the section
  IconData _getSectionIcon() {
    switch (title) {
      case 'Part Information':
        return Icons.build;
      case 'Mileage Information':
        return Icons.speed;
      case 'Additional Information':
        return Icons.info_outline;
      default:
        return Icons.category;
    }
  }
}

import 'package:flutter/material.dart';
import '../../../../core/utils/input_validators.dart';
import '../../../../core/widgets/custom_date_picker.dart';
import '../../../../core/widgets/custom_text_field.dart';

/// Widget for the Mileage Information section of the spare part form
class MileageInformationSection extends StatelessWidget {
  final TextEditingController mileageLimitController;
  final TextEditingController initialMileageController;
  final DateTime installationDate;
  final bool isEditing;
  final bool useCurrentMileage;
  final Function(DateTime) onDateSelected;
  final Function(bool?) onUseCurrentMileageChanged;
  final Widget Function(String) buildSectionHeader;

  const MileageInformationSection({
    super.key,
    required this.mileageLimitController,
    required this.initialMileageController,
    required this.installationDate,
    required this.isEditing,
    required this.useCurrentMileage,
    required this.onDateSelected,
    required this.onUseCurrentMileageChanged,
    required this.buildSectionHeader,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildSectionHeader('Mileage Information'),
        const SizedBox(height: 16),
        CustomTextField(
          controller: mileageLimitController,
          labelText: 'Mileage Limit (km)',
          hintText: 'After how many kilometers should this part be replaced?',
          prefixIcon: Icons.speed,
          keyboardType: TextInputType.number,
          validator: InputValidators.compose([
            InputValidators.required('Mileage limit is required'),
            InputValidators.integer('Mileage limit must be a whole number'),
            InputValidators.min(1, 'Mileage limit must be positive'),
          ]),
        ),
        const SizedBox(height: 16),

        // Installation Date
        Text(
          'Installation Date',
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        CustomDatePicker(
          initialDate: installationDate,
          onDateSelected: onDateSelected,
          firstDate: DateTime(2000),
          lastDate: DateTime.now(),
        ),
        const SizedBox(height: 16),

        // Initial Mileage
        if (!isEditing) ...[
          Row(
            children: [
              Checkbox(
                value: useCurrentMileage,
                onChanged: onUseCurrentMileageChanged,
              ),
              const Text('Use current vehicle mileage'),
            ],
          ),
        ],
        CustomTextField(
          controller: initialMileageController,
          labelText: 'Initial Mileage (km)',
          hintText:
              'What was the vehicle mileage when this part was installed?',
          prefixIcon: Icons.speed,
          keyboardType: TextInputType.number,
          enabled: !useCurrentMileage || isEditing,
          validator: InputValidators.compose([
            InputValidators.required('Initial mileage is required'),
            InputValidators.integer('Initial mileage must be a whole number'),
            InputValidators.min(0, 'Initial mileage must be non-negative'),
          ]),
        ),
      ],
    );
  }
}

import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/calculations.dart';
import '../../../../core/utils/date_formatter.dart';
import '../../../../core/utils/date_helper.dart';
import '../../domain/entities/spare_part.dart';

class SparePartCard extends StatelessWidget {
  final EnhancedSparePart sparePart;
  final VoidCallback onTap;
  final VoidCallback? onLongPress;

  const SparePartCard({
    super.key,
    required this.sparePart,
    required this.onTap,
    this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    final formattedDate = DateHelper.formatForDisplay(
      sparePart.installationDate,
    );
    final formattedPrice = Calculations.formatCurrency(sparePart.price);

    // Format usage period
    final usagePeriod = DateFormatter.formatTimePeriod(sparePart.daysInUse);

    // Calculate usage percentage for progress indicator
    final usagePercent = sparePart.usagePercent.clamp(0.0, 100.0) / 100.0;

    // Determine color based on usage percentage
    Color progressColor;
    if (usagePercent < 0.7) {
      progressColor = AppColors.success;
    } else if (usagePercent < 0.9) {
      progressColor = AppColors.warning;
    } else {
      progressColor = AppColors.error;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: sparePart.warningStatus
              ? AppColors.error.withAlpha(30)
              : Colors.grey.withAlpha(30),
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        onLongPress: onLongPress,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Part Type (moved to top)
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.secondary.withAlpha(15),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          _getIconForPartType(sparePart.partType),
                          size: 14,
                          color: AppColors.secondary,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          sparePart.partType.toUpperCase(),
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(
                                color: AppColors.secondary,
                                fontWeight: FontWeight.bold,
                                fontSize: 11,
                              ),
                        ),
                      ],
                    ),
                  ),
                  const Spacer(),
                  // Add replacement count badge
                  if (sparePart.replacementCount > 0)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 3,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.purple.withAlpha(15),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.history,
                            size: 12,
                            color: Colors.purple,
                          ),
                          const SizedBox(width: 2),
                          Text(
                            '${sparePart.replacementCount}x',
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(
                                  color: Colors.purple,
                                  fontWeight: FontWeight.w500,
                                  fontSize: 10,
                                ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 8),
              // Part Name with icon
              Row(
                children: [
                  Icon(Icons.build, color: progressColor, size: 18),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      sparePart.partName,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  _buildInfoItem(
                    Icons.calendar_today,
                    'Installed',
                    formattedDate,
                  ),
                  const SizedBox(width: 16),
                  _buildInfoItem(
                    Icons.monetization_on,
                    'Price',
                    formattedPrice,
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  _buildInfoItem(
                    Icons.speed,
                    'Mileage',
                    '${sparePart.usageMileage} / ${sparePart.mileageLimit} km',
                  ),
                  const SizedBox(width: 16),
                  _buildInfoItem(Icons.access_time, 'Age', usagePeriod),
                ],
              ),
              const SizedBox(height: 16),
              // Usage section with simplified styling
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.analytics, color: progressColor, size: 16),
                          const SizedBox(width: 6),
                          Text(
                            'Usage Status',
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(fontWeight: FontWeight.w600),
                          ),
                        ],
                      ),
                      Text(
                        '${sparePart.usagePercent.toStringAsFixed(1)}%',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: progressColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  LinearProgressIndicator(
                    value: usagePercent,
                    backgroundColor: Colors.grey.withAlpha(30),
                    valueColor: AlwaysStoppedAnimation<Color>(progressColor),
                    minHeight: 6,
                    borderRadius: BorderRadius.circular(3),
                  ),
                  const SizedBox(height: 6),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '${sparePart.usageMileage} km used',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        '${sparePart.mileageLimit} km limit',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              // Display notes if available
              if (sparePart.notes.isNotEmpty) ...[
                const SizedBox(height: 12),
                const Divider(height: 1),
                const SizedBox(height: 12),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Icon(
                      Icons.note_rounded,
                      color: Colors.blue,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        sparePart.notes,
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ),
                  ],
                ),
              ],

              // Display created and updated dates
              const SizedBox(height: 8),
              const Divider(height: 1),
              const SizedBox(height: 4),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Created: ${DateHelper.formatForDisplay(sparePart.createdAt)}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.textSecondary,
                      fontSize: 10,
                    ),
                  ),
                  Text(
                    'Updated: ${DateHelper.formatForDisplay(sparePart.updatedAt)}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.textSecondary,
                      fontSize: 10,
                    ),
                  ),
                ],
              ),

              if (sparePart.warningStatus) ...[
                const SizedBox(height: 12),
                const Divider(height: 1),
                const SizedBox(height: 12),
                Row(
                  children: [
                    const Icon(
                      Icons.warning_amber_rounded,
                      color: AppColors.error,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Replacement Recommended',
                            style: Theme.of(context).textTheme.titleSmall
                                ?.copyWith(
                                  color: AppColors.error,
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            'This part has exceeded ${sparePart.usagePercent.toStringAsFixed(1)}% of its recommended usage limit',
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(
                                  color: AppColors.error.withAlpha(200),
                                ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String label, String value) {
    return Expanded(
      child: Builder(
        builder: (context) => Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(icon, size: 14, color: AppColors.textSecondary),
            const SizedBox(width: 6),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.textSecondary,
                      fontSize: 11,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    value,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper method to get appropriate icon based on part type
  IconData _getIconForPartType(String partType) {
    switch (partType.toLowerCase()) {
      case 'oil':
        return Icons.oil_barrel;
      case 'filter':
        return Icons.filter_alt;
      case 'brake':
        return Icons.do_not_step; // Similar to brake symbol
      case 'tire':
      case 'tyre':
        return Icons.circle; // Represents a tire
      case 'battery':
        return Icons.battery_full;
      case 'belt':
        return Icons.line_style;
      case 'chain':
        return Icons.link;
      case 'spark plug':
        return Icons.electric_bolt;
      case 'suspension':
        return Icons.height;
      case 'transmission':
        return Icons.settings;
      case 'clutch':
        return Icons.adjust; // Represents clutch mechanism
      case 'cooling':
        return Icons.ac_unit;
      case 'electrical':
        return Icons.electrical_services;
      case 'fuel':
        return Icons.local_gas_station;
      case 'exhaust':
        return Icons.air;
      case 'steering':
        return Icons.control_camera; // Similar to steering control
      case 'light':
        return Icons.lightbulb;
      default:
        return Icons.build; // Default icon for other part types
    }
  }
}

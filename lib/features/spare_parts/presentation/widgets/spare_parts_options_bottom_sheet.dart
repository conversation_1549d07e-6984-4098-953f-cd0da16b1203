import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';

class SparePartsOptionsBottomSheet extends StatelessWidget {
  final int sparePartId;
  final VoidCallback onViewHistory;
  final VoidCallback onReplacePart;
  final VoidCallback onEditPart;
  final VoidCallback onDeletePart;

  const SparePartsOptionsBottomSheet({
    super.key,
    required this.sparePartId,
    required this.onViewHistory,
    required this.onReplacePart,
    required this.onEditPart,
    required this.onDeletePart,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 8, bottom: 4),
            height: 4,
            width: 40,
            decoration: BoxDecoration(
              color: Colors.grey.with<PERSON><PERSON><PERSON>(80),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Title
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
            child: Row(
              children: [
                const Icon(Icons.build, color: AppColors.primary, size: 20),
                const SizedBox(width: 8),
                Text(
                  'Spare Part Options',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),

          const Divider(),

          // Action buttons
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Column(
              children: [
                _buildActionButton(
                  context: context,
                  icon: Icons.history,
                  iconColor: AppColors.info,
                  label: 'View Replacement History',
                  description: 'See previous replacements for this part',
                  onTap: () {
                    Navigator.pop(context);
                    onViewHistory();
                  },
                ),

                _buildActionButton(
                  context: context,
                  icon: Icons.swap_horiz,
                  iconColor: AppColors.success,
                  label: 'Replace Part',
                  description: 'Record a replacement for this part',
                  onTap: () {
                    Navigator.pop(context);
                    onReplacePart();
                  },
                ),

                _buildActionButton(
                  context: context,
                  icon: Icons.edit,
                  iconColor: AppColors.warning,
                  label: 'Edit Part',
                  description: 'Modify part details and settings',
                  onTap: () {
                    Navigator.pop(context);
                    onEditPart();
                  },
                ),

                _buildActionButton(
                  context: context,
                  icon: Icons.delete,
                  iconColor: AppColors.error,
                  label: 'Delete Part',
                  description: 'Remove this part from your records',
                  isDestructive: true,
                  onTap: () {
                    Navigator.pop(context);
                    onDeletePart();
                  },
                ),
              ],
            ),
          ),

          // Cancel button
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
            child: SizedBox(
              width: double.infinity,
              child: TextButton(
                onPressed: () => Navigator.pop(context),
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child: const Text('Cancel'),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required BuildContext context,
    required IconData icon,
    required Color iconColor,
    required String label,
    required String description,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        margin: const EdgeInsets.only(bottom: 8),
        decoration: BoxDecoration(
          color: isDestructive
              ? AppColors.errorLight.withAlpha(30)
              : Colors.grey.withAlpha(10),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(icon, color: iconColor, size: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: isDestructive ? AppColors.error : null,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    description,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: isDestructive
                          ? AppColors.error.withAlpha(180)
                          : Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.chevron_right,
              color: isDestructive ? AppColors.error : Colors.grey[400],
              size: 18,
            ),
          ],
        ),
      ),
    );
  }
}

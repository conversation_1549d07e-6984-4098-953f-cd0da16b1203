import 'package:flutter/material.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/date_formatter.dart';
import '../../../../core/utils/date_helper.dart';
import '../../domain/entities/spare_part.dart';
import 'stat_widgets.dart';

/// Widget to display current part statistics
class CurrentPartStatisticsWidget extends StatelessWidget {
  final EnhancedSparePart sparePart;

  const CurrentPartStatisticsWidget({super.key, required this.sparePart});

  @override
  Widget build(BuildContext context) {
    // Calculate usage period
    final daysInUse = DateTime.now()
        .difference(sparePart.installationDate)
        .inDays;
    String usagePeriod;
    if (daysInUse <= 0) {
      usagePeriod = 'Today';
    } else {
      usagePeriod = DateFormatter.formatTimePeriod(daysInUse);
    }

    // Format distance
    String distanceText;
    if (sparePart.usageMileage <= 0) {
      distanceText = 'No distance yet';
    } else {
      distanceText = '${sparePart.usageMileage} km';
    }

    // Calculate usage percentage for progress indicator
    final usagePercent = sparePart.usagePercent.clamp(0.0, 100.0) / 100.0;

    // Determine color based on usage percentage
    Color progressColor;
    if (usagePercent < 0.7) {
      progressColor = AppColors.success;
    } else if (usagePercent < 0.9) {
      progressColor = AppColors.warning;
    } else {
      progressColor = AppColors.error;
    }

    return Card(
      elevation: 0,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: AppColors.primary.withAlpha(30), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.primary.withAlpha(20),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                const Icon(Icons.build, color: AppColors.primary, size: 20),
                const SizedBox(width: 12),
                Text(
                  'Current Part Statistics',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

          // Installation details
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                StatWidgets.buildStatSection(
                  context,
                  'Installation Details',
                  Icons.calendar_today,
                  Colors.blue,
                  [
                    StatWidgets.buildStatItem(
                      context,
                      'Installation Date',
                      DateHelper.formatForDisplay(sparePart.installationDate),
                      Icons.event,
                      Colors.blue,
                    ),
                    StatWidgets.buildStatItem(
                      context,
                      'Initial Mileage',
                      '${sparePart.initialMileage} km',
                      Icons.speed,
                      Colors.blue,
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // Usage statistics
                StatWidgets.buildStatSection(
                  context,
                  'Usage Statistics',
                  Icons.analytics,
                  progressColor,
                  [
                    StatWidgets.buildStatItem(
                      context,
                      'Used For',
                      usagePeriod,
                      Icons.access_time,
                      progressColor,
                    ),
                    StatWidgets.buildStatItem(
                      context,
                      'Distance',
                      distanceText,
                      Icons.straighten,
                      progressColor,
                    ),
                    StatWidgets.buildStatItem(
                      context,
                      'Replacement Count',
                      '${sparePart.replacementCount}',
                      Icons.repeat,
                      progressColor,
                    ),

                    // Usage progress bar
                    const SizedBox(height: 16),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Usage Progress',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(fontWeight: FontWeight.w500),
                            ),
                            Text(
                              '${sparePart.usagePercent.toStringAsFixed(1)}%',
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(
                                    color: progressColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        ClipRRect(
                          borderRadius: BorderRadius.circular(4),
                          child: LinearProgressIndicator(
                            value: usagePercent,
                            backgroundColor: Colors.grey.shade200,
                            color: progressColor,
                            minHeight: 4,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),

                // Notes if available
                if (sparePart.notes.isNotEmpty) ...[
                  const SizedBox(height: 20),
                  StatWidgets.buildStatSection(
                    context,
                    'Notes',
                    Icons.note,
                    Colors.purple,
                    [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.purple.withAlpha(10),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          sparePart.notes,
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ),
                    ],
                  ),
                ],

                const SizedBox(height: 20),

                // Record information
                StatWidgets.buildStatSection(
                  context,
                  'Record Information',
                  Icons.info_outline,
                  Colors.grey,
                  [
                    StatWidgets.buildStatItem(
                      context,
                      'Created',
                      DateHelper.formatTimestamp(sparePart.createdAt),
                      Icons.event_available,
                      Colors.grey,
                    ),
                    StatWidgets.buildStatItem(
                      context,
                      'Last Updated',
                      DateHelper.formatTimestamp(sparePart.updatedAt),
                      Icons.update,
                      Colors.grey,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

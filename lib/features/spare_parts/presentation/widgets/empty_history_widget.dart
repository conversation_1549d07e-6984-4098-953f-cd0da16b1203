import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../core/theme/app_colors.dart';
import '../providers/spare_parts_providers.dart';
import 'current_part_statistics_widget.dart';

/// Widget displayed when there is no replacement history for a spare part
class EmptyHistoryWidget extends ConsumerWidget {
  final int sparePartId;

  const EmptyHistoryWidget({super.key, required this.sparePartId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final sparePartAsync = ref.watch(enhancedSparePartProvider(sparePartId));

    return sparePartAsync.when(
      data: (sparePart) {
        if (sparePart == null) {
          return const Center(child: Text('Spare part not found'));
        }

        // Get the WidgetRef from the context
        final container = ProviderScope.containerOf(context);

        return RefreshIndicator(
          onRefresh: () async {
            container.refresh(enhancedReplacementHistoryProvider(sparePartId));
          },
          child: ListView(
            physics: const AlwaysScrollableScrollPhysics(),
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 24.0, bottom: 8.0),
                child: Center(
                  child: Card(
                    elevation: 0,
                    margin: const EdgeInsets.symmetric(horizontal: 32),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                      side: BorderSide(
                        color: AppColors.primary.withAlpha(30),
                        width: 1,
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(24),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.history,
                            size: 40,
                            color: AppColors.info,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No Replacement History',
                            style: Theme.of(context).textTheme.titleLarge
                                ?.copyWith(fontWeight: FontWeight.bold),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 12),
                          Text(
                            'When you replace this part, the history will be recorded here',
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(color: Colors.grey[600]),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.refresh,
                                size: 16,
                                color: AppColors.textSecondary,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'Pull down to refresh',
                                style: Theme.of(context).textTheme.bodySmall
                                    ?.copyWith(color: AppColors.textSecondary),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 4),
              CurrentPartStatisticsWidget(sparePart: sparePart),
            ],
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, _) =>
          Center(child: Text('Error loading spare part: $error')),
    );
  }
}

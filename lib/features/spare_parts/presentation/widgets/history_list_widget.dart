import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../domain/entities/replacement_history.dart';
import '../providers/spare_parts_providers.dart';
import 'history_card_widget.dart';

/// Widget to display a list of replacement history entries
class HistoryListWidget extends StatelessWidget {
  final List<EnhancedReplacementHistory> historyEntries;
  final int sparePartId;

  const HistoryListWidget({
    super.key,
    required this.historyEntries,
    required this.sparePartId,
  });

  @override
  Widget build(BuildContext context) {
    // Get the WidgetRef from the context
    final container = ProviderScope.containerOf(context);

    return RefreshIndicator(
      onRefresh: () async {
        container.refresh(enhancedReplacementHistoryProvider(sparePartId));
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: historyEntries.length,
        itemBuilder: (context, index) =>
            HistoryCardWidget(history: historyEntries[index], index: index),
      ),
    );
  }
}

import 'package:flutter/material.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/calculations.dart';
import '../../../../core/utils/date_formatter.dart';
import '../../../../core/utils/date_helper.dart';
import '../../domain/entities/replacement_history.dart';

/// Widget to display a single replacement history card
class HistoryCardWidget extends StatelessWidget {
  final EnhancedReplacementHistory history;
  final int index;

  const HistoryCardWidget({
    super.key,
    required this.history,
    required this.index,
  });

  @override
  Widget build(BuildContext context) {
    // Use DateHelper for consistent date formatting
    final formattedReplacementDate = DateHelper.formatForDisplay(
      history.replacementDate,
    );
    final formattedMileage = '${history.mileageAtReplacement} km';
    final formattedPrice = Calculations.formatCurrency(history.price);

    // Format usage statistics
    String usagePeriod;
    if (history.usageDays <= 0) {
      usagePeriod = 'Same day';
    } else {
      usagePeriod = DateFormatter.formatTimePeriod(history.usageDays);
    }

    String distanceTraveled;
    if (history.usageMileage <= 0) {
      distanceTraveled = 'No distance';
    } else {
      distanceTraveled = '${history.usageMileage} km';
    }

    // Determine color based on index
    final Color cardColor = index == 0 ? AppColors.primary : Colors.grey;

    return Card(
      elevation: 1,
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: cardColor.withAlpha(20),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      width: 24,
                      height: 24,
                      alignment: Alignment.center,
                      child: Icon(Icons.history, color: cardColor, size: 20),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Replacement #${index + 1}',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: cardColor.withAlpha(15),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    index == 0 ? 'Most Recent' : 'Previous',
                    style: TextStyle(
                      color: cardColor,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Part summary
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Part name and type
                Text(
                  history.partName,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  history.partType,
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
                ),

                const SizedBox(height: 20),

                // Key information in a grid layout
                Container(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.grey.withAlpha(10),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: [
                      // First row with proper alignment
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          Expanded(
                            child: _buildInfoItem(
                              context,
                              'Replaced',
                              formattedReplacementDate,
                              Icons.calendar_month,
                              cardColor,
                            ),
                          ),
                          Expanded(
                            child: _buildInfoItem(
                              context,
                              'Mileage',
                              formattedMileage,
                              Icons.speed,
                              cardColor,
                            ),
                          ),
                          Expanded(
                            child: _buildInfoItem(
                              context,
                              'Price',
                              formattedPrice,
                              Icons.monetization_on,
                              cardColor,
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),
                      const Divider(
                        height: 1,
                        thickness: 1,
                        indent: 16,
                        endIndent: 16,
                        color: Colors.black12,
                      ),
                      const SizedBox(height: 16),

                      // Second row with proper alignment
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          Expanded(
                            child: _buildInfoItem(
                              context,
                              'Used For',
                              usagePeriod,
                              Icons.access_time,
                              cardColor,
                            ),
                          ),
                          //Expanded(
                          //  child: Container(), // Empty space in the middle
                          //),
                          Expanded(
                            child: _buildInfoItem(
                              context,
                              'Distance',
                              distanceTraveled,
                              Icons.straighten,
                              cardColor,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Divider
          Divider(height: 1, thickness: 1, color: Colors.grey.withAlpha(30)),

          // Expandable sections with consistent styling
          Theme(
            data: Theme.of(context).copyWith(
              dividerColor: Colors.transparent,
              colorScheme: Theme.of(
                context,
              ).colorScheme.copyWith(surface: Colors.white),
            ),
            child: Column(
              children: [
                ExpansionTile(
                  title: Text(
                    'Replacement Reason',
                    style: TextStyle(
                      color: Colors.grey[800],
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                  leading: Container(
                    width: 24,
                    height: 24,
                    alignment: Alignment.center,
                    child: Icon(
                      Icons.info_outline,
                      color: Colors.grey[700],
                      size: 20,
                    ),
                  ),
                  tilePadding: const EdgeInsets.symmetric(horizontal: 16),
                  childrenPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(bottom: 8.0),
                      child: Text(
                        history.replacementReason,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ),

                    // Notes if they exist
                    if (history.notes.isNotEmpty) ...[
                      const SizedBox(height: 16),
                      Text(
                        'Notes:',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        history.notes,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ],
                  ],
                ),

                const Divider(height: 1, indent: 16, endIndent: 16),

                ExpansionTile(
                  title: Text(
                    'Additional Information',
                    style: TextStyle(
                      color: Colors.grey[800],
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                  leading: Container(
                    width: 24,
                    height: 24,
                    alignment: Alignment.center,
                    child: Icon(
                      Icons.more_horiz,
                      color: Colors.grey[700],
                      size: 20,
                    ),
                  ),
                  tilePadding: const EdgeInsets.symmetric(horizontal: 16),
                  childrenPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                  children: [
                    _buildDetailRow(
                      context,
                      'Replacement #',
                      history.replacementCount.toString(),
                      'Record Created',
                      DateHelper.formatTimestamp(history.createdAt),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build info items
  Widget _buildInfoItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          width: 24,
          height: 24,
          alignment: Alignment.center,
          child: Icon(icon, size: 18, color: color),
        ),
        const SizedBox(height: 6),
        Text(
          value,
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
          textAlign: TextAlign.center,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 11,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  // Helper method to build detail rows
  Widget _buildDetailRow(
    BuildContext context,
    String label1,
    String value1,
    String label2,
    String value2,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label1,
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 4),
              Text(value1, style: Theme.of(context).textTheme.bodyMedium),
            ],
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label2,
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 4),
              Text(value2, style: Theme.of(context).textTheme.bodyMedium),
            ],
          ),
        ),
      ],
    );
  }
}

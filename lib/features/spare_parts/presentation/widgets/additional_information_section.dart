import 'package:flutter/material.dart';
import '../../../../core/widgets/custom_text_field.dart';

/// Widget for the Additional Information section of the spare part form
class AdditionalInformationSection extends StatelessWidget {
  final TextEditingController notesController;
  final Widget Function(String) buildSectionHeader;

  const AdditionalInformationSection({
    super.key,
    required this.notesController,
    required this.buildSectionHeader,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildSectionHeader('Additional Information'),
        const SizedBox(height: 16),

        // Notes field
        CustomTextField(
          controller: notesController,
          labelText: 'Notes (Optional)',
          hintText: 'Add any notes about this part',
          prefixIcon: Icons.note,
          maxLines: 3,
        ),
      ],
    );
  }
}

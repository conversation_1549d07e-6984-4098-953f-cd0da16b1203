import 'package:flutter/material.dart';
import '../../../../core/utils/input_validators.dart';
import '../../../../core/widgets/custom_text_field.dart';

/// Widget for the Part Information section of the spare part form
class PartInformationSection extends StatelessWidget {
  final TextEditingController partNameController;
  final TextEditingController partTypeController;
  final TextEditingController priceController;
  final Widget Function(String) buildSectionHeader;

  const PartInformationSection({
    super.key,
    required this.partNameController,
    required this.partTypeController,
    required this.priceController,
    required this.buildSectionHeader,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildSectionHeader('Part Information'),
        const Sized<PERSON>ox(height: 16),
        CustomTextField(
          controller: partNameController,
          labelText: 'Part Name',
          hintText: 'Enter the name of the part',
          prefixIcon: Icons.build,
          validator: InputValidators.required('Part name is required'),
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: partTypeController,
          labelText: 'Part Type/Category',
          hintText: 'Enter the type or category of the part',
          prefixIcon: Icons.category,
          validator: InputValidators.required('Part type is required'),
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: priceController,
          labelText: 'Part Price',
          hintText: 'Enter the price of the part',
          prefixIcon: Icons.monetization_on,
          keyboardType: TextInputType.number,
          validator: InputValidators.compose([
            InputValidators.required('Price is required'),
            InputValidators.numeric('Price must be a number'),
            InputValidators.min(0, 'Price must be positive'),
          ]),
        ),
      ],
    );
  }
}

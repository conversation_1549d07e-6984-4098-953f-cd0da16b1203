// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'spare_parts_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$enhancedSparePartsRepositoryHash() =>
    r'26cff5e19feb3dea859a1fd090ad6df7121c7fef';

/// See also [enhancedSparePartsRepository].
@ProviderFor(enhancedSparePartsRepository)
final enhancedSparePartsRepositoryProvider =
    AutoDisposeProvider<EnhancedSparePartsRepository>.internal(
      enhancedSparePartsRepository,
      name: r'enhancedSparePartsRepositoryProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$enhancedSparePartsRepositoryHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef EnhancedSparePartsRepositoryRef =
    AutoDisposeProviderRef<EnhancedSparePartsRepository>;
String _$getEnhancedSparePartsHash() =>
    r'f8392427c0e7f9ac064b0db8657782411f0f9a55';

/// See also [getEnhancedSpareParts].
@ProviderFor(getEnhancedSpareParts)
final getEnhancedSparePartsProvider =
    AutoDisposeProvider<GetEnhancedSpareParts>.internal(
      getEnhancedSpareParts,
      name: r'getEnhancedSparePartsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$getEnhancedSparePartsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef GetEnhancedSparePartsRef =
    AutoDisposeProviderRef<GetEnhancedSpareParts>;
String _$getEnhancedSparePartHash() =>
    r'adf8e20e3fcfdee217800112a4a5ea79c5b4bacf';

/// See also [getEnhancedSparePart].
@ProviderFor(getEnhancedSparePart)
final getEnhancedSparePartProvider =
    AutoDisposeProvider<GetEnhancedSparePart>.internal(
      getEnhancedSparePart,
      name: r'getEnhancedSparePartProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$getEnhancedSparePartHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef GetEnhancedSparePartRef = AutoDisposeProviderRef<GetEnhancedSparePart>;
String _$addEnhancedSparePartHash() =>
    r'98df7c5935ee3ca2d98744f4319f69959d2a4576';

/// See also [addEnhancedSparePart].
@ProviderFor(addEnhancedSparePart)
final addEnhancedSparePartProvider =
    AutoDisposeProvider<AddEnhancedSparePart>.internal(
      addEnhancedSparePart,
      name: r'addEnhancedSparePartProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$addEnhancedSparePartHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AddEnhancedSparePartRef = AutoDisposeProviderRef<AddEnhancedSparePart>;
String _$replaceEnhancedSparePartHash() =>
    r'7afbb831fa74a21ef9a800720b8d2b5a9791ce72';

/// See also [replaceEnhancedSparePart].
@ProviderFor(replaceEnhancedSparePart)
final replaceEnhancedSparePartProvider =
    AutoDisposeProvider<ReplaceEnhancedSparePart>.internal(
      replaceEnhancedSparePart,
      name: r'replaceEnhancedSparePartProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$replaceEnhancedSparePartHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ReplaceEnhancedSparePartRef =
    AutoDisposeProviderRef<ReplaceEnhancedSparePart>;
String _$getEnhancedReplacementHistoryHash() =>
    r'0ef74f85eb6995b72f929d9372cb4d97f68aa46a';

/// See also [getEnhancedReplacementHistory].
@ProviderFor(getEnhancedReplacementHistory)
final getEnhancedReplacementHistoryProvider =
    AutoDisposeProvider<GetEnhancedReplacementHistory>.internal(
      getEnhancedReplacementHistory,
      name: r'getEnhancedReplacementHistoryProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$getEnhancedReplacementHistoryHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef GetEnhancedReplacementHistoryRef =
    AutoDisposeProviderRef<GetEnhancedReplacementHistory>;
String _$enhancedSparePartsListHash() =>
    r'23aa62490005e4b5f0ead7a76d25590cd90992f1';

/// See also [enhancedSparePartsList].
@ProviderFor(enhancedSparePartsList)
final enhancedSparePartsListProvider =
    AutoDisposeFutureProvider<List<EnhancedSparePart>>.internal(
      enhancedSparePartsList,
      name: r'enhancedSparePartsListProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$enhancedSparePartsListHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef EnhancedSparePartsListRef =
    AutoDisposeFutureProviderRef<List<EnhancedSparePart>>;
String _$enhancedSparePartHash() => r'3c0d6bdca43985c6484e06e409fca437b3a146b5';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [enhancedSparePart].
@ProviderFor(enhancedSparePart)
const enhancedSparePartProvider = EnhancedSparePartFamily();

/// See also [enhancedSparePart].
class EnhancedSparePartFamily extends Family<AsyncValue<EnhancedSparePart?>> {
  /// See also [enhancedSparePart].
  const EnhancedSparePartFamily();

  /// See also [enhancedSparePart].
  EnhancedSparePartProvider call(int id) {
    return EnhancedSparePartProvider(id);
  }

  @override
  EnhancedSparePartProvider getProviderOverride(
    covariant EnhancedSparePartProvider provider,
  ) {
    return call(provider.id);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'enhancedSparePartProvider';
}

/// See also [enhancedSparePart].
class EnhancedSparePartProvider
    extends AutoDisposeFutureProvider<EnhancedSparePart?> {
  /// See also [enhancedSparePart].
  EnhancedSparePartProvider(int id)
    : this._internal(
        (ref) => enhancedSparePart(ref as EnhancedSparePartRef, id),
        from: enhancedSparePartProvider,
        name: r'enhancedSparePartProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$enhancedSparePartHash,
        dependencies: EnhancedSparePartFamily._dependencies,
        allTransitiveDependencies:
            EnhancedSparePartFamily._allTransitiveDependencies,
        id: id,
      );

  EnhancedSparePartProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.id,
  }) : super.internal();

  final int id;

  @override
  Override overrideWith(
    FutureOr<EnhancedSparePart?> Function(EnhancedSparePartRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: EnhancedSparePartProvider._internal(
        (ref) => create(ref as EnhancedSparePartRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        id: id,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<EnhancedSparePart?> createElement() {
    return _EnhancedSparePartProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is EnhancedSparePartProvider && other.id == id;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, id.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin EnhancedSparePartRef on AutoDisposeFutureProviderRef<EnhancedSparePart?> {
  /// The parameter `id` of this provider.
  int get id;
}

class _EnhancedSparePartProviderElement
    extends AutoDisposeFutureProviderElement<EnhancedSparePart?>
    with EnhancedSparePartRef {
  _EnhancedSparePartProviderElement(super.provider);

  @override
  int get id => (origin as EnhancedSparePartProvider).id;
}

String _$enhancedReplacementHistoryHash() =>
    r'7b5c52f127787099deee734c318453f28182e889';

/// See also [enhancedReplacementHistory].
@ProviderFor(enhancedReplacementHistory)
const enhancedReplacementHistoryProvider = EnhancedReplacementHistoryFamily();

/// See also [enhancedReplacementHistory].
class EnhancedReplacementHistoryFamily
    extends Family<AsyncValue<List<EnhancedReplacementHistory>>> {
  /// See also [enhancedReplacementHistory].
  const EnhancedReplacementHistoryFamily();

  /// See also [enhancedReplacementHistory].
  EnhancedReplacementHistoryProvider call(int sparePartId) {
    return EnhancedReplacementHistoryProvider(sparePartId);
  }

  @override
  EnhancedReplacementHistoryProvider getProviderOverride(
    covariant EnhancedReplacementHistoryProvider provider,
  ) {
    return call(provider.sparePartId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'enhancedReplacementHistoryProvider';
}

/// See also [enhancedReplacementHistory].
class EnhancedReplacementHistoryProvider
    extends AutoDisposeFutureProvider<List<EnhancedReplacementHistory>> {
  /// See also [enhancedReplacementHistory].
  EnhancedReplacementHistoryProvider(int sparePartId)
    : this._internal(
        (ref) => enhancedReplacementHistory(
          ref as EnhancedReplacementHistoryRef,
          sparePartId,
        ),
        from: enhancedReplacementHistoryProvider,
        name: r'enhancedReplacementHistoryProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$enhancedReplacementHistoryHash,
        dependencies: EnhancedReplacementHistoryFamily._dependencies,
        allTransitiveDependencies:
            EnhancedReplacementHistoryFamily._allTransitiveDependencies,
        sparePartId: sparePartId,
      );

  EnhancedReplacementHistoryProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.sparePartId,
  }) : super.internal();

  final int sparePartId;

  @override
  Override overrideWith(
    FutureOr<List<EnhancedReplacementHistory>> Function(
      EnhancedReplacementHistoryRef provider,
    )
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: EnhancedReplacementHistoryProvider._internal(
        (ref) => create(ref as EnhancedReplacementHistoryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        sparePartId: sparePartId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<EnhancedReplacementHistory>>
  createElement() {
    return _EnhancedReplacementHistoryProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is EnhancedReplacementHistoryProvider &&
        other.sparePartId == sparePartId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, sparePartId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin EnhancedReplacementHistoryRef
    on AutoDisposeFutureProviderRef<List<EnhancedReplacementHistory>> {
  /// The parameter `sparePartId` of this provider.
  int get sparePartId;
}

class _EnhancedReplacementHistoryProviderElement
    extends AutoDisposeFutureProviderElement<List<EnhancedReplacementHistory>>
    with EnhancedReplacementHistoryRef {
  _EnhancedReplacementHistoryProviderElement(super.provider);

  @override
  int get sparePartId =>
      (origin as EnhancedReplacementHistoryProvider).sparePartId;
}

String _$addSparePartOperationHash() =>
    r'88d88c4aa1d5512ea8176d7533731b6472263fb7';

/// See also [AddSparePartOperation].
@ProviderFor(AddSparePartOperation)
final addSparePartOperationProvider =
    AutoDisposeAsyncNotifierProvider<
      AddSparePartOperation,
      EnhancedSparePart?
    >.internal(
      AddSparePartOperation.new,
      name: r'addSparePartOperationProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$addSparePartOperationHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$AddSparePartOperation = AutoDisposeAsyncNotifier<EnhancedSparePart?>;
String _$replaceSparePartOperationHash() =>
    r'5e332781215c895c60a99fe064ff9270a9bacbc1';

/// See also [ReplaceSparePartOperation].
@ProviderFor(ReplaceSparePartOperation)
final replaceSparePartOperationProvider =
    AutoDisposeAsyncNotifierProvider<
      ReplaceSparePartOperation,
      ReplacementResult?
    >.internal(
      ReplaceSparePartOperation.new,
      name: r'replaceSparePartOperationProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$replaceSparePartOperationHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$ReplaceSparePartOperation =
    AutoDisposeAsyncNotifier<ReplacementResult?>;
String _$deleteSparePartOperationHash() =>
    r'e86a929bdb5ce4eaf89d7d4af81773f85748cd90';

/// See also [DeleteSparePartOperation].
@ProviderFor(DeleteSparePartOperation)
final deleteSparePartOperationProvider =
    AutoDisposeAsyncNotifierProvider<DeleteSparePartOperation, bool?>.internal(
      DeleteSparePartOperation.new,
      name: r'deleteSparePartOperationProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$deleteSparePartOperationHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$DeleteSparePartOperation = AutoDisposeAsyncNotifier<bool?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../core/theme/app_colors.dart';

import '../providers/spare_parts_providers.dart';
import '../widgets/empty_history_widget.dart';
import '../widgets/error_widget.dart';
import '../widgets/history_list_widget.dart';

/// Screen that displays the replacement history for a spare part
class EnhancedReplacementHistoryScreen extends ConsumerStatefulWidget {
  final int sparePartId;

  const EnhancedReplacementHistoryScreen({
    super.key,
    required this.sparePartId,
  });

  @override
  ConsumerState<EnhancedReplacementHistoryScreen> createState() =>
      _EnhancedReplacementHistoryScreenState();
}

class _EnhancedReplacementHistoryScreenState
    extends ConsumerState<EnhancedReplacementHistoryScreen> {
  @override
  Widget build(BuildContext context) {
    final historyAsync = ref.watch(
      enhancedReplacementHistoryProvider(widget.sparePartId),
    );

    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppColors.primary,
        title: const Text('Replacement History'),
        // Removed sync button for consistency with other screens
      ),
      body: historyAsync.when(
        data: (historyEntries) {
          if (historyEntries.isEmpty) {
            return EmptyHistoryWidget(sparePartId: widget.sparePartId);
          }
          return HistoryListWidget(
            historyEntries: historyEntries,
            sparePartId: widget.sparePartId,
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stackTrace) => HistoryErrorWidget(
          error: error,
          stackTrace: stackTrace,
          sparePartId: widget.sparePartId,
        ),
      ),
    );
  }
}

import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../repositories/spare_parts_repository.dart';

class ReplaceEnhancedSparePart {
  final EnhancedSparePartsRepository repository;

  ReplaceEnhancedSparePart(this.repository);

  Future<Either<Failure, ReplacementResult>> execute({
    required int sparePartId,
    required DateTime replacementDate,
    required double newPartPrice,
    required int currentMileage,
    String? newPartName,
    String? newPartType,
    int? newMileageLimit,
    String replacementReason = 'Regular maintenance',
    String notes = '',
  }) async {
    try {
      // 1. Get the spare part to be replaced
      final sparePartResult = await repository.getSparePartById(sparePartId);

      return sparePartResult.fold(
        (failure) => Left(failure),
        (sparePart) async {
          // 3. Replace the spare part with the provided mileage
          return await repository.replaceSparePart(
            oldPart: sparePart,
            replacementDate: replacementDate,
            currentMileage: currentMileage,
            newPartPrice: newPartPrice,
            newPartName: newPartName,
            newPartType: newPartType,
            newMileageLimit: newMileageLimit,
            replacementReason: replacementReason,
            notes: notes,
          );
        },
      );
    } catch (e) {
      return Left(Failure.businessLogic(message: 'Error replacing spare part: $e'));
    }
  }
}

import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/spare_part.dart';
import '../repositories/spare_parts_repository.dart';

class AddEnhancedSparePart {
  final EnhancedSparePartsRepository repository;

  AddEnhancedSparePart(this.repository);

  Future<Either<Failure, EnhancedSparePart>> execute({
    required String partName,
    required String partType,
    required double price,
    required int mileageLimit,
    required DateTime installationDate,
    int? initialMileage,
    String notes = '',
  }) async {
    try {
      // If initial mileage is not provided, get the highest mileage from income
      int actualInitialMileage = initialMileage ?? 0;
      if (initialMileage == null) {
        final mileageResult = await repository.getHighestMileage();
        actualInitialMileage = mileageResult.fold(
          (failure) => 0, // Default to 0 if there's an error
          (mileage) => mileage,
        );
      }

      // Create the spare part entity with computed values
      final sparePart = EnhancedSparePart.withComputedValues(
        partName: partName,
        partType: partType,
        price: price,
        mileageLimit: mileageLimit,
        initialMileage: actualInitialMileage,
        installationDate: installationDate,
        currentMileage: actualInitialMileage, // Initially, current = initial
        warningStatus: false,
        notes: notes,
        replacementCount: 0, // New part, no replacements yet
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Save the spare part
      return await repository.saveSparePart(sparePart);
    } catch (e) {
      return Left(Failure.businessLogic(message: 'Error adding spare part: $e'));
    }
  }
}

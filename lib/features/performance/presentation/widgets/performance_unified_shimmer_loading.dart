import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

/// Widget that displays a unified shimmer loading effect for the entire performance screen
class PerformanceUnifiedShimmerLoading extends StatelessWidget {
  const PerformanceUnifiedShimmerLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return SliverFillRemaining(
      child: Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header section shimmer
              _buildHeaderSectionShimmer(context),

              // Performance list shimmer
              _buildPerformanceListShimmer(context),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds the shimmer effect for the header section
  Widget _buildHeaderSectionShimmer(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 8.0),
      child: <PERSON>um<PERSON>(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header title
          Container(
            width: 120,
            height: 20,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(height: 8),

          // Header subtitle
          Container(
            width: 200,
            height: 14,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the shimmer effect for the performance list
  Widget _buildPerformanceListShimmer(BuildContext context) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: 5,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 16.0,
            vertical: 6.0,
          ), // Reduced vertical padding
          child: Container(
            // Use constraints instead of fixed height to prevent overflow
            constraints: const BoxConstraints(
              minHeight: 150,
            ), // Further reduced height
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(12.0), // Reduced padding
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min, // Use minimum vertical space
                children: [
                  // Header row
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        width: 80,
                        height: 16,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      Container(
                        width: 60,
                        height: 16,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8), // Reduced spacing
                  // Performance cards
                  Row(
                    children: [
                      Expanded(child: _buildPerformanceCard()),
                      const SizedBox(width: 12),
                      Expanded(child: _buildPerformanceCard()),
                    ],
                  ),

                  const SizedBox(height: 8), // Reduced spacing
                  Container(
                    width: double.infinity,
                    height: 1,
                    color: Colors.white,
                  ),
                  const SizedBox(height: 8), // Reduced spacing
                  // Metrics row
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildMetricItem(),
                      _buildMetricItem(),
                      _buildMetricItem(),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// Builds a simplified shimmer effect for a performance card
  Widget _buildPerformanceCard() {
    return Container(
      height: 60, // Reduced height
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(8.0), // Reduced padding
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min, // Use minimum vertical space
          children: [
            // Card title
            Container(
              width: 80,
              height: 12,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
            const SizedBox(height: 8), // Reduced spacing
            // Progress bar
            Container(
              width: double.infinity,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds a simplified shimmer effect for a metric item
  Widget _buildMetricItem() {
    return Container(
      width: 40,
      height: 20,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
      ),
    );
  }
}

/// Widget that displays a shimmer loading effect for the date range selector
class PerformanceDateRangeSelectorShimmer extends StatelessWidget {
  const PerformanceDateRangeSelectorShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.white.withAlpha(77), // 0.3 * 255 = 77
      highlightColor: Colors.white.withAlpha(128), // 0.5 * 255 = 128
      child: Container(
        height: 36,
        decoration: BoxDecoration(
          color: Colors.white.withAlpha(77), // 0.3 * 255 = 77
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}

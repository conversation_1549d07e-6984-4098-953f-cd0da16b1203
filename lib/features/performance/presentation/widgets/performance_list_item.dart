import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/date_helper.dart';
import '../../../orders/presentation/providers/order_providers.dart';
import '../../domain/entities/performance.dart';

class PerformanceListItem extends ConsumerWidget {
  final Performance performance;
  final Function(Performance) onTap;
  final Function(Performance, int) onLongPress;

  const PerformanceListItem({
    super.key,
    required this.performance,
    required this.onTap,
    required this.onLongPress,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return FutureBuilder<Map<String, int>>(
      future: _getOrderCounts(ref, performance),
      builder: (context, snapshot) {
        final sumCompletedOrders = snapshot.data?['sumCompleted'] ?? 0;

        // Find the date range for progress comparison (14 days not including the current date)
        final endDate = performance.date.subtract(
          const Duration(days: 1),
        ); // Exclude current date
        final startDate = endDate.subtract(
          const Duration(days: 13),
        ); // 14 days total

        // Determine color based on performance metrics
        final avgPerformance =
            (performance.bidPerformance + performance.tripPerformance) / 2;
        Color performanceColor;
        if (avgPerformance >= 90) {
          performanceColor = Colors.green;
        } else if (avgPerformance >= 75) {
          performanceColor = Colors.orange;
        } else {
          performanceColor = Colors.red;
        }

        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: BorderSide(color: performanceColor.withAlpha(30), width: 1),
          ),
          child: InkWell(
            borderRadius: BorderRadius.circular(12),
            onTap: () => onTap(performance),
            onLongPress: () => onLongPress(performance, sumCompletedOrders),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          const Icon(
                            Icons.calendar_today,
                            color: AppColors.textSecondary,
                            size: 16,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            DateHelper.formatForDisplay(performance.date),
                            style: Theme.of(context).textTheme.titleSmall
                                ?.copyWith(fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                      Row(
                        children: [
                          const Icon(
                            Icons.check_circle_outline,
                            color: AppColors.primary,
                            size: 14,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '$sumCompletedOrders orders',
                            style: Theme.of(context).textTheme.titleSmall
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.primary,
                                ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  const Divider(height: 1),
                  const SizedBox(height: 12),

                  // Calculated metrics in summary style
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Calculated Metrics',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: Colors.grey[700],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildSimpleSummaryMetricItem(
                            context,
                            '${performance.avgCompleted?.toInt() ?? 0}',
                            'Avg Orders /Day',
                          ),
                          _buildSimpleSummaryMetricItem(
                            context,
                            '${(performance.avgOnline ?? 0).toInt()}',
                            'Avg Hours /Day',
                          ),
                          _buildSimpleSummaryMetricItem(
                            context,
                            '${(performance.retention ?? 0).toInt()}',
                            'Retention (min)',
                          ),
                        ],
                      ),
                    ],
                  ),

                  const SizedBox(height: 8),
                  Center(
                    child: Text(
                      'Progress from ${DateHelper.formatForDisplay(startDate)} to ${DateHelper.formatForDisplay(endDate)}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textSecondary,
                        fontSize: 11,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSimpleSummaryMetricItem(
    BuildContext context,
    String value,
    String label,
  ) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
        decoration: BoxDecoration(
          color: Colors.grey.withAlpha(15),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Text(
              value,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.primary,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
                fontSize: 11,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Future<Map<String, int>> _getOrderCounts(
    WidgetRef ref,
    Performance performance,
  ) async {
    final orderRepository = ref.read(orderRepositoryProvider);
    final result = <String, int>{};

    // Get sum completed for last 14 days (excluding current day)
    final sumCompletedResult = await orderRepository
        .getTotalCompletedOrdersForLast14Days(performance.date);
    result['sumCompleted'] = sumCompletedResult.fold(
      (failure) => 0,
      (total) => total,
    );

    return result;
  }
}

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/date_helper.dart';
import '../../../orders/presentation/providers/order_providers.dart';
import '../../domain/entities/performance.dart';

class PerformanceDetailsSheet extends ConsumerWidget {
  final Performance performance;

  const PerformanceDetailsSheet({super.key, required this.performance});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Find the date range for progress comparison (14 days not including the current date)
    final endDate = performance.date.subtract(
      const Duration(days: 1),
    ); // Exclude current date
    final startDate = endDate.subtract(
      const Duration(days: 13),
    ); // 14 days total

    // Determine color based on performance metrics
    final avgPerformance =
        (performance.bidPerformance + performance.tripPerformance) / 2;
    Color performanceColor;
    if (avgPerformance >= 90) {
      performanceColor = Colors.green;
    } else if (avgPerformance >= 75) {
      performanceColor = Colors.orange;
    } else {
      performanceColor = Colors.red;
    }

    return DraggableScrollableSheet(
      initialChildSize: 0.65,
      minChildSize: 0.3,
      maxChildSize: 0.95,
      expand: false,
      builder: (context, scrollController) {
        return FutureBuilder<Map<String, int>>(
          future: _getOrderCounts(ref, performance),
          builder: (context, snapshot) {
            final sumCompletedOrders = snapshot.data?['sumCompleted'] ?? 0;

            return Container(
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(16),
                ),
              ),
              child: Column(
                children: [
                  // Drag handle
                  Container(
                    margin: const EdgeInsets.only(top: 8, bottom: 4),
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.grey.withAlpha(80),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),

                  Expanded(
                    child: SingleChildScrollView(
                      controller: scrollController,
                      physics: const BouncingScrollPhysics(),
                      child: Padding(
                        padding: const EdgeInsets.fromLTRB(
                          16.0,
                          8.0,
                          16.0,
                          24.0,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Header with date
                            Padding(
                              padding: const EdgeInsets.only(bottom: 16.0),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Row(
                                    children: [
                                      const Icon(
                                        Icons.insights,
                                        color: AppColors.primary,
                                        size: 20,
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        'Performance Details',
                                        style: Theme.of(context)
                                            .textTheme
                                            .titleLarge
                                            ?.copyWith(
                                              fontWeight: FontWeight.bold,
                                            ),
                                      ),
                                    ],
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.close, size: 20),
                                    onPressed: () => Navigator.pop(context),
                                    padding: EdgeInsets.zero,
                                    constraints: const BoxConstraints(
                                      minWidth: 32,
                                      minHeight: 32,
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            // Date and summary
                            Card(
                              elevation: 0,
                              color: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                                side: BorderSide(
                                  color: performanceColor.withAlpha(30),
                                  width: 1,
                                ),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        const Icon(
                                          Icons.calendar_today,
                                          color: AppColors.textSecondary,
                                          size: 16,
                                        ),
                                        const SizedBox(width: 8),
                                        Text(
                                          DateHelper.formatForDisplay(
                                            performance.date,
                                          ),
                                          style: Theme.of(context)
                                              .textTheme
                                              .titleMedium
                                              ?.copyWith(
                                                fontWeight: FontWeight.bold,
                                              ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 12),
                                    Row(
                                      children: [
                                        const Icon(
                                          Icons.check_circle_outline,
                                          color: AppColors.primary,
                                          size: 16,
                                        ),
                                        const SizedBox(width: 8),
                                        Text(
                                          '$sumCompletedOrders orders',
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodyMedium
                                              ?.copyWith(
                                                fontWeight: FontWeight.w500,
                                                color: AppColors.primary,
                                              ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      'Progress from ${DateHelper.formatForDisplay(startDate)} to ${DateHelper.formatForDisplay(endDate)}',
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodySmall
                                          ?.copyWith(
                                            color: AppColors.textSecondary,
                                            fontSize: 11,
                                          ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            const SizedBox(height: 16),

                            // Performance Metrics
                            _buildDetailSection(
                              context,
                              'Performance Metrics',
                              Icons.trending_up,
                              Colors.purple,
                              [
                                _buildDetailItemWithProgress(
                                  context,
                                  'Bid Performance',
                                  '${(performance.bidPerformance).toInt()}%',
                                  performance.bidPerformance / 100,
                                  Colors.purple,
                                ),
                                _buildDetailItemWithProgress(
                                  context,
                                  'Trip Performance',
                                  '${(performance.tripPerformance).toInt()}%',
                                  performance.tripPerformance / 100,
                                  Colors.teal,
                                ),
                              ],
                            ),

                            const SizedBox(height: 20),

                            // Activity Metrics
                            _buildDetailSection(
                              context,
                              'Activity Metrics',
                              Icons.access_time,
                              AppColors.primary,
                              [
                                _buildDetailItemWithIcon(
                                  context,
                                  'Active Days',
                                  '${performance.activeDays}',
                                  Icons.calendar_today,
                                  AppColors.primary,
                                ),
                                _buildDetailItemWithIcon(
                                  context,
                                  'Online Hours',
                                  '${performance.onlineHours.toInt()}',
                                  Icons.timer,
                                  AppColors.primary,
                                ),
                              ],
                            ),

                            const SizedBox(height: 20),

                            // Calculated Metrics
                            _buildDetailSection(
                              context,
                              'Calculated Metrics',
                              Icons.calculate,
                              AppColors.primary,
                              [
                                _buildDetailItemWithIcon(
                                  context,
                                  'Avg Orders /Day',
                                  '${performance.avgCompleted?.toInt() ?? 0}',
                                  Icons.shopping_cart,
                                  AppColors.primary,
                                ),
                                _buildDetailItemWithIcon(
                                  context,
                                  'Avg Hours /Day',
                                  '${(performance.avgOnline ?? 0).toInt()}',
                                  Icons.schedule,
                                  AppColors.primary,
                                ),
                                _buildDetailItemWithIcon(
                                  context,
                                  'Retention (min/order)',
                                  '${(performance.retention ?? 0).toInt()}',
                                  Icons.timelapse,
                                  AppColors.primary,
                                ),
                              ],
                            ),

                            const SizedBox(height: 24),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildDetailSection(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    List<Widget> children,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 18),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: Theme.of(
                    context,
                  ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Padding(
              padding: EdgeInsets.symmetric(vertical: 8),
              child: Divider(height: 1),
            ),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildDetailItemWithProgress(
    BuildContext context,
    String label,
    String value,
    double progress,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Icon(Icons.trending_up, color: color, size: 16),
                  const SizedBox(width: 8),
                  Text(
                    label,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              Text(
                value,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: color.withAlpha(26),
            valueColor: AlwaysStoppedAnimation<Color>(color),
            minHeight: 6,
          ),
        ],
      ),
    );
  }

  Widget _buildDetailItemWithIcon(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              label,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
            ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Future<Map<String, int>> _getOrderCounts(
    WidgetRef ref,
    Performance performance,
  ) async {
    final orderRepository = ref.read(orderRepositoryProvider);
    final result = <String, int>{};

    // Get sum completed for last 14 days (excluding current day)
    final sumCompletedResult = await orderRepository
        .getTotalCompletedOrdersForLast14Days(performance.date);
    result['sumCompleted'] = sumCompletedResult.fold(
      (failure) => 0,
      (total) => total,
    );

    return result;
  }
}

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

import '../../../../core/providers/global_date_range_provider.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/date_helper.dart';
import '../../../../core/utils/snackbar_utils.dart';
import '../../../../core/widgets/date_range_selector_field.dart';
import '../../../../core/widgets/item_actions_bottom_sheet.dart';
import '../../domain/entities/performance.dart';
import '../constants/performance_constants.dart';
import '../providers/performance_providers.dart';
import '../screens/performance_form_screen.dart';
import '../widgets/empty_performance_view.dart';
import '../widgets/performance_details_sheet.dart';
import '../widgets/performance_list_item.dart';
import '../widgets/performance_unified_shimmer_loading.dart';

class PerformanceScreen extends ConsumerStatefulWidget {
  const PerformanceScreen({super.key});

  @override
  ConsumerState<PerformanceScreen> createState() => _PerformanceScreenState();
}

class _PerformanceScreenState extends ConsumerState<PerformanceScreen> {
  @override
  Widget build(BuildContext context) {
    final performanceListAsync = ref.watch(performanceListProvider);
    final dateRangeAsync = ref.watch(globalDateRangeProvider);

    // Check if any of the async values are in loading state
    final bool isLoading =
        performanceListAsync.isLoading || dateRangeAsync.isLoading;

    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () async {
          ref.invalidate(performanceListProvider);
        },
        child: CustomScrollView(
          slivers: [
            _buildAppBar(context, ref, dateRangeAsync),
            // If loading, show unified shimmer loading, otherwise show content
            if (isLoading)
              const PerformanceUnifiedShimmerLoading()
            else ...[
              _buildHeaderSection(context),
              performanceListAsync.maybeWhen(
                data: (performanceList) =>
                    _buildPerformanceList(context, ref, performanceList),
                error: (error, stack) => SliverFillRemaining(
                  child: Center(child: Text('Error: $error')),
                ),
                orElse: () =>
                    const SliverToBoxAdapter(child: SizedBox.shrink()),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Builds the app bar with date range selector
  Widget _buildAppBar(
    BuildContext context,
    WidgetRef ref,
    AsyncValue<DateTimeRange> dateRangeAsync,
  ) {
    return SliverAppBar(
      floating: true,
      pinned: true,
      elevation: 0,
      backgroundColor: AppColors.primary,
      title: const Text(PerformanceConstants.screenTitle),
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(60),
        child: Padding(
          padding: const EdgeInsets.fromLTRB(16.0, 4.0, 16.0, 12.0),
          child: dateRangeAsync.when(
            data: (dateRange) =>
                _buildDateRangeSelector(context, ref, dateRange),
            loading: () => const PerformanceDateRangeSelectorShimmer(),
            error: (error, stack) => Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.red.withAlpha(30),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(Icons.error_outline, color: Colors.red, size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Error: $error',
                      style: Theme.of(
                        context,
                      ).textTheme.bodyMedium?.copyWith(color: Colors.red),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.add),
          tooltip: 'Add new performance record',
          onPressed: () => _navigateToFormScreen(context),
        ),
      ],
    );
  }

  /// Builds the header section with title and subtitle
  Widget _buildHeaderSection(BuildContext context) {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              PerformanceConstants.historyTitle,
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: Text(
                PerformanceConstants.historySubtitle,
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Navigate to the form screen to add or edit a performance record
  void _navigateToFormScreen(BuildContext context, {Performance? performance}) {
    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (context) =>
                PerformanceFormScreen(performance: performance),
          ),
        )
        .then((_) {
          // Refresh the list when returning from the form screen
          ref.invalidate(performanceListProvider);
        });
  }

  /// Show the actions bottom sheet for a performance record
  void _showActionsBottomSheet(
    BuildContext context,
    Performance performance,
    int orderCount,
  ) {
    ItemActionsBottomSheet.show(
      context: context,
      title: PerformanceConstants.screenTitle,
      subtitle: 'Date: ${DateHelper.formatForDisplay(performance.date)}',
      onEdit: () => _navigateToFormScreen(context, performance: performance),
      onDelete: () => _showDeleteConfirmation(context, performance),
      itemIcon: Icons.insights,
    );
  }

  /// Show a confirmation dialog before deleting a performance record
  void _showDeleteConfirmation(BuildContext context, Performance performance) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(PerformanceConstants.deleteConfirmationTitle),
        content: Text(
          '${PerformanceConstants.deleteConfirmationMessage} ${DateFormat('dd MMM yyyy').format(performance.date)}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(PerformanceConstants.cancelLabel),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _deletePerformance(performance);
            },
            child: const Text(
              PerformanceConstants.confirmDeleteLabel,
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  /// Delete a performance record
  void _deletePerformance(Performance performance) async {
    if (performance.id == null) return;

    // Show loading indicator
    SnackbarUtils.showLoading(message: 'Deleting...');

    // Use the provider to delete the performance record
    final success = await ref
        .read(performanceOperationsProvider.notifier)
        .deletePerformance(performance.id!);

    if (mounted) {
      if (success) {
        SnackbarUtils.showSuccess(PerformanceConstants.deleteSuccessMessage);

        // Refresh the list
        ref.invalidate(performanceListProvider);
      } else {
        SnackbarUtils.showError(PerformanceConstants.deleteErrorPrefix);
      }
    }
  }

  /// Builds the performance list or empty state
  Widget _buildPerformanceList(
    BuildContext context,
    WidgetRef ref,
    List<Performance> performanceList,
  ) {
    if (performanceList.isEmpty) {
      return EmptyPerformanceView(
        onAddPressed: () => _navigateToFormScreen(context),
      );
    }

    // Sort performance list by date (newest first)
    final sortedList = List<Performance>.from(performanceList)
      ..sort((a, b) => b.date.compareTo(a.date));

    return SliverList(
      delegate: SliverChildBuilderDelegate((context, index) {
        final performance = sortedList[index];
        return PerformanceListItem(
          performance: performance,
          onTap: (performance) => _showPerformanceDetails(context, performance),
          onLongPress: (performance, orderCount) =>
              _showActionsBottomSheet(context, performance, orderCount),
        );
      }, childCount: sortedList.length),
    );
  }

  /// Shows the performance details in a bottom sheet
  void _showPerformanceDetails(BuildContext context, Performance performance) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => PerformanceDetailsSheet(performance: performance),
    );
  }

  /// Builds the date range selector widget
  Widget _buildDateRangeSelector(
    BuildContext context,
    WidgetRef ref,
    DateTimeRange dateRange,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withAlpha(30),
        borderRadius: BorderRadius.circular(8),
      ),
      child: DateRangeSelectorField(
        dateRange: dateRange,
        onDateRangeSelected: (newRange) async {
          await ref
              .read(globalDateRangeProvider.notifier)
              .setDateRange(newRange);
        },
      ),
    );
  }
}

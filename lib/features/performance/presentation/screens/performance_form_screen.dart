import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../core/utils/snackbar_utils.dart';
import '../../../../core/widgets/base_form_screen.dart';
import '../../../../core/widgets/number_input_field.dart';

import '../../domain/entities/performance.dart';
import '../providers/performance_providers.dart';

class PerformanceFormScreen extends BaseFormScreen<Performance> {
  const PerformanceFormScreen({super.key, Performance? performance})
    : super(entity: performance);

  @override
  ConsumerState<PerformanceFormScreen> createState() =>
      _PerformanceFormScreenState();
}

class _PerformanceFormScreenState
    extends BaseFormScreenState<Performance, PerformanceFormScreen> {
  final _bidPerformanceController = TextEditingController();
  final _tripPerformanceController = TextEditingController();
  final _activeDaysController = TextEditingController();
  final _onlineHoursController = TextEditingController();

  @override
  DateTime getInitialDate() {
    return widget.entity?.date ?? DateTime.now();
  }

  @override
  void initializeControllers() {
    if (isEditing) {
      // Populate form with existing data
      if (widget.entity!.bidPerformance != 0.0) {
        _bidPerformanceController.text = widget.entity!.bidPerformance
            .toString();
      }
      if (widget.entity!.tripPerformance != 0.0) {
        _tripPerformanceController.text = widget.entity!.tripPerformance
            .toString();
      }
      if (widget.entity!.activeDays != 0) {
        _activeDaysController.text = widget.entity!.activeDays.toString();
      }
      if (widget.entity!.onlineHours != 0.0) {
        _onlineHoursController.text = widget.entity!.onlineHours.toString();
      }
    }
  }

  @override
  void disposeControllers() {
    _bidPerformanceController.dispose();
    _tripPerformanceController.dispose();
    _activeDaysController.dispose();
    _onlineHoursController.dispose();
  }

  @override
  String getFormTitle() {
    return isEditing ? 'Edit Performance Record' : 'Add Performance Record';
  }

  IconData _getMetricIcon(String label) {
    switch (label) {
      case 'AVG Completed':
        return Icons.check_circle_outline;
      case 'AVG Online':
        return Icons.access_time;
      case 'Retention':
        return Icons.people_outline;
      default:
        return Icons.info_outline;
    }
  }

  @override
  Future<bool> checkDateExists() async {
    final repository = ref.read(performanceRepositoryProvider);
    final dateExistsResult = await repository.checkDateExists(
      selectedDate,
      excludeId: isEditing ? widget.entity!.id : null,
    );

    return dateExistsResult.fold((failure) {
      // If there was an error checking the date, show an error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error checking date: ${failure.toString()}'),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return false; // Assume date doesn't exist if there was an error
    }, (exists) => exists);
  }

  @override
  Future<void> submitForm() async {
    // Parse input values, defaulting to 0 if empty
    final bidPerformance = _bidPerformanceController.text.isEmpty
        ? 0.0
        : double.parse(_bidPerformanceController.text);
    final tripPerformance = _tripPerformanceController.text.isEmpty
        ? 0.0
        : double.parse(_tripPerformanceController.text);
    final activeDays = _activeDaysController.text.isEmpty
        ? 0
        : int.parse(_activeDaysController.text);
    final onlineHours = _onlineHoursController.text.isEmpty
        ? 0.0
        : double.parse(_onlineHoursController.text);

    final performance = Performance(
      id: widget.entity?.id,
      date: selectedDate,
      bidPerformance: bidPerformance,
      tripPerformance: tripPerformance,
      activeDays: activeDays,
      onlineHours: onlineHours,
      // The calculated fields will be set by the repository
      avgCompleted: widget.entity?.avgCompleted,
      avgOnline: widget.entity?.avgOnline,
      retention: widget.entity?.retention,
    );

    bool success;

    if (isEditing) {
      success = await ref
          .read(performanceOperationsProvider.notifier)
          .updatePerformance(performance);
    } else {
      success = await ref
          .read(performanceOperationsProvider.notifier)
          .addPerformance(performance);
    }

    // Show success or error message
    if (mounted) {
      if (success) {
        SnackbarUtils.showSuccess(
          '${isEditing ? 'Updated' : 'Added'} performance record successfully',
        );
      } else {
        SnackbarUtils.showError(
          'Failed to ${isEditing ? 'update' : 'add'} performance record',
        );
      }
    }
  }

  @override
  List<Widget> buildFormFields() {
    // Get calculated values if editing
    final avgCompleted = widget.entity?.avgCompleted ?? 0.0;
    final avgOnline = widget.entity?.avgOnline ?? 0.0;
    final retention = widget.entity?.retention ?? 0.0;

    return [
      buildSectionTitle('Performance Metrics', icon: Icons.insights),
      NumberInputField(
        label: 'Bid Performance',
        controller: _bidPerformanceController,
        isPercentage: true,
        isRequired: true,
        isDecimal: true,
        maxDecimalPlaces: 2,
      ),
      NumberInputField(
        label: 'Trip Performance',
        controller: _tripPerformanceController,
        isPercentage: true,
        isRequired: true,
        isDecimal: true,
        maxDecimalPlaces: 2,
      ),
      NumberInputField(
        label: 'Active Days',
        controller: _activeDaysController,
        isRequired: true,
        isDecimal: false,
      ),
      NumberInputField(
        label: 'Online Hours',
        controller: _onlineHoursController,
        isRequired: true,
        isDecimal: true,
        maxDecimalPlaces: 1,
      ),

      if (isEditing) ...[
        const SizedBox(height: 24),
        buildSectionTitle('Calculated Metrics', icon: Icons.auto_graph),
        buildReadOnlyField(
          label: 'AVG Completed',
          value: avgCompleted.toStringAsFixed(2),
          icon: _getMetricIcon('AVG Completed'),
        ),
        buildReadOnlyField(
          label: 'AVG Online',
          value: avgOnline.toStringAsFixed(2),
          icon: _getMetricIcon('AVG Online'),
        ),
        buildReadOnlyField(
          label: 'Retention',
          value: retention.toStringAsFixed(2),
          icon: _getMetricIcon('Retention'),
        ),
      ],
    ];
  }
}

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'performance_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$get14DayMetricsHash() => r'fc99e854674d02d597336df7fd271469da21761a';

/// See also [get14DayMetrics].
@ProviderFor(get14DayMetrics)
final get14DayMetricsProvider = AutoDisposeProvider<Get14DayMetrics>.internal(
  get14DayMetrics,
  name: r'get14DayMetricsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$get14DayMetricsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef Get14DayMetricsRef = AutoDisposeProviderRef<Get14DayMetrics>;
String _$calculateRetentionHash() =>
    r'62654d7182925bffcbdcada7b4b5b40e40d6a77e';

/// See also [calculateRetention].
@ProviderFor(calculateRetention)
final calculateRetentionProvider =
    AutoDisposeProvider<CalculateRetention>.internal(
      calculateRetention,
      name: r'calculateRetentionProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$calculateRetentionHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CalculateRetentionRef = AutoDisposeProviderRef<CalculateRetention>;
String _$calculatePerformanceMetricsHash() =>
    r'4908a80d33af42e5fb6b27951036c7d5df502b59';

/// See also [calculatePerformanceMetrics].
@ProviderFor(calculatePerformanceMetrics)
final calculatePerformanceMetricsProvider =
    AutoDisposeProvider<CalculatePerformanceMetrics>.internal(
      calculatePerformanceMetrics,
      name: r'calculatePerformanceMetricsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$calculatePerformanceMetricsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CalculatePerformanceMetricsRef =
    AutoDisposeProviderRef<CalculatePerformanceMetrics>;
String _$performanceRepositoryHash() =>
    r'4e465cba9c2a1ef9ce1ceee3e00aa05e4f661091';

/// See also [performanceRepository].
@ProviderFor(performanceRepository)
final performanceRepositoryProvider =
    AutoDisposeProvider<PerformanceRepository>.internal(
      performanceRepository,
      name: r'performanceRepositoryProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$performanceRepositoryHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef PerformanceRepositoryRef =
    AutoDisposeProviderRef<PerformanceRepository>;
String _$performanceListHash() => r'5356865a68cc8494567f6740e39081419e8a63f5';

/// See also [performanceList].
@ProviderFor(performanceList)
final performanceListProvider =
    AutoDisposeFutureProvider<List<Performance>>.internal(
      performanceList,
      name: r'performanceListProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$performanceListHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef PerformanceListRef = AutoDisposeFutureProviderRef<List<Performance>>;
String _$retentionHash() => r'1af43524d0384d49ba3a6e831694f0baedef3dcf';

/// See also [retention].
@ProviderFor(retention)
final retentionProvider = AutoDisposeFutureProvider<double>.internal(
  retention,
  name: r'retentionProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$retentionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef RetentionRef = AutoDisposeFutureProviderRef<double>;
String _$performanceOperationsHash() =>
    r'0caa8796038049a509b126bf887de5cf26dc560e';

/// See also [PerformanceOperations].
@ProviderFor(PerformanceOperations)
final performanceOperationsProvider =
    AutoDisposeAsyncNotifierProvider<PerformanceOperations, void>.internal(
      PerformanceOperations.new,
      name: r'performanceOperationsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$performanceOperationsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$PerformanceOperations = AutoDisposeAsyncNotifier<void>;
String _$performanceMetricsDataHash() =>
    r'49ce7d514a9286e2fc27552df930fff87e70c9d7';

/// See also [PerformanceMetricsData].
@ProviderFor(PerformanceMetricsData)
final performanceMetricsDataProvider =
    AutoDisposeAsyncNotifierProvider<
      PerformanceMetricsData,
      PerformanceMetrics
    >.internal(
      PerformanceMetricsData.new,
      name: r'performanceMetricsDataProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$performanceMetricsDataHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$PerformanceMetricsData = AutoDisposeAsyncNotifier<PerformanceMetrics>;
String _$onlineHoursHash() => r'465dbaae1ae5e824f569de1bc6d244dba8a5b038';

/// See also [OnlineHours].
@ProviderFor(OnlineHours)
final onlineHoursProvider =
    AutoDisposeNotifierProvider<OnlineHours, double>.internal(
      OnlineHours.new,
      name: r'onlineHoursProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$onlineHoursHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$OnlineHours = AutoDisposeNotifier<double>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package

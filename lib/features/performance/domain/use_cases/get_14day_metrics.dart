import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../orders/domain/repositories/order_repository.dart';
import '../repositories/performance_repository.dart';



/// Use case to calculate performance metrics based on the last 14 days of order data
class Get14DayMetrics {
  final OrderRepository orderRepository;
  final PerformanceRepository performanceRepository;

  Get14DayMetrics(this.orderRepository, this.performanceRepository);

  /// Calculate performance metrics based on order data from the last 14 days
  Future<Either<Failure, PerformanceMetrics>> execute(DateTime endDate) async {
    try {
      // Get orders for the last 14 days
      final ordersResult = await orderRepository.getOrdersForPerformanceCalculation(endDate);

      // Get the most recent performance record
      final performanceResult = await performanceRepository.getPerformanceForDateRange(
        endDate.subtract(const Duration(days: 30)), // Look back 30 days to find the most recent record
        endDate
      );

      // Extract bid and trip performance from the most recent performance record
      final mostRecentPerformance = performanceResult.fold(
        (failure) => null,
        (performances) {
          if (performances.isEmpty) return null;
          // Sort by date descending to get the most recent
          performances.sort((a, b) => b.date.compareTo(a.date));
          return performances.first;
        },
      );

      return ordersResult.fold(
        (failure) => Left(failure),
        (orders) {
          if (orders.isEmpty) {
            return Right(PerformanceMetrics.empty());
          }

          // Get total completed orders
          int totalOrdersCompleted = 0;
          for (final order in orders) {
            totalOrdersCompleted += order.orderCompleted;
          }

          // Use bid and trip performance from the most recent performance record if available
          // Otherwise default to 0.0
          final bidPerformance = mostRecentPerformance?.bidPerformance ?? 0.0;
          final tripPerformance = mostRecentPerformance?.tripPerformance ?? 0.0;

          // Count active days (days with at least one order)
          final Set<DateTime> activeDaysSet = {};
          for (final order in orders) {
            // Only count the date part (not time)
            final orderDate = DateTime(order.date.year, order.date.month, order.date.day);
            activeDaysSet.add(orderDate);
          }
          final activeDays = activeDaysSet.length;

          // Create performance metrics
          // Note: The date range is 14 days not including the current date
          // If endDate is March 25, 2025, we want orders from March 11, 2025 to March 24, 2025
          final adjustedEndDate = endDate.subtract(const Duration(days: 1)); // Exclude current date
          final startDate = adjustedEndDate.subtract(const Duration(days: 13)); // 14 days total

          return Right(
            PerformanceMetrics(
              bidPerformance: bidPerformance,
              tripPerformance: tripPerformance,
              activeDays: activeDays,
              totalOrdersCompleted: totalOrdersCompleted,
              recordCount: orders.length,
              dateRange: DateRange(
                start: startDate,
                end: adjustedEndDate,
              ),
            ),
          );
        },
      );
    } catch (e) {
      return Left(Failure.unexpected(message: 'Error calculating 14-day metrics: $e'));
    }
  }
}

/// Data class to hold performance metrics calculated from order data
class PerformanceMetrics {
  final double bidPerformance;
  final double tripPerformance;
  final int activeDays;
  final int totalOrdersCompleted;
  final int recordCount;
  final DateRange dateRange;

  PerformanceMetrics({
    required this.bidPerformance,
    required this.tripPerformance,
    required this.activeDays,
    required this.totalOrdersCompleted,
    required this.recordCount,
    required this.dateRange,
  });

  /// Calculate average completed orders per active day
  double get avgCompletedPerDay {
    if (activeDays == 0) return 0;
    return totalOrdersCompleted / activeDays;
  }

  factory PerformanceMetrics.empty() {
    final now = DateTime.now();
    return PerformanceMetrics(
      bidPerformance: 0,
      tripPerformance: 0,
      activeDays: 0,
      totalOrdersCompleted: 0,
      recordCount: 0,
      dateRange: DateRange(
        start: now.subtract(const Duration(days: 13)),
        end: now,
      ),
    );
  }
}

/// Simple class to represent a date range
class DateRange {
  final DateTime start;
  final DateTime end;

  DateRange({
    required this.start,
    required this.end,
  });
}

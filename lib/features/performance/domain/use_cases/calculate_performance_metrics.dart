import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../../../orders/domain/repositories/order_repository.dart';
import '../repositories/performance_repository.dart';

/// Use case to calculate all performance metrics for a given date range
///
/// This use case encapsulates the business logic for calculating performance metrics
/// across a date range, removing this logic from the UI layer.
class CalculatePerformanceMetrics {
  final PerformanceRepository _performanceRepository;
  final OrderRepository _orderRepository;

  /// Constructor
  CalculatePerformanceMetrics({
    required PerformanceRepository performanceRepository,
    required OrderRepository orderRepository,
  })  : _performanceRepository = performanceRepository,
        _orderRepository = orderRepository;

  /// Calculate performance metrics for a date range
  ///
  /// Returns Either a Failure or a map containing the calculated metrics:
  /// - avgBidPerformance: Average bid performance across the date range
  /// - avgTripPerformance: Average trip performance across the date range
  /// - totalActiveDays: Total number of active days in the date range
  /// - avgOnlineHours: Average online hours per day
  /// - avgCompletedOrders: Average completed orders per day
  /// - retention: Average retention rate
  Future<Either<Failure, Map<String, dynamic>>> execute(
      DateTime startDate, DateTime endDate) async {
    try {
      // Get performance records for the date range
      final performanceResult = await _performanceRepository
          .getPerformanceForDateRange(startDate, endDate);

      return performanceResult.fold(
        (failure) => Left(failure),
        (performanceList) async {
          if (performanceList.isEmpty) {
            return const Right({
              'avgBidPerformance': 0.0,
              'avgTripPerformance': 0.0,
              'totalActiveDays': 0,
              'avgOnlineHours': 0.0,
              'avgCompletedOrders': 0.0,
              'retention': 0.0,
            });
          }

          // Calculate average bid and trip performance
          double sumBidPerformance = 0.0;
          double sumTripPerformance = 0.0;
          int totalActiveDays = 0;
          double totalOnlineHours = 0.0;

          for (final performance in performanceList) {
            sumBidPerformance += performance.bidPerformance;
            sumTripPerformance += performance.tripPerformance;
            totalActiveDays += performance.activeDays;
            totalOnlineHours += performance.onlineHours;
          }

          final avgBidPerformance = sumBidPerformance / performanceList.length;
          final avgTripPerformance = sumTripPerformance / performanceList.length;
          final avgOnlineHours = totalOnlineHours / performanceList.length;

          // Get total completed orders for the date range
          final ordersResult = await _orderRepository.getOrdersForDateRange(
              startDate, endDate);

          int totalCompletedOrders = 0;
          ordersResult.fold(
            (failure) => {},
            (orders) {
              for (final order in orders) {
                totalCompletedOrders += order.orderCompleted;
              }
            },
          );

          // Calculate average completed orders per day
          final avgCompletedOrders = totalActiveDays > 0
              ? totalCompletedOrders / totalActiveDays
              : 0.0;

          // Calculate retention
          final retention = avgCompletedOrders > 0
              ? (avgOnlineHours * 60) / avgCompletedOrders
              : 0.0;

          return Right({
            'avgBidPerformance': avgBidPerformance,
            'avgTripPerformance': avgTripPerformance,
            'totalActiveDays': totalActiveDays,
            'avgOnlineHours': avgOnlineHours,
            'avgCompletedOrders': avgCompletedOrders,
            'retention': retention,
          });
        },
      );
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }
}

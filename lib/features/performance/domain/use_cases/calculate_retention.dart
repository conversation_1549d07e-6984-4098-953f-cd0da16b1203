import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/services/calculation/calculation_service.dart';
import '../../../../core/services/calculation/strategies/performance_calculation_strategy.dart';
import '../entities/performance.dart';

/// Use case to calculate retention and other derived performance metrics
///
/// This class uses the generic CalculationService to handle the calculation
/// logic in a consistent way across the application.
class CalculateRetention {
  final CalculationService? _calculationService;

  /// Constructor that optionally takes a CalculationService
  /// If no service is provided, the use case will fall back to the legacy implementation
  CalculateRetention([this._calculationService]);

  /// Calculate all derived values for a performance entity
  /// - avgCompleted
  /// - avgOnline
  /// - retention
  Either<Failure, Performance> execute(
    Performance performance, {
    int? totalCompletedOrders,
  }) {
    // If we have a calculation service, use it
    if (_calculationService != null) {
      return _calculationService.calculate<Performance>(
        performance,
        params: totalCompletedOrders != null
            ? {'totalCompletedOrders': totalCompletedOrders}
            : null,
      );
    }

    // Otherwise, use the strategy directly for backward compatibility
    final strategy = PerformanceCalculationStrategy();
    return strategy.execute(
      performance,
      params: totalCompletedOrders != null
          ? {'totalCompletedOrders': totalCompletedOrders}
          : null,
    );
  }
}

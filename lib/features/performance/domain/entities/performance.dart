import 'package:freezed_annotation/freezed_annotation.dart';

part 'performance.freezed.dart';
part 'performance.g.dart';

@freezed
sealed class Performance with _$Performance {
  const Performance._();

  const factory Performance({
    int? id,
    required DateTime date,
    required double bidPerformance,
    required double tripPerformance,
    required int activeDays,
    required double onlineHours,
    double? avgCompleted,
    double? avgOnline,
    double? retention,
  }) = _Performance;

  factory Performance.fromJson(Map<String, dynamic> json) =>
      _$PerformanceFromJson(json);
}

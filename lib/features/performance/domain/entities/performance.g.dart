// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'performance.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Performance _$PerformanceFromJson(Map<String, dynamic> json) => _Performance(
  id: (json['id'] as num?)?.toInt(),
  date: DateTime.parse(json['date'] as String),
  bidPerformance: (json['bidPerformance'] as num).toDouble(),
  tripPerformance: (json['tripPerformance'] as num).toDouble(),
  activeDays: (json['activeDays'] as num).toInt(),
  onlineHours: (json['onlineHours'] as num).toDouble(),
  avgCompleted: (json['avgCompleted'] as num?)?.toDouble(),
  avgOnline: (json['avgOnline'] as num?)?.toDouble(),
  retention: (json['retention'] as num?)?.toDouble(),
);

Map<String, dynamic> _$PerformanceToJson(_Performance instance) =>
    <String, dynamic>{
      'id': instance.id,
      'date': instance.date.toIso8601String(),
      'bidPerformance': instance.bidPerformance,
      'tripPerformance': instance.tripPerformance,
      'activeDays': instance.activeDays,
      'onlineHours': instance.onlineHours,
      'avgCompleted': instance.avgCompleted,
      'avgOnline': instance.avgOnline,
      'retention': instance.retention,
    };

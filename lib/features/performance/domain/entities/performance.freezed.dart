// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'performance.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Performance {

 int? get id; DateTime get date; double get bidPerformance; double get tripPerformance; int get activeDays; double get onlineHours; double? get avgCompleted; double? get avgOnline; double? get retention;
/// Create a copy of Performance
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PerformanceCopyWith<Performance> get copyWith => _$PerformanceCopyWithImpl<Performance>(this as Performance, _$identity);

  /// Serializes this Performance to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Performance&&(identical(other.id, id) || other.id == id)&&(identical(other.date, date) || other.date == date)&&(identical(other.bidPerformance, bidPerformance) || other.bidPerformance == bidPerformance)&&(identical(other.tripPerformance, tripPerformance) || other.tripPerformance == tripPerformance)&&(identical(other.activeDays, activeDays) || other.activeDays == activeDays)&&(identical(other.onlineHours, onlineHours) || other.onlineHours == onlineHours)&&(identical(other.avgCompleted, avgCompleted) || other.avgCompleted == avgCompleted)&&(identical(other.avgOnline, avgOnline) || other.avgOnline == avgOnline)&&(identical(other.retention, retention) || other.retention == retention));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,date,bidPerformance,tripPerformance,activeDays,onlineHours,avgCompleted,avgOnline,retention);

@override
String toString() {
  return 'Performance(id: $id, date: $date, bidPerformance: $bidPerformance, tripPerformance: $tripPerformance, activeDays: $activeDays, onlineHours: $onlineHours, avgCompleted: $avgCompleted, avgOnline: $avgOnline, retention: $retention)';
}


}

/// @nodoc
abstract mixin class $PerformanceCopyWith<$Res>  {
  factory $PerformanceCopyWith(Performance value, $Res Function(Performance) _then) = _$PerformanceCopyWithImpl;
@useResult
$Res call({
 int? id, DateTime date, double bidPerformance, double tripPerformance, int activeDays, double onlineHours, double? avgCompleted, double? avgOnline, double? retention
});




}
/// @nodoc
class _$PerformanceCopyWithImpl<$Res>
    implements $PerformanceCopyWith<$Res> {
  _$PerformanceCopyWithImpl(this._self, this._then);

  final Performance _self;
  final $Res Function(Performance) _then;

/// Create a copy of Performance
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? date = null,Object? bidPerformance = null,Object? tripPerformance = null,Object? activeDays = null,Object? onlineHours = null,Object? avgCompleted = freezed,Object? avgOnline = freezed,Object? retention = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,date: null == date ? _self.date : date // ignore: cast_nullable_to_non_nullable
as DateTime,bidPerformance: null == bidPerformance ? _self.bidPerformance : bidPerformance // ignore: cast_nullable_to_non_nullable
as double,tripPerformance: null == tripPerformance ? _self.tripPerformance : tripPerformance // ignore: cast_nullable_to_non_nullable
as double,activeDays: null == activeDays ? _self.activeDays : activeDays // ignore: cast_nullable_to_non_nullable
as int,onlineHours: null == onlineHours ? _self.onlineHours : onlineHours // ignore: cast_nullable_to_non_nullable
as double,avgCompleted: freezed == avgCompleted ? _self.avgCompleted : avgCompleted // ignore: cast_nullable_to_non_nullable
as double?,avgOnline: freezed == avgOnline ? _self.avgOnline : avgOnline // ignore: cast_nullable_to_non_nullable
as double?,retention: freezed == retention ? _self.retention : retention // ignore: cast_nullable_to_non_nullable
as double?,
  ));
}

}


/// Adds pattern-matching-related methods to [Performance].
extension PerformancePatterns on Performance {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Performance value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Performance() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Performance value)  $default,){
final _that = this;
switch (_that) {
case _Performance():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Performance value)?  $default,){
final _that = this;
switch (_that) {
case _Performance() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int? id,  DateTime date,  double bidPerformance,  double tripPerformance,  int activeDays,  double onlineHours,  double? avgCompleted,  double? avgOnline,  double? retention)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Performance() when $default != null:
return $default(_that.id,_that.date,_that.bidPerformance,_that.tripPerformance,_that.activeDays,_that.onlineHours,_that.avgCompleted,_that.avgOnline,_that.retention);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int? id,  DateTime date,  double bidPerformance,  double tripPerformance,  int activeDays,  double onlineHours,  double? avgCompleted,  double? avgOnline,  double? retention)  $default,) {final _that = this;
switch (_that) {
case _Performance():
return $default(_that.id,_that.date,_that.bidPerformance,_that.tripPerformance,_that.activeDays,_that.onlineHours,_that.avgCompleted,_that.avgOnline,_that.retention);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int? id,  DateTime date,  double bidPerformance,  double tripPerformance,  int activeDays,  double onlineHours,  double? avgCompleted,  double? avgOnline,  double? retention)?  $default,) {final _that = this;
switch (_that) {
case _Performance() when $default != null:
return $default(_that.id,_that.date,_that.bidPerformance,_that.tripPerformance,_that.activeDays,_that.onlineHours,_that.avgCompleted,_that.avgOnline,_that.retention);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Performance extends Performance {
  const _Performance({this.id, required this.date, required this.bidPerformance, required this.tripPerformance, required this.activeDays, required this.onlineHours, this.avgCompleted, this.avgOnline, this.retention}): super._();
  factory _Performance.fromJson(Map<String, dynamic> json) => _$PerformanceFromJson(json);

@override final  int? id;
@override final  DateTime date;
@override final  double bidPerformance;
@override final  double tripPerformance;
@override final  int activeDays;
@override final  double onlineHours;
@override final  double? avgCompleted;
@override final  double? avgOnline;
@override final  double? retention;

/// Create a copy of Performance
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PerformanceCopyWith<_Performance> get copyWith => __$PerformanceCopyWithImpl<_Performance>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PerformanceToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Performance&&(identical(other.id, id) || other.id == id)&&(identical(other.date, date) || other.date == date)&&(identical(other.bidPerformance, bidPerformance) || other.bidPerformance == bidPerformance)&&(identical(other.tripPerformance, tripPerformance) || other.tripPerformance == tripPerformance)&&(identical(other.activeDays, activeDays) || other.activeDays == activeDays)&&(identical(other.onlineHours, onlineHours) || other.onlineHours == onlineHours)&&(identical(other.avgCompleted, avgCompleted) || other.avgCompleted == avgCompleted)&&(identical(other.avgOnline, avgOnline) || other.avgOnline == avgOnline)&&(identical(other.retention, retention) || other.retention == retention));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,date,bidPerformance,tripPerformance,activeDays,onlineHours,avgCompleted,avgOnline,retention);

@override
String toString() {
  return 'Performance(id: $id, date: $date, bidPerformance: $bidPerformance, tripPerformance: $tripPerformance, activeDays: $activeDays, onlineHours: $onlineHours, avgCompleted: $avgCompleted, avgOnline: $avgOnline, retention: $retention)';
}


}

/// @nodoc
abstract mixin class _$PerformanceCopyWith<$Res> implements $PerformanceCopyWith<$Res> {
  factory _$PerformanceCopyWith(_Performance value, $Res Function(_Performance) _then) = __$PerformanceCopyWithImpl;
@override @useResult
$Res call({
 int? id, DateTime date, double bidPerformance, double tripPerformance, int activeDays, double onlineHours, double? avgCompleted, double? avgOnline, double? retention
});




}
/// @nodoc
class __$PerformanceCopyWithImpl<$Res>
    implements _$PerformanceCopyWith<$Res> {
  __$PerformanceCopyWithImpl(this._self, this._then);

  final _Performance _self;
  final $Res Function(_Performance) _then;

/// Create a copy of Performance
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? date = null,Object? bidPerformance = null,Object? tripPerformance = null,Object? activeDays = null,Object? onlineHours = null,Object? avgCompleted = freezed,Object? avgOnline = freezed,Object? retention = freezed,}) {
  return _then(_Performance(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,date: null == date ? _self.date : date // ignore: cast_nullable_to_non_nullable
as DateTime,bidPerformance: null == bidPerformance ? _self.bidPerformance : bidPerformance // ignore: cast_nullable_to_non_nullable
as double,tripPerformance: null == tripPerformance ? _self.tripPerformance : tripPerformance // ignore: cast_nullable_to_non_nullable
as double,activeDays: null == activeDays ? _self.activeDays : activeDays // ignore: cast_nullable_to_non_nullable
as int,onlineHours: null == onlineHours ? _self.onlineHours : onlineHours // ignore: cast_nullable_to_non_nullable
as double,avgCompleted: freezed == avgCompleted ? _self.avgCompleted : avgCompleted // ignore: cast_nullable_to_non_nullable
as double?,avgOnline: freezed == avgOnline ? _self.avgOnline : avgOnline // ignore: cast_nullable_to_non_nullable
as double?,retention: freezed == retention ? _self.retention : retention // ignore: cast_nullable_to_non_nullable
as double?,
  ));
}


}

// dart format on

import 'package:dartz/dartz.dart';
import 'package:drift/drift.dart';
import 'package:uuid/uuid.dart';

import '../../../../core/datasources/app_database.dart' as db;
import '../../../../core/errors/exceptions.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/services/sync/sync_operations.dart';
import '../../../../core/services/sync/sync_service.dart';
import '../../../orders/domain/repositories/order_repository.dart';
import '../../domain/entities/performance.dart';
import '../../domain/repositories/performance_repository.dart';
import '../../domain/use_cases/calculate_retention.dart';

class PerformanceRepositoryImpl implements PerformanceRepository {
  final db.AppDatabase database;
  final CalculateRetention calculateRetention;
  final OrderRepository orderRepository;
  final SyncService syncService;

  PerformanceRepositoryImpl({
    required this.database,
    required this.calculateRetention,
    required this.orderRepository,
    required this.syncService,
  });

  @override
  Future<Either<Failure, List<Performance>>> getAllPerformance() async {
    try {
      final performanceList = await database.getAllPerformance();

      return Right(performanceList.map((data) => _mapFromData(data)).toList());
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Performance>> getPerformanceById(int id) async {
    try {
      final query = database.select(database.performance)
        ..where((tbl) => tbl.id.equals(id));

      final performanceData = await query.getSingleOrNull();

      if (performanceData == null) {
        return const Left(Failure.notFound(message: 'Performance record not found'));
      }

      return Right(_mapFromData(performanceData));
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Performance>> savePerformance(Performance performance) async {
    try {
      // Get total completed orders for the last 14 days
      final totalCompletedOrdersResult = await getTotalCompletedOrdersForLast14Days(performance.date);

      final totalCompletedOrders = totalCompletedOrdersResult.fold(
        (failure) => null,
        (total) => total,
      );

      // Calculate all derived fields
      final calculatedPerformance = calculateRetention.execute(
        performance,
        totalCompletedOrders: totalCompletedOrders,
      );

      return calculatedPerformance.fold(
        (failure) => Left(failure),
        (calculatedPerformance) async {
          final id = await database.insertPerformance(_mapToCompanion(calculatedPerformance)); // For new records, no need to pass uuid or originalCreatedAt

          return Right(calculatedPerformance.copyWith(id: id));
        },
      );
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Performance>> updatePerformance(Performance performance) async {
    try {
      if (performance.id == null) {
        return const Left(Failure.invalidInput(message: 'Performance ID cannot be null for update'));
      }

      // Get the existing record to preserve the UUID
      final query = database.select(database.performance)
        ..where((tbl) => tbl.id.equals(performance.id!));
      final existingRecord = await query.getSingleOrNull();

      if (existingRecord == null) {
        return const Left(Failure.notFound(message: 'Performance record not found'));
      }

      // Get total completed orders for the last 14 days
      final totalCompletedOrdersResult = await getTotalCompletedOrdersForLast14Days(performance.date);

      final totalCompletedOrders = totalCompletedOrdersResult.fold(
        (failure) => null,
        (total) => total,
      );

      // Calculate all derived fields
      final calculatedPerformance = calculateRetention.execute(
        performance,
        totalCompletedOrders: totalCompletedOrders,
      );

      return calculatedPerformance.fold(
        (failure) => Left(failure),
        (calculatedPerformance) async {
          // Map to data with the existing UUID and original createdAt
          final performanceData = _mapToDataWithUuid(
            calculatedPerformance,
            existingRecord.uuid,
            originalCreatedAt: existingRecord.createdAt
          );
          final success = await database.updatePerformance(performanceData);

          if (!success) {
            return const Left(Failure.notFound(message: 'Performance record not found'));
          }

          return Right(calculatedPerformance);
        },
      );
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> deletePerformance(int id) async {
    try {
      // Use soft delete instead of hard delete
      final success = await database.softDeletePerformance(id);

      if (!success) {
        return const Left(Failure.notFound(message: 'Performance record not found'));
      }

      // Trigger sync after deleting a record
      syncService.syncData(SyncOperation.upload);

      return const Right(true);
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Performance>>> getPerformanceForDateRange(
    DateTime start,
    DateTime end,
  ) async {
    try {
      final query = database.select(database.performance)
        ..where((tbl) => tbl.date.isBetweenValues(
          start,
          end.add(const Duration(days: 1)).subtract(const Duration(microseconds: 1)),
        ))
        ..where((tbl) => tbl.deletedAt.isNull())
        ..orderBy([(t) => OrderingTerm.desc(t.date)]);

      final performanceList = await query.get();

      return Right(performanceList.map((data) => _mapFromData(data)).toList());
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> checkDateExists(DateTime date, {int? excludeId}) async {
    try {
      // Create a query to find records with the same date
      final query = database.select(database.performance)
        ..where((tbl) => tbl.date.equals(date))
        ..where((tbl) => tbl.deletedAt.isNull());

      // If excludeId is provided, exclude that record from the check
      // This is useful when updating an existing record
      if (excludeId != null) {
        query.where((tbl) => tbl.id.isNotValue(excludeId));
      }

      // Get the count of records with the same date
      final count = await query.get().then((records) => records.length);

      // Return true if any records were found with the same date
      return Right(count > 0);
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, int>> getTotalCompletedOrdersForLast14Days(DateTime endDate) async {
    try {
      // Get orders for the last 14 days (not including the current date)
      // If endDate is March 25, 2025, we want orders from March 11, 2025 to March 24, 2025
      final ordersResult = await orderRepository.getOrdersForPerformanceCalculation(endDate);

      return ordersResult.fold(
        (failure) => Left(failure),
        (orders) {
          // Calculate total completed orders
          int totalOrdersCompleted = 0;
          for (final order in orders) {
            totalOrdersCompleted += order.orderCompleted;
          }

          return Right(totalOrdersCompleted);
        },
      );
    } catch (e) {
      return Left(Failure.unexpected(message: 'Error calculating total completed orders: $e'));
    }
  }

  // Helper method to map from database entity to domain entity
  Performance _mapFromData(db.PerformanceData data) {
    return Performance(
      id: data.id,
      date: data.date,
      bidPerformance: data.bidPerformance,
      tripPerformance: data.tripPerformance,
      activeDays: data.activeDays,
      onlineHours: data.onlineHours,
      avgCompleted: data.avgCompleted,
      avgOnline: data.avgOnline,
      retention: data.retention,
    );
  }



  // Helper method to map from domain entity to database entity with a specific UUID (for updates)
  db.PerformanceData _mapToDataWithUuid(Performance performance, String uuid, {DateTime? originalCreatedAt}) {
    return db.PerformanceData(
      id: performance.id!,
      uuid: uuid,
      date: performance.date,
      bidPerformance: performance.bidPerformance,
      tripPerformance: performance.tripPerformance,
      activeDays: performance.activeDays,
      onlineHours: performance.onlineHours,
      avgCompleted: performance.avgCompleted,
      avgOnline: performance.avgOnline,
      retention: performance.retention,
      createdAt: originalCreatedAt ?? DateTime.now(), // Preserve original createdAt if provided
      updatedAt: DateTime.now(),
      syncStatus: db.SyncStatus.pendingUpload,
    );
  }

  // Helper method to map from domain entity to database companion
  db.PerformanceCompanion _mapToCompanion(Performance performance, {String? uuid, DateTime? originalCreatedAt}) {
    return db.PerformanceCompanion(
      id: performance.id == null ? const Value.absent() : Value(performance.id!),
      uuid: Value(uuid ?? const Uuid().v4()),
      date: Value(performance.date),
      bidPerformance: Value(performance.bidPerformance),
      tripPerformance: Value(performance.tripPerformance),
      activeDays: Value(performance.activeDays),
      onlineHours: Value(performance.onlineHours),
      avgCompleted: performance.avgCompleted == null ? const Value.absent() : Value(performance.avgCompleted),
      avgOnline: performance.avgOnline == null ? const Value.absent() : Value(performance.avgOnline),
      retention: performance.retention == null ? const Value.absent() : Value(performance.retention),
      createdAt: Value(originalCreatedAt ?? DateTime.now()), // Preserve original createdAt if provided
      updatedAt: Value(DateTime.now()),
      syncStatus: const Value(db.SyncStatus.pendingUpload),
    );
  }
}

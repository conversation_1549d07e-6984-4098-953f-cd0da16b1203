import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/services/auth_service.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_dimensions.dart';
import '../../../../core/utils/text_style_helper.dart';
import '../widgets/auth_button.dart';
import '../widgets/auth_text_field.dart';

class ForgotPasswordScreen extends ConsumerStatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  ConsumerState<ForgotPasswordScreen> createState() =>
      _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends ConsumerState<ForgotPasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  bool _isLoading = false;
  String? _errorMessage;
  bool _resetSent = false;

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  Future<void> _resetPassword() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final authService = ref.read(authServiceProvider);
      await authService.resetPassword(_emailController.text.trim());

      if (mounted) {
        setState(() {
          _resetSent = true;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Initialize dimensions for responsive layout
    AppDimensions.init(context);

    return Scaffold(
      appBar: AppBar(
        title: Text('Reset Password', style: TextStyleHelper.heading2(context)),
        backgroundColor: Colors.transparent,
        elevation: 0,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
      ),
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: EdgeInsets.all(AppDimensions.spacing24),
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  if (_resetSent)
                    // Success message
                    Container(
                      padding: EdgeInsets.all(AppDimensions.spacing16),
                      margin: EdgeInsets.only(bottom: AppDimensions.spacing24),
                      decoration: BoxDecoration(
                        color: AppColors.successLight,
                        borderRadius: BorderRadius.circular(
                          AppDimensions.buttonRadius,
                        ),
                      ),
                      child: Column(
                        children: [
                          Icon(
                            Icons.check_circle_outline,
                            color: AppColors.success,
                            size: AppDimensions.iconSizeLarge * 2,
                          ),
                          SizedBox(height: AppDimensions.spacing16),
                          Text(
                            'Reset Link Sent!',
                            style: TextStyleHelper.heading3(
                              context,
                            ).copyWith(color: AppColors.success),
                          ),
                          SizedBox(height: AppDimensions.spacing8),
                          Text(
                            'We\'ve sent a password reset link to ${_emailController.text}. Please check your email.',
                            textAlign: TextAlign.center,
                            style: TextStyleHelper.bodyMedium(context),
                          ),
                          SizedBox(height: AppDimensions.spacing16),
                          ElevatedButton(
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.success,
                              foregroundColor: Colors.white,
                            ),
                            child: Text(
                              'Back to Login',
                              style: TextStyleHelper.buttonText(context),
                            ),
                          ),
                        ],
                      ),
                    )
                  else
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Text(
                          'Forgot your password?',
                          style: TextStyleHelper.heading2(context),
                        ),
                        SizedBox(height: AppDimensions.spacing8),
                        Text(
                          'Enter your email address and we\'ll send you a link to reset your password.',
                          style: TextStyleHelper.bodyMedium(context).copyWith(
                            color: AppColors.withOpacity(
                              Theme.of(context).colorScheme.onSurface,
                              0.7,
                            ),
                          ),
                        ),
                        SizedBox(height: AppDimensions.spacing32),

                        // Error message
                        if (_errorMessage != null)
                          Container(
                            padding: EdgeInsets.all(AppDimensions.spacing12),
                            margin: EdgeInsets.only(
                              bottom: AppDimensions.spacing16,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.errorLight,
                              borderRadius: BorderRadius.circular(
                                AppDimensions.buttonRadius,
                              ),
                            ),
                            child: Text(
                              _errorMessage!,
                              style: TextStyleHelper.bodyMedium(context)
                                  .copyWith(
                                    color: Theme.of(context).colorScheme.error,
                                  ),
                            ),
                          ),

                        // Email field
                        AuthTextField(
                          controller: _emailController,
                          hintText: 'Email',
                          prefixIcon: Icons.email_outlined,
                          keyboardType: TextInputType.emailAddress,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter your email';
                            }
                            if (!RegExp(
                              r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                            ).hasMatch(value)) {
                              return 'Please enter a valid email';
                            }
                            return null;
                          },
                        ),
                        SizedBox(height: AppDimensions.spacing24),

                        // Reset button
                        AuthButton(
                          text: 'Send Reset Link',
                          isLoading: _isLoading,
                          onPressed: _resetPassword,
                        ),
                      ],
                    ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

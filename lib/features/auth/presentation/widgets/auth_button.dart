import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';

class AuthButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final bool isLoading;
  final Color? backgroundColor;
  final Color? textColor;

  const AuthButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.isLoading = false,
    this.backgroundColor,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    // Get theme colors
    final colorScheme = Theme.of(context).colorScheme;

    // Fixed sizes
    const double buttonHeight = 48.0;
    const double loaderSize = 20.0;

    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor ?? colorScheme.primary,
        foregroundColor: textColor ?? colorScheme.onPrimary,
        disabledBackgroundColor: AppColors.primary.withA<PERSON><PERSON>(178),
        disabledForegroundColor: colorScheme.onPrimary.withAlpha(204),
        padding: const EdgeInsets.symmetric(vertical: 16.0),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.0)),
        elevation: 0,
        minimumSize: const Size(0, buttonHeight),
      ),
      child: isLoading
          ? SizedBox(
              height: loaderSize,
              width: loaderSize,
              child: CircularProgressIndicator(
                color: textColor ?? colorScheme.onPrimary,
                strokeWidth: 2,
              ),
            )
          : Text(
              text,
              style: const TextStyle(
                fontSize: 14.0,
                fontWeight: FontWeight.bold,
              ),
            ),
    );
  }
}

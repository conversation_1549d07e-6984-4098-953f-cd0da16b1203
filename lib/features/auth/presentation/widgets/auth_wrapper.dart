import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/services/auth_service.dart';
import '../../../../features/home/<USER>/screens/home_screen.dart';

/// AuthWrapper that bypasses login and goes directly to HomePage
///
/// This component no longer forces users to log in to use the app.
/// Authentication is now optional and only required for sync functionality.
class AuthWrapper extends ConsumerWidget {
  const AuthWrapper({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch the auth state provider to update UI when auth state changes
    final authState = ref.watch(authStateProvider);

    // Show loading indicator while auth state is being determined
    if (authState.isLoading) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    // Always show home screen regardless of authentication state
    // Authentication is now optional and only required for sync
    return const HomeScreen();
  }
}

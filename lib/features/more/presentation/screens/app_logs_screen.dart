import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/services/auth/auth_logger.dart';
import '../../../../core/services/sync/sync_logger.dart';
import '../../../../core/services/sync/sync_service.dart';
import '../../../../core/theme/app_colors.dart';

/// Enum for log types
enum LogType {
  /// Sync logs
  sync,

  /// Authentication logs
  auth,
}

/// Provider for the selected log type
final selectedLogTypeProvider = StateProvider<LogType>((ref) => LogType.sync);

/// Provider for the auth log importance filter
final authLogImportanceFilterProvider = StateProvider<AuthLogImportance>(
  (ref) => AuthLogImportance.important,
);

/// A unified screen that displays both sync and authentication logs
class AppLogsScreen extends ConsumerWidget {
  /// Constructor
  const AppLogsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedLogType = ref.watch(selectedLogTypeProvider);

    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () async {
          // Refresh logs based on selected type
          if (selectedLogType == LogType.sync) {
            ref.read(syncServiceProvider).refreshLogs();
          } else {
            // For auth logs, just trigger a UI refresh
            ref.invalidate(authLogsProvider);
          }
        },
        child: CustomScrollView(
          slivers: [
            _buildAppBar(context, ref, selectedLogType),

            // Show logs based on selected type
            if (selectedLogType == LogType.sync)
              _buildSyncLogs(ref)
            else
              _buildAuthLogs(ref),
          ],
        ),
      ),
    );
  }

  /// Builds the app bar with log type selector
  SliverAppBar _buildAppBar(
    BuildContext context,
    WidgetRef ref,
    LogType selectedLogType,
  ) {
    return SliverAppBar(
      floating: true,
      pinned: true,
      elevation: 0,
      backgroundColor: AppColors.primary,
      title: Text('${selectedLogType == LogType.sync ? 'Sync' : 'Auth'} Logs'),
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(60),
        child: Padding(
          padding: const EdgeInsets.fromLTRB(16.0, 4.0, 16.0, 12.0),
          child: _buildLogFilterSelector(context, ref, selectedLogType),
        ),
      ),
      actions: [
        // Log type selector
        IconButton(
          icon: Icon(
            selectedLogType == LogType.sync ? Icons.security : Icons.sync,
          ),
          tooltip:
              'Switch to ${selectedLogType == LogType.sync ? 'Auth' : 'Sync'} Logs',
          onPressed: () {
            ref.read(selectedLogTypeProvider.notifier).state =
                selectedLogType == LogType.sync ? LogType.auth : LogType.sync;
          },
        ),
        // Clear logs button
        IconButton(
          icon: const Icon(Icons.delete_sweep),
          tooltip: 'Clear logs',
          onPressed: () {
            _showClearLogsConfirmation(context, ref, selectedLogType);
          },
        ),
      ],
    );
  }

  /// Builds the appropriate filter selector based on log type
  Widget _buildLogFilterSelector(
    BuildContext context,
    WidgetRef ref,
    LogType selectedLogType,
  ) {
    if (selectedLogType == LogType.sync) {
      final logLevelFilter = ref.watch(logLevelFilterProvider);
      return _buildSyncLogLevelSelector(context, ref, logLevelFilter);
    } else {
      final importanceFilter = ref.watch(authLogImportanceFilterProvider);
      return _buildAuthLogImportanceSelector(context, ref, importanceFilter);
    }
  }

  /// Builds the sync log level selector
  Widget _buildSyncLogLevelSelector(
    BuildContext context,
    WidgetRef ref,
    LogLevel logLevelFilter,
  ) {
    return InkWell(
      onTap: () => _selectSyncLogLevel(context, ref),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppColors.primary.withAlpha(77)),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                'Show ${logLevelFilter.label}+',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: _getSyncLogLevelColor(logLevelFilter),
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const Icon(Icons.filter_list),
          ],
        ),
      ),
    );
  }

  /// Builds the auth log importance selector
  Widget _buildAuthLogImportanceSelector(
    BuildContext context,
    WidgetRef ref,
    AuthLogImportance importanceFilter,
  ) {
    return InkWell(
      onTap: () => _selectAuthLogImportance(context, ref),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppColors.primary.withAlpha(77)),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                'Show ${_getAuthImportanceLabel(importanceFilter)}+',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: _getAuthImportanceColor(importanceFilter),
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const Icon(Icons.filter_list),
          ],
        ),
      ),
    );
  }

  /// Builds the sync logs section
  Widget _buildSyncLogs(WidgetRef ref) {
    final syncLogs = ref.watch(syncLogsProvider);
    final logLevelFilter = ref.watch(logLevelFilterProvider);

    // Filter logs based on selected level
    final filteredLogs = syncLogs
        .where((log) => log.level >= logLevelFilter)
        .toList();

    // Show empty state if no logs
    if (filteredLogs.isEmpty) {
      return const SliverFillRemaining(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.history, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text(
                'No sync logs available',
                style: TextStyle(fontSize: 18, color: Colors.grey),
              ),
              SizedBox(height: 8),
              Text(
                'Logs will appear here after sync operations',
                style: TextStyle(fontSize: 14, color: Colors.grey),
              ),
            ],
          ),
        ),
      );
    }

    // Reverse the logs to show newest first
    final reversedLogs = filteredLogs.reversed.toList();

    return SliverPadding(
      padding: const EdgeInsets.all(8.0),
      sliver: SliverList(
        delegate: SliverChildBuilderDelegate((context, index) {
          final logEntry = reversedLogs[index];
          return _buildSyncLogItem(context, logEntry);
        }, childCount: reversedLogs.length),
      ),
    );
  }

  /// Builds the auth logs section
  Widget _buildAuthLogs(WidgetRef ref) {
    final authLogs = ref.watch(authLogsProvider);
    final importanceFilter = ref.watch(authLogImportanceFilterProvider);

    // Filter logs based on selected importance
    final filteredLogs = authLogs.where((log) {
      switch (importanceFilter) {
        case AuthLogImportance.verbose:
          return true; // Show all logs
        case AuthLogImportance.important:
          return log.importance == AuthLogImportance.important ||
              log.importance == AuthLogImportance.critical;
        case AuthLogImportance.critical:
          return log.importance == AuthLogImportance.critical;
      }
    }).toList();

    // Show empty state if no logs
    if (filteredLogs.isEmpty) {
      return const SliverFillRemaining(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.security, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text(
                'No authentication logs available',
                style: TextStyle(fontSize: 18, color: Colors.grey),
              ),
              SizedBox(height: 8),
              Text(
                'Logs will appear here after authentication events',
                style: TextStyle(fontSize: 14, color: Colors.grey),
              ),
            ],
          ),
        ),
      );
    }

    // Reverse the logs to show newest first
    final reversedLogs = filteredLogs.reversed.toList();

    return SliverPadding(
      padding: const EdgeInsets.all(8.0),
      sliver: SliverList(
        delegate: SliverChildBuilderDelegate((context, index) {
          final logEntry = reversedLogs[index];
          return _buildAuthLogItem(context, logEntry);
        }, childCount: reversedLogs.length),
      ),
    );
  }

  /// Builds a sync log item
  Widget _buildSyncLogItem(BuildContext context, LogEntry logEntry) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8.0),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  logEntry.formattedTimestamp,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: _getSyncLogLevelColor(logEntry.level).withAlpha(25),
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(
                      color: _getSyncLogLevelColor(
                        logEntry.level,
                      ).withAlpha(128),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    logEntry.level.label,
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: _getSyncLogLevelColor(logEntry.level),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              logEntry.message,
              style: TextStyle(
                fontSize: 14,
                color: _getSyncMessageColor(logEntry.message, logEntry.level),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds an auth log item
  Widget _buildAuthLogItem(BuildContext context, AuthLogEntry logEntry) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8.0),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  logEntry.formattedLocalTimestamp,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: _getAuthImportanceColor(
                      logEntry.importance,
                    ).withAlpha(25),
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(
                      color: _getAuthImportanceColor(
                        logEntry.importance,
                      ).withAlpha(128),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    _getAuthImportanceLabel(logEntry.importance),
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: _getAuthImportanceColor(logEntry.importance),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              logEntry.message,
              style: TextStyle(
                fontSize: 14,
                color: _getAuthMessageColor(
                  logEntry.message,
                  logEntry.importance,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Shows a confirmation dialog for clearing logs
  void _showClearLogsConfirmation(
    BuildContext context,
    WidgetRef ref,
    LogType logType,
  ) {
    final logTypeStr = logType == LogType.sync ? 'sync' : 'authentication';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Logs'),
        content: Text('Are you sure you want to clear all $logTypeStr logs?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () {
              // Clear the logs based on type
              if (logType == LogType.sync) {
                ref.read(syncServiceProvider).clearLogs();
              } else {
                // Clear auth logs
                ref.read(authLogsProvider.notifier).state = [];
              }
              Navigator.of(context).pop();
            },
            child: const Text('CLEAR'),
          ),
        ],
      ),
    );
  }

  /// Shows a dialog to select sync log level
  Future<void> _selectSyncLogLevel(BuildContext context, WidgetRef ref) async {
    final selectedLevel = await showDialog<LogLevel>(
      context: context,
      builder: (BuildContext context) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(
              context,
            ).colorScheme.copyWith(primary: AppColors.primary),
          ),
          child: AlertDialog(
            title: const Text('Select Log Level'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: LogLevel.values.map((level) {
                return ListTile(
                  title: Text(
                    'Show ${level.label}+',
                    style: TextStyle(
                      color: _getSyncLogLevelColor(level),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  onTap: () {
                    Navigator.of(context).pop(level);
                  },
                  selected: level == ref.read(logLevelFilterProvider),
                  selectedTileColor: AppColors.primary.withAlpha(25),
                );
              }).toList(),
            ),
          ),
        );
      },
    );

    if (selectedLevel != null) {
      ref.read(logLevelFilterProvider.notifier).state = selectedLevel;
    }
  }

  /// Shows a dialog to select auth log importance
  Future<void> _selectAuthLogImportance(
    BuildContext context,
    WidgetRef ref,
  ) async {
    final selectedImportance = await showDialog<AuthLogImportance>(
      context: context,
      builder: (BuildContext context) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(
              context,
            ).colorScheme.copyWith(primary: AppColors.primary),
          ),
          child: AlertDialog(
            title: const Text('Select Log Importance'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: AuthLogImportance.values.map((importance) {
                return ListTile(
                  title: Text(
                    'Show ${_getAuthImportanceLabel(importance)}+',
                    style: TextStyle(
                      color: _getAuthImportanceColor(importance),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  onTap: () {
                    Navigator.of(context).pop(importance);
                  },
                  selected:
                      importance == ref.read(authLogImportanceFilterProvider),
                  selectedTileColor: AppColors.primary.withAlpha(25),
                );
              }).toList(),
            ),
          ),
        );
      },
    );

    if (selectedImportance != null) {
      ref.read(authLogImportanceFilterProvider.notifier).state =
          selectedImportance;
    }
  }

  /// Gets the color for a sync log level
  Color _getSyncLogLevelColor(LogLevel level) {
    switch (level) {
      case LogLevel.debug:
        return Colors.grey;
      case LogLevel.info:
        return Colors.blue;
      case LogLevel.warning:
        return Colors.orange;
      case LogLevel.error:
        return Colors.red;
    }
  }

  /// Gets the color for an auth log importance
  Color _getAuthImportanceColor(AuthLogImportance importance) {
    switch (importance) {
      case AuthLogImportance.verbose:
        return Colors.grey;
      case AuthLogImportance.important:
        return Colors.blue;
      case AuthLogImportance.critical:
        return Colors.red;
    }
  }

  /// Gets the label for an auth log importance
  String _getAuthImportanceLabel(AuthLogImportance importance) {
    switch (importance) {
      case AuthLogImportance.verbose:
        return 'All';
      case AuthLogImportance.important:
        return 'Important';
      case AuthLogImportance.critical:
        return 'Critical';
    }
  }

  /// Gets the color for a sync log message
  Color _getSyncMessageColor(String message, LogLevel level) {
    if (level == LogLevel.error) {
      return Colors.red.shade800;
    } else if (level == LogLevel.warning) {
      return Colors.orange.shade800;
    } else if (message.contains('ERROR') || message.contains('FAILED')) {
      return Colors.red.shade800;
    } else if (message.contains('WARNING')) {
      return Colors.orange.shade800;
    } else {
      return Colors.black87;
    }
  }

  /// Gets the color for an auth log message
  Color _getAuthMessageColor(String message, AuthLogImportance importance) {
    if (importance == AuthLogImportance.critical) {
      return Colors.red.shade800;
    } else if (message.contains('ERROR') ||
        message.contains('FAILED') ||
        message.contains('WARNING') ||
        message.contains('EXPIRED')) {
      return Colors.red.shade800;
    } else if (message.contains('AUTH STATE CHANGE')) {
      return Colors.blue.shade800;
    } else {
      return Colors.black87;
    }
  }
}

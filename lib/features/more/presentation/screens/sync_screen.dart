import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/providers/sync_preference_provider.dart';
import '../../../../core/services/auth_service.dart';
import '../../../../core/services/sync/sync_operations.dart';
import '../../../../core/services/sync/sync_service.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_dimensions.dart';

import '../../../../core/widgets/expandable_card.dart';
import '../../../../core/widgets/sync_status_indicator_widget.dart';
import '../../../auth/presentation/screens/login_screen.dart';
import 'app_logs_screen.dart';

class SyncScreen extends ConsumerStatefulWidget {
  const SyncScreen({super.key});

  @override
  ConsumerState<SyncScreen> createState() => _SyncScreenState();
}

class _SyncScreenState extends ConsumerState<SyncScreen> {
  @override
  Widget build(BuildContext context) {
    final syncState = ref.watch(syncStateProvider);
    final lastSyncTime = ref.watch(lastSyncTimeProvider);
    final nextScheduledSync = ref.watch(nextScheduledSyncProvider);
    final syncEnabled = ref.watch(syncEnabledProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Cloud Sync'),
        backgroundColor: AppColors.primary,
      ),
      body: ListView(
        padding: EdgeInsets.all(AppDimensions.spacing16),
        children: [
          _buildStatusCard(context, syncState, lastSyncTime, nextScheduledSync),
          SizedBox(height: AppDimensions.spacing16),
          _buildAutoSyncCard(context, ref, syncEnabled),
          SizedBox(height: AppDimensions.spacing16),
          _buildSyncOptionsCard(context, ref),
          SizedBox(height: AppDimensions.spacing16),
          _buildInfoCard(context),
        ],
      ),
    );
  }

  Widget _buildStatusCard(
    BuildContext context,
    AsyncValue<String> syncState,
    DateTime? lastSyncTime,
    DateTime? nextScheduledSync,
  ) {
    // Initialize dimensions
    AppDimensions.init(context);

    final syncService = ref.read(syncServiceProvider);

    return Card(
      elevation: 0,
      margin: EdgeInsets.only(bottom: AppDimensions.spacing12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.cardRadius),
        side: BorderSide(
          color: Colors.grey.withAlpha(30),
          width: AppDimensions.borderWidth,
        ),
      ),
      child: Padding(
        padding: EdgeInsets.all(AppDimensions.spacing16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Use the new SyncStatusIndicatorWidget
            const SyncStatusIndicatorWidget(
              compact: false,
              showNextSync: true,
              showDetailedStatus: true,
            ),

            // Add manual sync button
            SizedBox(height: AppDimensions.spacing16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  syncService.syncNow(SyncOperation.full, isManualSync: true);
                },
                icon: const Icon(Icons.sync),
                label: const Text('Sync Now'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(
                    vertical: AppDimensions.spacing12,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAutoSyncCard(
    BuildContext context,
    WidgetRef ref,
    bool syncEnabled,
  ) {
    // Check if user is authenticated
    final authState = ref.watch(authStateProvider);
    final isAuthenticated = authState.isAuthenticated;

    return Card(
      elevation: 0,
      margin: EdgeInsets.only(bottom: AppDimensions.spacing12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.cardRadius),
        side: BorderSide(
          color: Colors.grey.withAlpha(30),
          width: AppDimensions.borderWidth,
        ),
      ),
      child: Padding(
        padding: EdgeInsets.all(AppDimensions.spacing16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // Text content - use Expanded to take available space
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Automatic Sync',
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: AppDimensions.spacing4),
                      Text(
                        isAuthenticated
                            ? 'Enable to sync data automatically when changes are made'
                            : 'Sign in to enable automatic sync',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: isAuthenticated
                              ? AppColors.textSecondary
                              : AppColors.warning,
                        ),
                      ),
                    ],
                  ),
                ),
                // Small switch with minimal size
                Transform.scale(
                  scale: 0.7, // Make the switch smaller
                  child: Switch(
                    value: syncEnabled,
                    onChanged: isAuthenticated
                        ? (value) => ref
                              .read(syncEnabledProvider.notifier)
                              .updateSyncEnabled(value)
                        : null, // Disable switch when not authenticated
                    activeColor: AppColors.primary,
                    materialTapTargetSize: MaterialTapTargetSize
                        .shrinkWrap, // Reduce tap target size
                  ),
                ),
              ],
            ),

            // Show login button if not authenticated
            if (!isAuthenticated) ...[
              SizedBox(height: AppDimensions.spacing12),
              OutlinedButton.icon(
                icon: const Icon(Icons.login, size: 18),
                label: const Text('Login to Enable Sync'),
                onPressed: () async {
                  // Navigate to login screen and wait for it to complete
                  await Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const LoginScreen(),
                    ),
                  );

                  // After returning from login screen, refresh the UI to reflect new auth state
                  if (mounted) {
                    // Force a rebuild of the widget to reflect the new authentication state
                    setState(() {});
                  }
                },
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppColors.primary,
                  side: const BorderSide(color: AppColors.primary),
                  padding: EdgeInsets.symmetric(
                    horizontal: AppDimensions.spacing16,
                    vertical: AppDimensions.spacing8,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSyncOptionsCard(BuildContext context, WidgetRef ref) {
    final syncService = ref.read(syncServiceProvider);

    return ExpandableCard(
      title: 'Sync Options',
      icon: Icons.sync_alt,
      iconColor: AppColors.primary,
      borderColor: AppColors.primary,
      initiallyExpanded: false,
      collapsedChild: const SizedBox.shrink(), // Empty when collapsed
      expandedChild: Column(
        children: [
          ListTile(
            leading: const Icon(Icons.cloud_upload, color: AppColors.primary),
            title: Text(
              'Upload to Cloud',
              style: Theme.of(
                context,
              ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w500),
            ),
            subtitle: Text(
              'Send local changes to cloud',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
            ),
            onTap: () =>
                syncService.syncNow(SyncOperation.upload, isManualSync: true),
          ),
          const Divider(height: 1, indent: 16, endIndent: 16),
          ListTile(
            leading: const Icon(Icons.cloud_download, color: AppColors.primary),
            title: Text(
              'Download from Cloud',
              style: Theme.of(
                context,
              ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w500),
            ),
            subtitle: Text(
              'Get latest data from cloud',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
            ),
            onTap: () =>
                syncService.syncNow(SyncOperation.download, isManualSync: true),
          ),
          const Divider(height: 1, indent: 16, endIndent: 16),
          ListTile(
            leading: const Icon(Icons.sync, color: AppColors.primary),
            title: Text(
              'Full Sync',
              style: Theme.of(
                context,
              ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w500),
            ),
            subtitle: Text(
              'Upload and download all data',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
            ),
            onTap: () =>
                syncService.syncNow(SyncOperation.full, isManualSync: true),
          ),
          const Divider(height: 1, indent: 16, endIndent: 16),
          ListTile(
            leading: const Icon(Icons.history, color: AppColors.primary),
            title: Text(
              'View Sync Logs',
              style: Theme.of(
                context,
              ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w500),
            ),
            subtitle: Text(
              'See detailed sync history',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
            ),
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const AppLogsScreen()),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard(BuildContext context) {
    return ExpandableCard(
      title: 'About Cloud Sync',
      icon: Icons.info_outline,
      iconColor: AppColors.info,
      borderColor: AppColors.info,
      initiallyExpanded: false,
      collapsedChild: const SizedBox.shrink(), // Empty when collapsed
      expandedChild: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoItem(
            context,
            Icons.info_outline,
            'Your data is synced to the cloud when Automatic Sync is enabled',
          ),
          SizedBox(height: AppDimensions.spacing8),
          _buildInfoItem(
            context,
            Icons.info_outline,
            'Full sync is scheduled to run automatically every 6 days to conserve resources',
          ),
          SizedBox(height: AppDimensions.spacing8),
          _buildInfoItem(
            context,
            Icons.info_outline,
            'Sync will also run immediately when a significant number of changes are detected',
          ),
          SizedBox(height: AppDimensions.spacing8),
          _buildInfoItem(
            context,
            Icons.info_outline,
            'Sync no longer triggers automatically on app launch, resume, or connectivity changes',
          ),
          SizedBox(height: AppDimensions.spacing8),
          _buildInfoItem(
            context,
            Icons.info_outline,
            'All your devices with the same account will stay in sync',
          ),
          SizedBox(height: AppDimensions.spacing8),
          _buildInfoItem(
            context,
            Icons.warning_amber,
            'If you need immediate sync, use the manual sync options',
            isWarning: false,
          ),
          SizedBox(height: AppDimensions.spacing8),
          _buildInfoItem(
            context,
            Icons.warning_amber,
            'If you experience sync issues, try a Full Sync',
            isWarning: true,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(
    BuildContext context,
    IconData icon,
    String text, {
    bool isWarning = false,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: 20,
          color: isWarning ? AppColors.warning : AppColors.textSecondary,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: isWarning ? AppColors.warning : AppColors.textSecondary,
            ),
          ),
        ),
      ],
    );
  }
}

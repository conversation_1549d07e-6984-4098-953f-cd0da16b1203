import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/services/sync/sync_logger.dart';
import '../../../../core/services/sync/sync_service.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/widgets/log_level_selector_field.dart';

/// A screen that displays the sync logs
class SyncLogsScreen extends ConsumerWidget {
  const SyncLogsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final syncLogs = ref.watch(syncLogsProvider);
    final syncService = ref.watch(syncServiceProvider);
    final logLevelFilter = ref.watch(logLevelFilterProvider);

    // Filter logs based on selected level
    final filteredLogs = syncLogs
        .where((log) => log.level >= logLevelFilter)
        .toList();

    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () async {
          // Instead of invalidating the provider, we'll add a refresh log entry
          // This will trigger a UI update without clearing the logs
          syncService.refreshLogs();
        },
        child: CustomScrollView(
          slivers: [
            _buildAppBar(context, ref, syncService, logLevelFilter),

            // Show either empty state or logs list
            filteredLogs.isEmpty
                ? SliverFillRemaining(child: _buildEmptyState())
                : _buildLogsList(filteredLogs),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.history, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'No sync logs available',
            style: TextStyle(fontSize: 18, color: Colors.grey),
          ),
          SizedBox(height: 8),
          Text(
            'Logs will appear here after sync operations',
            style: TextStyle(fontSize: 14, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildLogsList(List<LogEntry> logs) {
    // Reverse the logs to show newest first
    final reversedLogs = logs.reversed.toList();

    return SliverPadding(
      padding: const EdgeInsets.all(8.0),
      sliver: SliverList(
        delegate: SliverChildBuilderDelegate((context, index) {
          final logEntry = reversedLogs[index];

          return Card(
            elevation: 1,
            margin: const EdgeInsets.symmetric(vertical: 4.0),
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        logEntry.formattedTimestamp,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: _getLogLevelColor(
                            logEntry.level,
                          ).withAlpha(25),
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(
                            color: _getLogLevelColor(
                              logEntry.level,
                            ).withAlpha(128),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          logEntry.level.label,
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                            color: _getLogLevelColor(logEntry.level),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    logEntry.message,
                    style: TextStyle(
                      fontSize: 14,
                      color: _getMessageColor(logEntry.message, logEntry.level),
                    ),
                  ),
                ],
              ),
            ),
          );
        }, childCount: reversedLogs.length),
      ),
    );
  }

  Color _getLogLevelColor(LogLevel level) {
    switch (level) {
      case LogLevel.debug:
        return Colors.grey;
      case LogLevel.info:
        return Colors.blue;
      case LogLevel.warning:
        return Colors.orange;
      case LogLevel.error:
        return Colors.red;
    }
  }

  Color _getMessageColor(String message, LogLevel level) {
    // Base color on log level
    switch (level) {
      case LogLevel.debug:
        return Colors.grey[700]!;
      case LogLevel.info:
        if (message.contains('completed successfully') ||
            message.contains('Successfully')) {
          return Colors.green;
        }
        return Colors.black87;
      case LogLevel.warning:
        return Colors.orange[700]!;
      case LogLevel.error:
        return Colors.red;
    }
  }

  /// Builds the app bar with log level selector
  SliverAppBar _buildAppBar(
    BuildContext context,
    WidgetRef ref,
    dynamic syncService,
    LogLevel logLevelFilter,
  ) {
    return SliverAppBar(
      floating: true,
      pinned: true,
      elevation: 0,
      backgroundColor: AppColors.primary,
      title: const Text('Sync Logs'),
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(60),
        child: Padding(
          padding: const EdgeInsets.fromLTRB(16.0, 4.0, 16.0, 12.0),
          child: _buildLogLevelSelector(context, ref, logLevelFilter),
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.delete_sweep),
          tooltip: 'Clear logs',
          onPressed: () {
            _showClearLogsConfirmation(context, syncService);
          },
        ),
      ],
    );
  }

  /// Builds the log level selector widget
  Widget _buildLogLevelSelector(
    BuildContext context,
    WidgetRef ref,
    LogLevel logLevel,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withAlpha(30),
        borderRadius: BorderRadius.circular(8),
      ),
      child: LogLevelSelectorField(
        logLevel: logLevel,
        onLogLevelSelected: (newLevel) {
          ref.read(logLevelFilterProvider.notifier).state = newLevel;
        },
      ),
    );
  }

  void _showClearLogsConfirmation(BuildContext context, dynamic syncService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Logs'),
        content: const Text('Are you sure you want to clear all sync logs?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () {
              // Clear the logs
              syncService.clearLogs();
              Navigator.of(context).pop();
            },
            child: const Text('CLEAR'),
          ),
        ],
      ),
    );
  }
}

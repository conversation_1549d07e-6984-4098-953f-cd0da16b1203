import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/presentation/widgets/sync_status_widget.dart';
import '../../../../core/services/auth_service.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_dimensions.dart';
import '../../../auth/presentation/screens/login_screen.dart';
import '../../../backup/presentation/screens/backup_restore_screen.dart';
import '../../../levels/presentation/screens/levels_screen.dart';
import '../../../settings/presentation/screens/app_settings_screen.dart';
import '../../../spare_parts/presentation/screens/spare_parts_screen.dart';
import '../screens/app_logs_screen.dart';
import '../screens/sync_screen_wrapper.dart';
import '../widgets/about_dialog.dart';
import '../widgets/footer.dart';
import '../widgets/menu_card.dart';
import '../widgets/menu_divider.dart';
import '../widgets/menu_item.dart';
import '../widgets/section_header.dart';
import '../widgets/user_info_card.dart';

/// Screen that displays additional options and features
class MoreScreen extends ConsumerWidget {
  const MoreScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(appBar: _buildAppBar(), body: _buildBody(context, ref));
  }

  /// Builds the app bar for the screen
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      elevation: 0,
      backgroundColor: AppColors.primary,
      title: const Text('More Options'),
      actions: const [
        Padding(
          padding: EdgeInsets.only(right: 16.0),
          child: SyncStatusWidget(mini: true),
        ),
      ],
    );
  }

  /// Builds the main body of the screen
  Widget _buildBody(BuildContext context, WidgetRef ref) {
    return ListView(
      padding: const EdgeInsets.fromLTRB(16.0, 8.0, 16.0, 24.0),
      children: [
        // User Info Card at the top
        const UserInfoCard(),

        SizedBox(height: AppDimensions.spacing24),

        // Main Features Section
        const MoreSectionHeader(title: 'Features', icon: Icons.star),
        const SizedBox(height: 12),

        // Main Features Card
        _buildFeaturesCard(context, ref),

        SizedBox(height: AppDimensions.spacing24),

        // Settings Section
        const MoreSectionHeader(
          title: 'Settings & Support',
          icon: Icons.settings,
        ),
        const SizedBox(height: 12),

        // Settings Card
        _buildSettingsCard(context),

        SizedBox(height: AppDimensions.spacing24),

        // Footer text
        const AppFooter(),
      ],
    );
  }

  /// Builds the features card with menu items
  Widget _buildFeaturesCard(BuildContext context, WidgetRef ref) {
    return MenuCard(
      children: [
        MenuItem(
          icon: Icons.military_tech,
          title: 'Driver Level',
          subtitle: 'View your current level and requirements',
          iconColor: AppColors.primary,
          onTap: () => _navigateTo(context, const LevelsScreen()),
        ),
        const MenuDivider(),
        MenuItem(
          icon: Icons.build,
          title: 'Spare Parts',
          subtitle: 'Track and manage vehicle maintenance',
          iconColor: AppColors.primary,
          onTap: () => _navigateTo(context, const EnhancedSparePartsScreen()),
        ),
        const MenuDivider(),
        MenuItem(
          icon: Icons.cloud_sync,
          title: 'Cloud Sync',
          subtitle: 'Synchronize your data across devices',
          iconColor: AppColors.primary,
          onTap: () => _navigateTo(context, const SyncScreenWrapper()),
          trailing: const SyncStatusWidget(mini: true),
        ),
        const MenuDivider(),
        MenuItem(
          icon: Icons.backup,
          title: 'Backup & Restore',
          subtitle: 'Secure your data and transfer between devices',
          iconColor: AppColors.primary,
          onTap: () => _navigateTo(context, const BackupRestoreScreen()),
        ),
      ],
    );
  }

  /// Builds the settings card with menu items
  Widget _buildSettingsCard(BuildContext context) {
    return MenuCard(
      children: [
        MenuItem(
          icon: Icons.settings,
          title: 'App Settings',
          subtitle: 'Configure app preferences',
          iconColor: AppColors.primary,
          onTap: () => _navigateTo(context, const AppSettingsScreen()),
        ),
        const MenuDivider(),
        MenuItem(
          icon: Icons.history,
          title: 'App Logs',
          subtitle: 'View sync and authentication logs',
          iconColor: AppColors.primary,
          onTap: () => _navigateTo(context, const AppLogsScreen()),
        ),
        const MenuDivider(),
        MenuItem(
          icon: Icons.info_outline,
          title: 'About',
          subtitle: 'App information and credits',
          iconColor: AppColors.primary,
          onTap: () => AboutAppDialog.show(context),
        ),
        const MenuDivider(),
        // Conditionally show Login or Logout based on authentication state
        Consumer(
          builder: (context, ref, child) {
            final authState = ref.watch(authStateProvider);

            // Show Login button if not authenticated
            if (!authState.isAuthenticated) {
              return MenuItem(
                icon: Icons.login,
                title: 'Login',
                subtitle: 'Sign in to enable cloud sync',
                iconColor: AppColors.primary,
                onTap: () => _navigateTo(context, const LoginScreen()),
              );
            }

            // Show Logout button if authenticated
            return MenuItem(
              icon: Icons.logout,
              title: 'Logout',
              subtitle: 'Sign out of your account',
              iconColor: Colors.red,
              onTap: () async {
                // Show confirmation dialog
                final shouldLogout =
                    await showDialog<bool>(
                      context: context,
                      builder: (context) => AlertDialog(
                        title: const Text('Logout'),
                        content: const Text(
                          'Are you sure you want to logout?\n\nYou can still use the app without being logged in, but cloud sync will be disabled.',
                        ),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(false),
                            child: const Text('Cancel'),
                          ),
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(true),
                            child: const Text('Logout'),
                          ),
                        ],
                      ),
                    ) ??
                    false;

                if (shouldLogout) {
                  // Logout user
                  await ref.read(authServiceProvider).signOut();
                }
              },
            );
          },
        ),
      ],
    );
  }

  /// Helper method to navigate to a screen
  void _navigateTo(BuildContext context, Widget screen) {
    Navigator.of(context).push(MaterialPageRoute(builder: (context) => screen));
  }
}

import 'package:flutter/material.dart';
import '../../../../core/theme/app_dimensions.dart';

/// A card widget for grouping menu items
class MenuCard extends StatelessWidget {
  final List<Widget> children;
  final EdgeInsetsGeometry margin;
  final double borderRadius;

  const MenuCard({
    super.key,
    required this.children,
    this.margin = const EdgeInsets.only(bottom: 12.0),
    this.borderRadius = 12.0,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: margin,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius),
        side: BorderSide(
          color: Colors.grey.withAlpha(30),
          width: AppDimensions.borderWidth,
        ),
      ),
      child: Column(children: children),
    );
  }
}

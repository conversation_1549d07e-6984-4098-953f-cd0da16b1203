import 'package:flutter/material.dart';

/// A divider widget for menu items
class MenuDivider extends StatelessWidget {
  final double leftPadding;
  final double rightPadding;
  final double height;
  final Color? color;

  const MenuDivider({
    super.key,
    this.leftPadding = 56,
    this.rightPadding = 16,
    this.height = 1,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: leftPadding, right: rightPadding),
      child: Divider(height: height, color: color ?? Colors.grey.withAlpha(30)),
    );
  }
}

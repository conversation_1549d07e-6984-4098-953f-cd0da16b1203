import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_dimensions.dart';

/// A reusable section header widget for the More screen
class MoreSectionHeader extends StatelessWidget {
  final String title;
  final IconData icon;

  const MoreSectionHeader({super.key, required this.title, required this.icon});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 4.0, bottom: 4.0),
      child: Row(
        children: [
          Icon(
            icon,
            color: AppColors.primary,
            size: AppDimensions.iconSizeSmall,
          ),
          SizedBox(width: AppDimensions.spacing8),
          Text(
            title,
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }
}

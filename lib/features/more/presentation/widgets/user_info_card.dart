import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/services/auth_service.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_dimensions.dart';

/// A card widget displaying user information
class UserInfoCard extends ConsumerWidget {
  const UserInfoCard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authStateProvider);
    final user = authState.user;

    return Card(
      margin: const EdgeInsets.only(top: 12),
      elevation: 0,
      color: AppColors.primary.withAlpha(15),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.cardRadius),
        side: BorderSide(
          color: AppColors.primary.withAlpha(30),
          width: AppDimensions.borderWidth,
        ),
      ),
      child: Padding(
        padding: AppDimensions.cardPadding,
        child: Row(
          children: [
            _buildUserAvatar(user),
            SizedBox(width: AppDimensions.spacing16),
            Expanded(child: _buildUserInfo(context, user)),
          ],
        ),
      ),
    );
  }

  /// Builds the user avatar
  Widget _buildUserAvatar(User? user) {
    // If user has a name, use the first letter as avatar
    final String avatarText = user?.email != null
        ? user!.email![0].toUpperCase()
        : AppConstants.appName[0];

    return CircleAvatar(
      radius: 28,
      backgroundColor: AppColors.primary,
      child: Text(
        avatarText,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 24,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// Builds the user information section
  Widget _buildUserInfo(BuildContext context, user) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // User email or app name
        Text(
          user?.email ?? AppConstants.appName,
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),

        // User ID or app version
        Text(
          user != null
              ? 'User ID: ${_formatUserId(user.id)}'
              : 'Version ${AppConstants.appVersion}',
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 8),

        // Account status
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: user != null
                ? Colors.green.withAlpha(30)
                : Colors.orange.withAlpha(30),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            user != null
                ? 'Logged In - Cloud Sync Available'
                : 'Offline Mode - Login for Cloud Sync',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w500,
              color: user != null ? Colors.green[700] : Colors.orange[700],
            ),
          ),
        ),
      ],
    );
  }

  /// Format user ID to show only first and last few characters
  String _formatUserId(String id) {
    if (id.length <= 8) return id;
    return '${id.substring(0, 4)}...${id.substring(id.length - 4)}';
  }
}

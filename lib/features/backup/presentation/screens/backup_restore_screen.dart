import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../core/theme/app_colors.dart';
import 'tabs/backup_tab.dart';
import 'tabs/restore_tab.dart';

class BackupRestoreScreen extends ConsumerStatefulWidget {
  const BackupRestoreScreen({super.key});

  @override
  ConsumerState<BackupRestoreScreen> createState() =>
      _BackupRestoreScreenState();
}

class _BackupRestoreScreenState extends ConsumerState<BackupRestoreScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppColors.primary,
        title: const Text('Backup & Restore'),
        bottom: TabBar(
          controller: _tabController,
          labelStyle: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontSize: 14.0,
            fontWeight: FontWeight.bold,
          ),
          unselectedLabelStyle: Theme.of(context).textTheme.labelLarge
              ?.copyWith(fontSize: 14.0, fontWeight: FontWeight.normal),
          indicatorWeight: 3.0,
          indicatorSize: TabBarIndicatorSize.tab,
          indicatorColor: Colors.white,
          dividerColor: Colors.transparent,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(
              icon: Icon(Icons.backup, size: 24, color: Colors.white),
              text: 'Backup',
            ),
            Tab(
              icon: Icon(Icons.restore, size: 24, color: Colors.white),
              text: 'Restore',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: const [BackupTab(), RestoreTab()],
      ),
    );
  }
}

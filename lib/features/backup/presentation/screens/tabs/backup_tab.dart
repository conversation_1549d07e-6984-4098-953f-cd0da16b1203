import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../core/services/permission_service.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../domain/entities/backup_info.dart';
import '../../providers/backup_providers.dart';
import '../../widgets/backup_actions_bottom_sheet.dart';
import '../../widgets/backup_card_item.dart';
import '../../widgets/backup_info_card.dart';
import '../../widgets/backup_info_row.dart';
import '../../widgets/empty_backup_card.dart';
import '../../widgets/info_card.dart';
import '../../widgets/loading_indicator.dart';
import '../../widgets/status_card.dart';

class BackupTab extends ConsumerWidget {
  const BackupTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final backupOperation = ref.watch(backupOperationProvider);
    final backupsAsync = ref.watch(availableBackupsProvider);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Info card
          InfoCard(
            backgroundColor: AppColors.primary.withAlpha(15),
            borderColor: AppColors.primary.withAlpha(30),
            icon: Icons.cloud_upload,
            iconColor: AppColors.primary,
            title: 'Backup Information',
            actionButton: SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                icon: const Icon(Icons.backup, size: 18),
                label: const Text('Create Backup'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  elevation: 0,
                ),
                onPressed: backupOperation.isLoading
                    ? null
                    : () => _createBackup(context, ref),
              ),
            ),
            children: [
              Text(
                'Backup creates a copy of all your data including income records, orders, performance metrics, and spare parts information.',
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
              ),
              const SizedBox(height: 8),
              Text(
                'Backups are stored on your device and can be used to restore your data if needed.',
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
              ),
            ],
          ),

          if (backupOperation.isLoading)
            const LoadingIndicator(message: 'Creating backup...'),

          if (backupOperation.hasError)
            StatusCard(
              backgroundColor: Colors.white,
              borderColor: AppColors.error.withAlpha(30),
              icon: Icons.error_outline,
              iconColor: AppColors.error,
              title: 'Backup Error',
              message: 'Error: ${backupOperation.error}',
            ),

          if (backupOperation.hasValue && backupOperation.value != null)
            StatusCard(
              backgroundColor: Colors.white,
              borderColor: AppColors.success.withAlpha(30),
              icon: Icons.check_circle,
              iconColor: AppColors.success,
              title: 'Backup Created Successfully',
              children: [
                BackupInfoRow(
                  label: 'File',
                  value: backupOperation.value!.fileName,
                  icon: Icons.insert_drive_file,
                ),
                const SizedBox(height: 8),
                BackupInfoRow(
                  label: 'Size',
                  value: backupOperation.value!.formattedSize,
                  icon: Icons.data_usage,
                ),
                const SizedBox(height: 8),
                BackupInfoRow(
                  label: 'Date',
                  value: backupOperation.value!.formattedDate,
                  icon: Icons.calendar_today,
                ),
              ],
            ),

          const SizedBox(height: 32),

          // Recent backups header
          Row(
            children: [
              const Icon(Icons.history, color: AppColors.info, size: 18),
              const SizedBox(width: 8),
              Text(
                'Recent Backups',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Backup list
          _buildBackupList(context, ref, backupsAsync),
        ],
      ),
    );
  }

  Widget _buildBackupList(
    BuildContext context,
    WidgetRef ref,
    AsyncValue<List<BackupInfo>> backupsAsync,
  ) {
    return backupsAsync.when(
      data: (backups) {
        if (backups.isEmpty) {
          return EmptyBackupCard(
            title: 'No Backups Found',
            message:
                'Create your first backup by tapping the "Create Backup" button above',
            actionButton: ElevatedButton.icon(
              onPressed: () => _createBackup(context, ref),
              icon: const Icon(Icons.backup, size: 18),
              label: const Text('Create Backup Now'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          );
        }

        return ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: backups.length,
          itemBuilder: (context, index) {
            final backup = backups[index];
            return BackupCardItem(
              backup: backup,
              onLongPress: () => _showActionsBottomSheet(context, ref, backup),
            );
          },
        );
      },
      loading: () => const LoadingIndicator(message: 'Loading backups...'),
      error: (error, stack) => StatusCard(
        backgroundColor: Colors.white,
        borderColor: AppColors.error.withAlpha(30),
        icon: Icons.error_outline,
        iconColor: AppColors.error,
        title: 'Error',
        message: 'Error: $error',
      ),
    );
  }

  void _showActionsBottomSheet(
    BuildContext context,
    WidgetRef ref,
    BackupInfo backupInfo,
  ) {
    BackupActionsBottomSheet.show(
      context: context,
      title: 'Backup Options',
      subtitle: 'File: ${backupInfo.fileName}',
      onDelete: () => _showDeleteConfirmationDialog(context, ref, backupInfo),
      itemIcon: Icons.backup,
    );
  }

  Future<void> _showDeleteConfirmationDialog(
    BuildContext context,
    WidgetRef ref,
    BackupInfo backupInfo,
  ) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Delete'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Are you sure you want to delete this backup?',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            BackupInfoCard(backupInfo: backupInfo),
            const SizedBox(height: 12),
            const Row(
              children: [
                Icon(Icons.warning, color: AppColors.error, size: 16),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'This action cannot be undone.',
                    style: TextStyle(
                      color: AppColors.error,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: AppColors.error),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    await ref
        .read(deleteBackupOperationProvider.notifier)
        .deleteBackup(backupInfo.filePath);

    if (!context.mounted) return;

    if (ref.read(deleteBackupOperationProvider).hasValue &&
        ref.read(deleteBackupOperationProvider).value == true) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Backup deleted successfully'),
          backgroundColor: AppColors.success,
          behavior: SnackBarBehavior.floating,
        ),
      );
    } else if (ref.read(deleteBackupOperationProvider).hasError) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Error: ${ref.read(deleteBackupOperationProvider).error}',
          ),
          backgroundColor: AppColors.error,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  Future<void> _createBackup(BuildContext context, WidgetRef ref) async {
    await ref.read(backupOperationProvider.notifier).createBackup();

    if (!context.mounted) return;

    if (ref.read(backupOperationProvider).hasValue &&
        ref.read(backupOperationProvider).value != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Backup created successfully'),
          backgroundColor: AppColors.success,
          behavior: SnackBarBehavior.floating,
        ),
      );
    } else if (ref.read(backupOperationProvider).hasError) {
      final error = ref.read(backupOperationProvider).error.toString();

      // Check if this is a permission error
      if (error.contains('permission') || error.contains('Permission')) {
        // Show permission settings dialog
        await PermissionService.showPermissionSettingsDialog(context);
      } else {
        // Show regular error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error_outline, color: Colors.white, size: 20),
                const SizedBox(width: 12),
                Expanded(child: Text('Error: $error')),
              ],
            ),
            backgroundColor: AppColors.error,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    }
  }
}

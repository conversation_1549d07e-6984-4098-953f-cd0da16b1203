import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../core/services/permission_service.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../domain/entities/backup_info.dart';
import '../../providers/backup_providers.dart';
import '../../widgets/backup_card_item.dart';
import '../../widgets/backup_info_card.dart';
import '../../widgets/empty_backup_card.dart';
import '../../widgets/info_card.dart';
import '../../widgets/loading_indicator.dart';
import '../../widgets/status_card.dart';

class RestoreTab extends ConsumerWidget {
  const RestoreTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final restoreOperation = ref.watch(restoreOperationProvider);
    final backupsAsync = ref.watch(availableBackupsProvider);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Info card
          InfoCard(
            backgroundColor: AppColors.warning.withAlpha(15),
            borderColor: AppColors.warning.withAlpha(30),
            icon: Icons.warning_amber,
            iconColor: AppColors.warning,
            title: 'Restore Information',
            actionButton: SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                icon: const Icon(Icons.refresh, size: 18),
                label: const Text('Refresh Backup List'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  elevation: 0,
                ),
                onPressed: restoreOperation.isLoading
                    ? null
                    : () {
                        ref.invalidate(availableBackupsProvider);
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Backup list refreshed'),
                            behavior: SnackBarBehavior.floating,
                          ),
                        );
                      },
              ),
            ),
            children: [
              Text(
                'Restoring will replace all current data with the data from the selected backup.',
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
              ),
              const SizedBox(height: 12),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Icon(Icons.warning, color: AppColors.warning, size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Warning: This action cannot be undone. Make sure to create a backup of your current data first.',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.warning,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),

          if (restoreOperation.isLoading)
            const LoadingIndicator(message: 'Restoring data...'),

          if (restoreOperation.hasError)
            StatusCard(
              backgroundColor: Colors.white,
              borderColor: AppColors.error.withAlpha(30),
              icon: Icons.error_outline,
              iconColor: AppColors.error,
              title: 'Error',
              message: 'Error: ${restoreOperation.error}',
            ),

          if (restoreOperation.hasValue && restoreOperation.value == true)
            StatusCard(
              backgroundColor: Colors.white,
              borderColor: AppColors.success.withAlpha(30),
              icon: Icons.check,
              iconColor: AppColors.success,
              title: 'Data Restored Successfully',
              message: 'Your data has been restored from the selected backup.',
            ),

          const SizedBox(height: 32),

          // Available backups header
          Row(
            children: [
              const Icon(Icons.restore, color: AppColors.secondary, size: 18),
              const SizedBox(width: 8),
              Text(
                'Available Backups',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Backup list
          _buildBackupList(context, ref, backupsAsync),
        ],
      ),
    );
  }

  Widget _buildBackupList(
    BuildContext context,
    WidgetRef ref,
    AsyncValue<List<BackupInfo>> backupsAsync,
  ) {
    return backupsAsync.when(
      data: (backups) {
        if (backups.isEmpty) {
          return const Center(
            child: EmptyBackupCard(
              title: 'No Backups Found',
              message: 'Create a backup first by going to the Backup tab',
            ),
          );
        }

        return ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: backups.length,
          itemBuilder: (context, index) {
            final backup = backups[index];
            return BackupCardItem(
              backup: backup,
              leadingIcon: Icons.restore,
              iconColor: AppColors.secondary,
              trailingIcon: Icons.arrow_forward,
              onTap: () => _showRestoreConfirmationDialog(context, ref, backup),
            );
          },
        );
      },
      loading: () => const LoadingIndicator(message: 'Loading backups...'),
      error: (error, stack) => StatusCard(
        backgroundColor: Colors.white,
        borderColor: AppColors.error.withAlpha(30),
        icon: Icons.error_outline,
        iconColor: AppColors.error,
        title: 'Error',
        message: 'Error: $error',
      ),
    );
  }

  Future<void> _showRestoreConfirmationDialog(
    BuildContext context,
    WidgetRef ref,
    BackupInfo backupInfo,
  ) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Restore'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Are you sure you want to restore this backup? This will replace all current data.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            BackupInfoCard(backupInfo: backupInfo),
            const SizedBox(height: 12),
            const Row(
              children: [
                Icon(Icons.warning, color: AppColors.warning, size: 16),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'This action will replace all your current data. Make sure you have a backup of your current data if needed.',
                    style: TextStyle(
                      color: AppColors.warning,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: AppColors.secondary),
            child: const Text('Restore'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    await ref
        .read(restoreOperationProvider.notifier)
        .restoreFromBackup(backupInfo.filePath);

    if (!context.mounted) return;

    if (ref.read(restoreOperationProvider).hasValue &&
        ref.read(restoreOperationProvider).value == true) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Data restored successfully'),
          backgroundColor: AppColors.success,
          behavior: SnackBarBehavior.floating,
        ),
      );
    } else if (ref.read(restoreOperationProvider).hasError) {
      final error = ref.read(restoreOperationProvider).error.toString();

      // Check if this is a permission error
      if (error.contains('permission') || error.contains('Permission')) {
        // Show permission settings dialog
        await PermissionService.showPermissionSettingsDialog(context);
      } else {
        // Show regular error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error_outline, color: Colors.white, size: 20),
                const SizedBox(width: 12),
                Expanded(child: Text('Error: $error')),
              ],
            ),
            backgroundColor: AppColors.error,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    }
  }
}

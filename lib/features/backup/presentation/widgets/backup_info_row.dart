import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';

class BackupInfoRow extends StatelessWidget {
  final String label;
  final String value;
  final IconData icon;
  final TextStyle? labelStyle;
  final TextStyle? valueStyle;

  const BackupInfoRow({
    super.key,
    required this.label,
    required this.value,
    required this.icon,
    this.labelStyle,
    this.valueStyle,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Icon(icon, size: 16, color: AppColors.textSecondary),
        const SizedBox(width: 8),
        Text(
          '$label:',
          style:
              labelStyle ??
              Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            value,
            style:
                valueStyle ??
                Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.w600),
          ),
        ),
      ],
    );
  }
}

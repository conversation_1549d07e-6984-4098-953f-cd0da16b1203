import 'package:flutter/material.dart';
import '../../domain/entities/backup_info.dart';
import 'backup_info_row.dart';

class BackupInfoCard extends StatelessWidget {
  final BackupInfo backupInfo;

  const BackupInfoCard({super.key, required this.backupInfo});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      color: Colors.grey.shade50,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(color: Colors.grey.withAlpha(30), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            BackupInfoRow(
              label: 'Backup',
              value: backupInfo.fileName,
              icon: Icons.insert_drive_file,
            ),
            const SizedBox(height: 8),
            BackupInfoRow(
              label: 'Date',
              value: backupInfo.formattedDate,
              icon: Icons.calendar_today,
            ),
            const SizedBox(height: 8),
            BackupInfoRow(
              label: 'Size',
              value: backupInfo.formattedSize,
              icon: Icons.data_usage,
            ),
          ],
        ),
      ),
    );
  }
}

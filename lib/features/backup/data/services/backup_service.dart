import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:intl/intl.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sqflite/sqflite.dart';

import '../../../../core/datasources/app_database.dart';
import '../../../../core/services/permission_service.dart';
import '../../domain/entities/backup_info.dart';

class BackupService {
  final AppDatabase database;

  BackupService({required this.database});

  /// Check if we have the necessary permissions for backup/restore operations
  Future<bool> checkAndRequestPermissions() async {
    // Get Android SDK version to handle Android 10 specifically
    int sdkVersion = 0;
    if (Platform.isAndroid) {
      final deviceInfo = DeviceInfoPlugin();
      final androidInfo = await deviceInfo.androidInfo;
      sdkVersion = androidInfo.version.sdkInt;
    }

    // For Android 10, we need to handle permissions differently
    if (Platform.isAndroid && sdkVersion == 29) {
      // For Android 10, we'll use the basic storage permission
      // and then use SAF for specific folder access
      final storageStatus = await Permission.storage.status;

      if (storageStatus.isDenied) {
        final result = await Permission.storage.request();
        return result.isGranted;
      }

      return storageStatus.isGranted;
    } else {
      // For other Android versions, use the standard permission request
      return await PermissionService.requestStoragePermissions();
    }
  }

  // Create backup of the database
  Future<BackupInfo?> createBackup() async {
    try {
      // Get database file - try both possible locations
      final appDir = await getApplicationDocumentsDirectory();
      final appDbPath = join(appDir.path, 'BidTrakr.db');
      final appDbFile = File(appDbPath);

      final dbFolder = await getDatabasesPath();
      final sqfliteDbPath = join(dbFolder, 'BidTrakr.db');
      final sqfliteDbFile = File(sqfliteDbPath);

      // Check which database file exists
      File dbFile;

      if (appDbFile.existsSync()) {
        dbFile = appDbFile;
      } else if (sqfliteDbFile.existsSync()) {
        dbFile = sqfliteDbFile;
      } else {
        return null;
      }

      // Get the backup directory
      final backupDir = await _getBackupDirectory();

      // If we couldn't get a backup directory, return null
      if (backupDir == null) {
        return null;
      }

      // Create timestamp for backup filename
      final timestamp = DateFormat('yyyyMMdd_HHmmss').format(DateTime.now());
      final backupFileName = 'BidTrakr_backup_$timestamp.db';

      // Create backup folder if it doesn't exist
      if (!backupDir.existsSync()) {
        backupDir.createSync(recursive: true);
      }

      final backupPath = join(backupDir.path, backupFileName);
      final backupFile = File(backupPath);

      // Create a temporary backup first to avoid database locking issues
      final tempDir = await getTemporaryDirectory();
      final tempBackupPath = join(tempDir.path, backupFileName);
      final tempBackupFile = File(tempBackupPath);

      // Create a temporary file
      await tempBackupFile.create();

      // Copy the database file to the temp location
      await _copyDatabaseFile(dbFile.path, tempBackupPath);

      // Then copy to final location
      await tempBackupFile.copy(backupPath);

      // Clean up temp file
      await tempBackupFile.delete();

      // Get file size
      final fileSize = await backupFile.length();

      return BackupInfo(
        fileName: backupFileName,
        createdAt: DateTime.now(),
        filePath: backupPath,
        fileSize: fileSize,
      );
    } catch (e) {
      return null;
    }
  }

  // Restore database from backup
  Future<bool> restoreBackup(String backupPath) async {
    try {
      // Check if backup file exists
      final backupFile = File(backupPath);
      if (!backupFile.existsSync()) {
        return false;
      }

      // Get database file - try both possible locations
      final appDir = await getApplicationDocumentsDirectory();
      final appDbPath = join(appDir.path, 'BidTrakr.db');
      final appDbFile = File(appDbPath);

      final dbFolder = await getDatabasesPath();
      final sqfliteDbPath = join(dbFolder, 'BidTrakr.db');
      final sqfliteDbFile = File(sqfliteDbPath);

      // Determine which database path to use
      String dbPath;

      // If the app database file exists, use that path
      if (appDbFile.existsSync()) {
        dbPath = appDbPath;
      } else if (sqfliteDbFile.existsSync()) {
        // If the SQLite database file exists, use that path
        dbPath = sqfliteDbPath;
      } else {
        // If neither exists, prefer the app documents path
        dbPath = appDbPath;
      }

      // Create a temporary copy of the backup file
      final tempDir = await getTemporaryDirectory();
      final tempRestorePath = join(
        tempDir.path,
        'temp_restore_${DateTime.now().millisecondsSinceEpoch}.db',
      );
      final tempRestoreFile = File(tempRestorePath);

      try {
        // Make sure the parent directory exists
        final dbDir = Directory(dirname(dbPath));
        if (!dbDir.existsSync()) {
          dbDir.createSync(recursive: true);
        }

        // Create the temporary file
        await tempRestoreFile.create();

        // Copy backup to temp location using our helper method
        await _copyDatabaseFile(backupFile.path, tempRestorePath);

        // Copy temp file to database location using our helper method
        await _copyDatabaseFile(tempRestorePath, dbPath);

        // Clean up temp file
        try {
          await tempRestoreFile.delete();
        } catch (e) {
          // Ignore errors when deleting temp file
        }

        // Verify the copy was successful
        final dbFile = File(dbPath);
        if (!dbFile.existsSync()) {
          return false;
        }

        return true;
      } catch (e) {
        // Try an alternative approach if the first one fails
        try {
          // Make sure the parent directory exists
          final dbDir = Directory(dirname(dbPath));
          if (!dbDir.existsSync()) {
            dbDir.createSync(recursive: true);
          }

          // Copy backup directly to database location using our helper method
          await _copyDatabaseFile(backupFile.path, dbPath);

          // Verify the copy was successful
          final dbFile = File(dbPath);
          if (!dbFile.existsSync()) {
            return false;
          }

          return true;
        } catch (e) {
          return false;
        }
      }
    } catch (e) {
      return false;
    }
  }

  // Get list of available backups
  Future<List<BackupInfo>> getAvailableBackups() async {
    try {
      final backupInfos = <BackupInfo>[];

      // Get backup directory
      final backupDir = await _getBackupDirectory();

      // Check the backup directory if it's available
      if (backupDir != null) {
        if (!backupDir.existsSync()) {
          // Try to create the directory
          try {
            backupDir.createSync(recursive: true);
          } catch (e) {
            return [];
          }
        }

        try {
          // List files in backup directory
          final files = await backupDir.list().toList();
          final backupFiles = files
              .whereType<File>()
              .where(
                (file) =>
                    file.path.endsWith('.db') &&
                    (file.path.contains('BidTrakr_backup_') ||
                        file.path.contains('bidtrakr_backup_')),
              )
              .toList();

          // Add backups to the list
          for (final file in backupFiles) {
            try {
              final fileName = basename(file.path);
              final fileStats = file.statSync();
              final createdAt = fileStats.modified;
              final fileSize = fileStats.size;

              backupInfos.add(
                BackupInfo(
                  fileName: fileName,
                  createdAt: createdAt,
                  filePath: file.path,
                  fileSize: fileSize,
                ),
              );
            } catch (e) {
              // Skip this file
            }
          }
        } catch (e) {
          return [];
        }
      } else {
        return [];
      }

      // Sort by creation date (newest first)
      backupInfos.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      return backupInfos;
    } catch (e) {
      return [];
    }
  }

  // Delete backup file
  Future<bool> deleteBackup(String backupPath) async {
    try {
      // Delete file
      final file = File(backupPath);
      await file.delete();
      return true;
    } catch (e) {
      return false;
    }
  }

  // Constants for SharedPreferences
  static const String _backupDirKey = 'backup_directory_path';

  // Set custom backup directory
  Future<bool> setCustomBackupDirectory(String path) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setString(_backupDirKey, path);
    } catch (e) {
      return false;
    }
  }

  // Clear custom backup directory
  Future<bool> clearCustomBackupDirectory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.remove(_backupDirKey);
    } catch (e) {
      return false;
    }
  }

  // Get custom backup directory
  Future<String?> getCustomBackupDirectory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_backupDirKey);
    } catch (e) {
      return null;
    }
  }

  // Helper method to copy database file without locking issues
  Future<void> _copyDatabaseFile(
    String sourcePath,
    String destinationPath,
  ) async {
    try {
      // Use direct file operations to copy the database
      final sourceFile = File(sourcePath);
      final destinationFile = File(destinationPath);

      // Read the source file in chunks to avoid memory issues with large databases
      final sourceStream = sourceFile.openRead();
      final destinationSink = destinationFile.openWrite();

      // Copy the file
      await sourceStream.pipe(destinationSink);

      // Close the sink
      await destinationSink.flush();
      await destinationSink.close();
    } catch (e) {
      // Fallback to direct copy
      final sourceFile = File(sourcePath);
      await sourceFile.copy(destinationPath);
    }
  }

  // Get backup directory
  Future<Directory?> _getBackupDirectory() async {
    try {
      // Check if a custom directory is set in SharedPreferences
      final customPath = await getCustomBackupDirectory();
      if (customPath != null) {
        // Check if we have permission to write to this directory
        final hasPermission = await PermissionService.canWriteToDirectory(
          customPath,
        );

        if (!hasPermission) {
          // Fall back to default directory
        } else {
          final customDir = Directory(customPath);

          // Create the directory if it doesn't exist
          if (!customDir.existsSync()) {
            customDir.createSync(recursive: true);
          }

          return customDir;
        }
      }
    } catch (e) {
      // Continue with default directory
    }

    // Use default directory if no custom directory is set or if we don't have permission
    Directory backupDir;

    // Use app-specific directory which doesn't require special permissions
    final appDir = await getApplicationDocumentsDirectory();
    backupDir = Directory('${appDir.path}/backups');

    // Create the directory if it doesn't exist
    if (!backupDir.existsSync()) {
      backupDir.createSync(recursive: true);
    }

    return backupDir;
  }
}

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'level_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$levelRequirementsHash() => r'ca2ac26728a68ed6fdd3396394505e289b00fcd3';

/// See also [levelRequirements].
@ProviderFor(levelRequirements)
final levelRequirementsProvider =
    AutoDisposeFutureProvider<LevelRequirements>.internal(
      levelRequirements,
      name: r'levelRequirementsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$levelRequirementsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef LevelRequirementsRef = AutoDisposeFutureProviderRef<LevelRequirements>;
String _$evaluateLevelHash() => r'5e491de62746b557ea86d0697fcb6add74e9bbbe';

/// See also [evaluateLevel].
@ProviderFor(evaluateLevel)
final evaluateLevelProvider = AutoDisposeProvider<EvaluateLevel>.internal(
  evaluateLevel,
  name: r'evaluateLevelProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$evaluateLevelHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef EvaluateLevelRef = AutoDisposeProviderRef<EvaluateLevel>;
String _$driverStatusHash() => r'6e646ad2b3f03e952fa1eb03a4afbfd993a8ca08';

/// See also [driverStatus].
@ProviderFor(driverStatus)
final driverStatusProvider = AutoDisposeFutureProvider<DriverStatus>.internal(
  driverStatus,
  name: r'driverStatusProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$driverStatusHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef DriverStatusRef = AutoDisposeFutureProviderRef<DriverStatus>;
String _$nextLevelRequirementsHash() =>
    r'd5f7976c118b5504ef94bd467d45109f6c088984';

/// See also [nextLevelRequirements].
@ProviderFor(nextLevelRequirements)
final nextLevelRequirementsProvider =
    AutoDisposeFutureProvider<Map<String, dynamic>>.internal(
      nextLevelRequirements,
      name: r'nextLevelRequirementsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$nextLevelRequirementsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef NextLevelRequirementsRef =
    AutoDisposeFutureProviderRef<Map<String, dynamic>>;
String _$allUpcomingLevelRequirementsHash() =>
    r'7dfce94c39d7319fe285e00b41a41546fbc39e14';

/// See also [allUpcomingLevelRequirements].
@ProviderFor(allUpcomingLevelRequirements)
final allUpcomingLevelRequirementsProvider =
    AutoDisposeFutureProvider<List<Map<String, dynamic>>>.internal(
      allUpcomingLevelRequirements,
      name: r'allUpcomingLevelRequirementsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$allUpcomingLevelRequirementsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AllUpcomingLevelRequirementsRef =
    AutoDisposeFutureProviderRef<List<Map<String, dynamic>>>;
String _$levelRequirementsEditorHash() =>
    r'71065f9aea12df26ce141b54f3dea679c9cd8f1a';

/// See also [LevelRequirementsEditor].
@ProviderFor(LevelRequirementsEditor)
final levelRequirementsEditorProvider =
    AutoDisposeAsyncNotifierProvider<
      LevelRequirementsEditor,
      LevelRequirements
    >.internal(
      LevelRequirementsEditor.new,
      name: r'levelRequirementsEditorProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$levelRequirementsEditorHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$LevelRequirementsEditor = AutoDisposeAsyncNotifier<LevelRequirements>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package

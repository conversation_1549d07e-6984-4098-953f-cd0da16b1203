import 'package:drift/drift.dart' hide Column;
import 'package:flutter/material.dart';
import 'package:riverpod/riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../core/datasources/app_database.dart' as db;
import '../../../../core/errors/failures.dart';
import '../../../../core/providers/app_settings_provider.dart';
import '../../../../core/utils/date_helper.dart';
import '../../../orders/presentation/providers/order_providers.dart';
import '../../domain/entities/level.dart';
import '../../domain/use_cases/evaluate_level.dart';

part 'level_providers.g.dart';

// Provider for level requirements
@riverpod
Future<LevelRequirements> levelRequirements(Ref ref) async {
  final database = ref.watch(databaseProvider);

  try {
    final settings = await database.getLevelSettings();

    if (settings != null) {
      return LevelRequirements(
        id: settings.id,
        platinumPointsReq: settings.platinumPointsReq,
        platinumBidReq: settings.platinumBidReq,
        platinumTripReq: settings.platinumTripReq,
        goldPointsReq: settings.goldPointsReq,
        goldBidReq: settings.goldBidReq,
        goldTripReq: settings.goldTripReq,
        silverPointsReq: settings.silverPointsReq,
        silverBidReq: settings.silverBidReq,
        silverTripReq: settings.silverTripReq,
      );
    } else {
      // Create default settings if none exist
      final defaultRequirements = LevelRequirements.defaultRequirements();
      // In a real app, you would save this to the database here
      return defaultRequirements;
    }
  } catch (e) {
    debugPrint('Error loading level requirements: $e');
    return LevelRequirements.defaultRequirements();
  }
}

// Provider for updating level requirements
@riverpod
class LevelRequirementsEditor extends _$LevelRequirementsEditor {
  @override
  Future<LevelRequirements> build() async {
    return ref.watch(levelRequirementsProvider.future);
  }

  Future<void> updateRequirements(LevelRequirements requirements) async {
    final database = ref.read(databaseProvider);

    try {
      final data = db.LevelSettingsCompanion(
        id: Value(requirements.id),
        platinumPointsReq: Value(requirements.platinumPointsReq),
        platinumBidReq: Value(requirements.platinumBidReq),
        platinumTripReq: Value(requirements.platinumTripReq),
        goldPointsReq: Value(requirements.goldPointsReq),
        goldBidReq: Value(requirements.goldBidReq),
        goldTripReq: Value(requirements.goldTripReq),
        silverPointsReq: Value(requirements.silverPointsReq),
        silverBidReq: Value(requirements.silverBidReq),
        silverTripReq: Value(requirements.silverTripReq),
      );

      final success = await database.updateLevelSettings(data); // data is a LevelSettingsCompanion, ensure updateLevelSettings expects this type

      if (success) {
        state = AsyncValue.data(requirements);
        // Refresh the level requirements provider
        ref.invalidate(levelRequirementsProvider);
      } else {
        state = AsyncValue.error(
          const Failure.database(message: 'Failed to update level requirements'),
          StackTrace.current,
        );
      }
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }
}

// Provider for evaluating driver level use case
@riverpod
EvaluateLevel evaluateLevel(Ref ref) {
  return EvaluateLevel();
}

// Provider for driver's current status
@riverpod
Future<DriverStatus> driverStatus(Ref ref) async {
  final evaluateLevel = ref.watch(evaluateLevelProvider);
  final requirementsAsync = await ref.watch(levelRequirementsProvider.future);
  final pointsAsync = await ref.watch(currentMonthPointsProvider.future);

  // Get orders for the current month to calculate bid acceptance and trip completion
  final now = DateTime.now();
  // Ensure dates are in UTC for consistent comparison
  final startDate = DateHelper.ensureUtc(DateTime(now.year, now.month, 1));
  final endDate = DateHelper.ensureUtc(DateTime(now.year, now.month + 1, 0));

  final orderRepository = ref.read(orderRepositoryProvider);
  final ordersResult = await orderRepository.getOrdersForDateRange(startDate, endDate);

  double bidAcceptance = 0.0;
  double tripCompletion = 0.0;

  ordersResult.fold(
    (failure) {
      debugPrint('Error fetching orders: ${failure.toString()}');
    },
    (orders) {
      if (orders.isNotEmpty) {
        int totalOrdersReceived = 0;
        int totalOrdersCompleted = 0;
        int totalCbsOrders = 0;
        int totalIncomingOrders = 0;

        for (final order in orders) {
          totalOrdersReceived += order.orderReceived ?? 0;
          totalOrdersCompleted += order.orderCompleted;
          totalCbsOrders += order.cbsOrder;
          totalIncomingOrders += order.incomingOrder ?? 0;
        }

        if (totalIncomingOrders > 0) {
          bidAcceptance = ((totalOrdersReceived + totalCbsOrders) / totalIncomingOrders * 100).ceil() / 100;
        }

        if (totalOrdersReceived > 0) {
          tripCompletion = (totalOrdersCompleted / totalOrdersReceived * 100).ceil() / 100;
        }
      }
    },
  );

  // Store the order counts for use in the UI
  int totalOrdersReceived = 0;
  int totalOrdersCompleted = 0;
  int totalCbsOrders = 0;
  int totalIncomingOrders = 0;

  // Extract order counts from the result
  ordersResult.fold(
    (failure) {
      debugPrint('Error fetching orders: ${failure.toString()}');
    },
    (orders) {
      if (orders.isNotEmpty) {
        for (final order in orders) {
          totalOrdersReceived += order.orderReceived ?? 0;
          totalOrdersCompleted += order.orderCompleted;
          totalCbsOrders += order.cbsOrder;
          totalIncomingOrders += order.incomingOrder ?? 0;
        }
      }
    },
  );

  final result = evaluateLevel.execute(
    totalPoints: pointsAsync,
    bidAcceptance: bidAcceptance,
    tripCompletion: tripCompletion,
    requirements: requirementsAsync,
  );

  return result.fold(
    (failure) {
      debugPrint('Error evaluating driver level: ${failure.toString()}');
      return DriverStatus.empty();
    },
    (status) {
      // Create a new DriverStatus object with the order counts
      return status.copyWith(
        totalOrdersReceived: totalOrdersReceived,
        totalOrdersCompleted: totalOrdersCompleted,
        totalIncomingOrders: totalIncomingOrders,
        totalCbsOrders: totalCbsOrders,
        pointsPerOrder: 150, // Set the points per order value
      );
    },
  );
}

// Provider for the next level requirements
@riverpod
Future<Map<String, dynamic>> nextLevelRequirements(Ref ref) async {
  final driverStatusAsync = await ref.watch(driverStatusProvider.future);

  // If already at the highest level, return current level requirements
  if (driverStatusAsync.currentLevel == DriverLevel.platinum) {
    return {
      'level': DriverLevel.platinum,
      'pointsReq': driverStatusAsync.requirements.platinumPointsReq,
      'bidReq': driverStatusAsync.requirements.platinumBidReq,
      'tripReq': driverStatusAsync.requirements.platinumTripReq,
    };
  }

  // Return requirements for the next level
  switch (driverStatusAsync.currentLevel) {
    case DriverLevel.basic:
      return {
        'level': DriverLevel.silver,
        'pointsReq': driverStatusAsync.requirements.silverPointsReq,
        'bidReq': driverStatusAsync.requirements.silverBidReq,
        'tripReq': driverStatusAsync.requirements.silverTripReq,
      };
    case DriverLevel.silver:
      return {
        'level': DriverLevel.gold,
        'pointsReq': driverStatusAsync.requirements.goldPointsReq,
        'bidReq': driverStatusAsync.requirements.goldBidReq,
        'tripReq': driverStatusAsync.requirements.goldTripReq,
      };
    case DriverLevel.gold:
      return {
        'level': DriverLevel.platinum,
        'pointsReq': driverStatusAsync.requirements.platinumPointsReq,
        'bidReq': driverStatusAsync.requirements.platinumBidReq,
        'tripReq': driverStatusAsync.requirements.platinumTripReq,
      };
    case DriverLevel.platinum:
      // Already handled above, but including for completeness
      return {
        'level': DriverLevel.platinum,
        'pointsReq': driverStatusAsync.requirements.platinumPointsReq,
        'bidReq': driverStatusAsync.requirements.platinumBidReq,
        'tripReq': driverStatusAsync.requirements.platinumTripReq,
      };
  }
}

// Provider for all upcoming level requirements
@riverpod
Future<List<Map<String, dynamic>>> allUpcomingLevelRequirements(Ref ref) async {
  final driverStatusAsync = await ref.watch(driverStatusProvider.future);
  final List<Map<String, dynamic>> upcomingLevels = [];

  // If already at the highest level, return an empty list
  if (driverStatusAsync.currentLevel == DriverLevel.platinum) {
    return upcomingLevels;
  }

  // Add requirements for all levels above the current level
  switch (driverStatusAsync.currentLevel) {
    case DriverLevel.basic:
      // Add Silver level requirements
      upcomingLevels.add({
        'level': DriverLevel.silver,
        'pointsReq': driverStatusAsync.requirements.silverPointsReq,
        'bidReq': driverStatusAsync.requirements.silverBidReq,
        'tripReq': driverStatusAsync.requirements.silverTripReq,
      });

      // Add Gold level requirements
      upcomingLevels.add({
        'level': DriverLevel.gold,
        'pointsReq': driverStatusAsync.requirements.goldPointsReq,
        'bidReq': driverStatusAsync.requirements.goldBidReq,
        'tripReq': driverStatusAsync.requirements.goldTripReq,
      });

      // Add Platinum level requirements
      upcomingLevels.add({
        'level': DriverLevel.platinum,
        'pointsReq': driverStatusAsync.requirements.platinumPointsReq,
        'bidReq': driverStatusAsync.requirements.platinumBidReq,
        'tripReq': driverStatusAsync.requirements.platinumTripReq,
      });
      break;

    case DriverLevel.silver:
      // Add Gold level requirements
      upcomingLevels.add({
        'level': DriverLevel.gold,
        'pointsReq': driverStatusAsync.requirements.goldPointsReq,
        'bidReq': driverStatusAsync.requirements.goldBidReq,
        'tripReq': driverStatusAsync.requirements.goldTripReq,
      });

      // Add Platinum level requirements
      upcomingLevels.add({
        'level': DriverLevel.platinum,
        'pointsReq': driverStatusAsync.requirements.platinumPointsReq,
        'bidReq': driverStatusAsync.requirements.platinumBidReq,
        'tripReq': driverStatusAsync.requirements.platinumTripReq,
      });
      break;

    case DriverLevel.gold:
      // Add Platinum level requirements
      upcomingLevels.add({
        'level': DriverLevel.platinum,
        'pointsReq': driverStatusAsync.requirements.platinumPointsReq,
        'bidReq': driverStatusAsync.requirements.platinumBidReq,
        'tripReq': driverStatusAsync.requirements.platinumTripReq,
      });
      break;

    case DriverLevel.platinum:
      // Already at highest level, no upcoming levels
      break;
  }

  return upcomingLevels;
}

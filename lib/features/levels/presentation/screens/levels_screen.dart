import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_dimensions.dart';
import '../../domain/entities/level.dart';
import '../providers/level_providers.dart';
import '../widgets/error_widget.dart';
import '../widgets/level_cards/current_level_card.dart';
import '../widgets/level_cards/next_level_card.dart';
import '../widgets/level_cards/platinum_level_card.dart';
import '../widgets/loading_widget.dart';
import 'level_settings_screen.dart';

/// Screen that displays the driver's current level and progress towards the next level
class LevelsScreen extends ConsumerStatefulWidget {
  const LevelsScreen({super.key});

  @override
  ConsumerState<LevelsScreen> createState() => _LevelsScreenState();
}

class _LevelsScreenState extends ConsumerState<LevelsScreen> {
  DateTime _lastRefreshed = DateTime.now();

  @override
  Widget build(BuildContext context) {
    final driverStatusAsync = ref.watch(driverStatusProvider);
    final nextLevelReqAsync = ref.watch(nextLevelRequirementsProvider);

    return Scaffold(
      appBar: _buildAppBar(),
      body: RefreshIndicator(
        onRefresh: _refreshData,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: EdgeInsets.all(AppDimensions.spacing16),
          child: driverStatusAsync.when(
            data: (status) => _buildContent(context, status, nextLevelReqAsync),
            loading: () =>
                const LevelLoadingWidget(message: 'Loading Driver Level...'),
            error: (error, stack) => LevelErrorWidget(
              title: 'Error Loading Level Data',
              error: error.toString(),
            ),
          ),
        ),
      ),
    );
  }

  /// Builds the app bar for the screen
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      elevation: 0,
      backgroundColor: AppColors.primary,
      title: const Text('Level'),
      actions: [
        IconButton(
          icon: const Icon(Icons.settings),
          tooltip: 'Level Settings',
          onPressed: () => _navigateToSettings(context),
        ),
      ],
    );
  }

  /// Refreshes the driver status and next level requirements data
  Future<void> _refreshData() async {
    ref.invalidate(driverStatusProvider);
    ref.invalidate(nextLevelRequirementsProvider);
    setState(() {
      _lastRefreshed = DateTime.now();
    });
  }

  /// Builds the main content of the screen
  Widget _buildContent(
    BuildContext context,
    DriverStatus status,
    AsyncValue<Map<String, dynamic>> nextLevelReqAsync,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Current level card
        CurrentLevelCard(status: status, lastRefreshed: _lastRefreshed),
        SizedBox(height: AppDimensions.spacing24),

        // Next level card or requirements
        _buildNextLevelSection(context, status, nextLevelReqAsync),
      ],
    );
  }

  /// Builds the next level section based on the async state
  Widget _buildNextLevelSection(
    BuildContext context,
    DriverStatus status,
    AsyncValue<Map<String, dynamic>> nextLevelReqAsync,
  ) {
    return nextLevelReqAsync.when(
      data: (nextLevelReq) =>
          _buildNextLevelCard(context, status, nextLevelReq),
      loading: () => const NextLevelLoadingCard(),
      error: (error, stack) => NextLevelErrorCard(error: error.toString()),
    );
  }

  /// Builds the appropriate next level card based on the driver's current level
  Widget _buildNextLevelCard(
    BuildContext context,
    DriverStatus status,
    Map<String, dynamic> nextLevelReq,
  ) {
    // If already at the highest level, show the platinum card
    if (status.currentLevel == DriverLevel.platinum) {
      return const PlatinumLevelCard();
    }

    // Otherwise, show progress towards next level
    return NextLevelCard(status: status, nextLevelReq: nextLevelReq);
  }

  /// Navigates to the level settings screen
  void _navigateToSettings(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const LevelSettingsScreen()),
    );
  }
}

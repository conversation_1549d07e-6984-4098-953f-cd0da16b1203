import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../../../core/theme/app_colors.dart';
import '../../domain/entities/level.dart';
import '../models/level_requirement_model.dart';
import '../providers/level_providers.dart';
import '../widgets/level_info_card.dart';
import '../widgets/level_requirement_section.dart';

class LevelSettingsScreen extends ConsumerStatefulWidget {
  const LevelSettingsScreen({super.key});

  @override
  ConsumerState<LevelSettingsScreen> createState() =>
      _LevelSettingsScreenState();
}

class _LevelSettingsScreenState extends ConsumerState<LevelSettingsScreen> {
  final _formKey = GlobalKey<FormState>();

  // Use the model class to manage level requirements
  late final LevelRequirementModel silverModel;
  late final LevelRequirementModel goldModel;
  late final LevelRequirementModel platinumModel;

  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // Initialize models
    silverModel = LevelRequirementModel.create(
      'Silver',
      AppColors.silverLevelBadge,
    );
    goldModel = LevelRequirementModel.create('Gold', AppColors.goldLevelBadge);
    platinumModel = LevelRequirementModel.create(
      'Platinum',
      AppColors.platinumLevelBadge,
    );

    _loadCurrentSettings();
  }

  @override
  void dispose() {
    // Dispose all models
    silverModel.dispose();
    goldModel.dispose();
    platinumModel.dispose();
    super.dispose();
  }

  Future<void> _loadCurrentSettings() async {
    final requirementsAsync = ref.read(levelRequirementsProvider);

    requirementsAsync.whenData((requirements) {
      // Initialize controllers with data from requirements
      silverModel.initializeFromRequirements(requirements);
      goldModel.initializeFromRequirements(requirements);
      platinumModel.initializeFromRequirements(requirements);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppColors.primary,
        title: const Text('Level Settings'),
        actions: [
          _isLoading
              ? Container(
                  margin: const EdgeInsets.all(14),
                  width: 24,
                  height: 24,
                  child: const CircularProgressIndicator(
                    strokeWidth: 2,
                    color: Colors.white,
                  ),
                )
              : IconButton(
                  icon: const Icon(Icons.save),
                  onPressed: _saveSettings,
                  tooltip: 'Save Settings',
                ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const LevelInfoCard(),
              const SizedBox(height: 24),
              LevelRequirementSection(model: silverModel),
              const SizedBox(height: 24),
              LevelRequirementSection(model: goldModel),
              const SizedBox(height: 24),
              LevelRequirementSection(model: platinumModel),
              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _saveSettings() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        final editor = ref.read(levelRequirementsEditorProvider.notifier);
        final currentRequirements = await ref.read(
          levelRequirementsProvider.future,
        );

        // Parse input values
        final silverPoints = int.parse(silverModel.pointsController.text);
        final silverBid = double.parse(silverModel.bidController.text) / 100;
        final silverTrip = double.parse(silverModel.tripController.text) / 100;

        final goldPoints = int.parse(goldModel.pointsController.text);
        final goldBid = double.parse(goldModel.bidController.text) / 100;
        final goldTrip = double.parse(goldModel.tripController.text) / 100;

        final platinumPoints = int.parse(platinumModel.pointsController.text);
        final platinumBid =
            double.parse(platinumModel.bidController.text) / 100;
        final platinumTrip =
            double.parse(platinumModel.tripController.text) / 100;

        // Validate level progression
        if (silverPoints >= goldPoints) {
          _showError('Silver points must be less than Gold points');
          return;
        }
        if (goldPoints >= platinumPoints) {
          _showError('Gold points must be less than Platinum points');
          return;
        }

        // Create updated requirements
        final newRequirements = LevelRequirements(
          id: currentRequirements.id,
          silverPointsReq: silverPoints,
          silverBidReq: silverBid,
          silverTripReq: silverTrip,
          goldPointsReq: goldPoints,
          goldBidReq: goldBid,
          goldTripReq: goldTrip,
          platinumPointsReq: platinumPoints,
          platinumBidReq: platinumBid,
          platinumTripReq: platinumTrip,
        );

        await editor.updateRequirements(newRequirements);

        // Show success message and navigate back to levels page
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Settings saved successfully'),
              backgroundColor: AppColors.success,
              behavior: SnackBarBehavior.floating,
            ),
          );

          // Navigate back to the levels page
          Navigator.of(context).pop();
        }
      } catch (e) {
        // Show error message
        _showError('Error saving settings: $e');
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  void _showError(String message) {
    if (mounted) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppColors.error,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }
}

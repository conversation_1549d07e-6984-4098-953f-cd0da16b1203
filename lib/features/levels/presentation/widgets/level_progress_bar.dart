import 'package:flutter/material.dart';

/// A custom progress bar widget for level requirements
class LevelProgressBar extends StatelessWidget {
  final double progress;
  final Color color;
  final Color backgroundColor;
  final double height;
  final double borderRadius;

  const LevelProgressBar({
    super.key,
    required this.progress,
    required this.color,
    required this.backgroundColor,
    this.height = 4.0,
    this.borderRadius = 4.0,
  });

  @override
  Widget build(BuildContext context) {
    // Ensure progress is between 0 and 1
    final clampedProgress = progress > 1.0
        ? 1.0
        : (progress < 0.0 ? 0.0 : progress);

    return LayoutBuilder(
      builder: (context, constraints) {
        final maxWidth = constraints.maxWidth;

        return ClipRRect(
          borderRadius: BorderRadius.circular(borderRadius),
          child: <PERSON>ack(
            children: [
              // Background progress bar
              Container(
                height: height,
                width: maxWidth,
                color: backgroundColor,
              ),
              // Foreground progress bar
              Container(
                height: height,
                width: maxWidth * clampedProgress,
                color: color,
              ),
            ],
          ),
        );
      },
    );
  }
}

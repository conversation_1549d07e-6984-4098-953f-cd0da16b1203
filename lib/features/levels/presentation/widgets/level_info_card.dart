import 'package:flutter/material.dart';

/// A widget that displays information about level settings
class LevelInfoCard extends StatelessWidget {
  const LevelInfoCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      color: Theme.of(context).primaryColor.withAlpha(20),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Theme.of(context).primaryColor,
                  size: 18,
                ),
                const SizedBox(width: 8),
                Text(
                  'Level Requirements',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              'Configure the requirements for each driver level. These settings determine when drivers are promoted to higher levels based on their performance metrics.',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(height: 1.5),
            ),
            const SizedBox(height: 12),
            _buildMetricExplanation(
              context,
              'Monthly Points',
              'Total points earned from completed orders in the current month',
              Icons.format_list_numbered,
            ),
            const SizedBox(height: 8),
            _buildMetricExplanation(
              context,
              'BID Acceptance',
              'Percentage of order bids accepted by the driver',
              Icons.check_circle_outline,
            ),
            const SizedBox(height: 8),
            _buildMetricExplanation(
              context,
              'Trip Completion',
              'Percentage of accepted orders successfully completed',
              Icons.delivery_dining,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricExplanation(
    BuildContext context,
    String title,
    String description,
    IconData icon,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          color: Theme.of(context).primaryColor.withAlpha(204),
          size: 16,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
              Text(description, style: Theme.of(context).textTheme.bodySmall),
            ],
          ),
        ),
      ],
    );
  }
}

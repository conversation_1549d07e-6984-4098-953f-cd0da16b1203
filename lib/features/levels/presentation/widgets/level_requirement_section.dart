import 'package:flutter/material.dart';
import '../../../../core/widgets/integer_input_field.dart';
import '../../../../core/widgets/number_input_field.dart';
import '../models/level_requirement_model.dart';

/// A reusable widget for displaying and editing level requirements
class LevelRequirementSection extends StatelessWidget {
  final LevelRequirementModel model;
  final bool showBenefits;

  const LevelRequirementSection({
    super.key,
    required this.model,
    this.showBenefits = true,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: model.color.withAlpha(30), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            const SizedBox(height: 16),
            _buildInputFields(),
            if (showBenefits) ...[
              const SizedBox(height: 16),
              _buildBenefitsSection(context),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Icon(Icons.military_tech, color: model.color, size: 18),
        const SizedBox(width: 8),
        Text(
          '${model.name} Level Requirements',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: model.color,
          ),
        ),
      ],
    );
  }

  Widget _buildInputFields() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        IntegerInputField(
          label: 'Monthly Points Required',
          controller: model.pointsController,
          isRequired: true,
          prefixIcon: Icons.format_list_numbered,
        ),
        const SizedBox(height: 16),
        NumberInputField(
          label: 'BID Acceptance Rate',
          controller: model.bidController,
          isRequired: true,
          isPercentage: true,
          isDecimal: false,
          prefixIcon: Icons.percent,
        ),
        const SizedBox(height: 16),
        NumberInputField(
          label: 'Trip Completion Rate',
          controller: model.tripController,
          isRequired: true,
          isPercentage: true,
          isDecimal: false,
          prefixIcon: Icons.percent,
        ),
      ],
    );
  }

  Widget _buildBenefitsSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.star, color: model.color, size: 16),
            const SizedBox(width: 8),
            Text(
              'Benefits',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: model.color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          model.benefitsDescription,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(height: 1.5),
        ),
      ],
    );
  }
}

import 'package:flutter/material.dart';

/// A reusable widget for displaying a metric row with an icon, label, and value
class LevelMetricRow extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;
  final Color textColor;
  final double iconSize;
  final double fontSize;
  final FontWeight labelWeight;
  final FontWeight valueWeight;

  const LevelMetricRow({
    super.key,
    required this.icon,
    required this.label,
    required this.value,
    this.textColor = Colors.black87,
    this.iconSize = 18,
    this.fontSize = 14,
    this.labelWeight = FontWeight.w500,
    this.valueWeight = FontWeight.bold,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Icon(icon, color: textColor, size: iconSize),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            label,
            style: TextStyle(
              color: textColor,
              fontSize: fontSize,
              fontWeight: labelWeight,
            ),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            color: textColor,
            fontSize: fontSize,
            fontWeight: valueWeight,
          ),
        ),
      ],
    );
  }
}

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../../../core/theme/app_colors.dart';
import '../../../domain/entities/level.dart';
import '../level_metric_row.dart';

/// A card that displays the driver's current level and performance metrics
class CurrentLevelCard extends StatelessWidget {
  final DriverStatus status;
  final DateTime lastRefreshed;

  const CurrentLevelCard({
    super.key,
    required this.status,
    required this.lastRefreshed,
  });

  @override
  Widget build(BuildContext context) {
    final List<Color> gradientColors = _getLevelGradient(status.currentLevel);
    final Color primaryColor = gradientColors[0];

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: primaryColor,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildLevel<PERSON>eader(primaryColor),
            const SizedBox(height: 16),
            _buildSectionTitle(),
            const SizedBox(height: 12),
            _buildMetricsContainer(),
            _buildLastUpdatedInfo(),
          ],
        ),
      ),
    );
  }

  /// Builds the level badge and name section
  Widget _buildLevelHeader(Color primaryColor) {
    return Row(
      children: [
        // Level badge
        CircleAvatar(
          radius: 24,
          backgroundColor: Colors.white,
          child: Icon(Icons.military_tech, color: primaryColor, size: 24),
        ),
        const SizedBox(width: 16),

        // Level info
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Current Level',
              style: TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              status.currentLevel.displayName.toUpperCase(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Builds the section title
  Widget _buildSectionTitle() {
    return const Text(
      'Performance Metrics',
      style: TextStyle(
        color: Colors.white,
        fontSize: 14,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  /// Builds the metrics container with all performance metrics
  Widget _buildMetricsContainer() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withAlpha(20),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          // Monthly Points
          LevelMetricRow(
            icon: Icons.star,
            label: 'Monthly Points',
            value: '${status.totalPoints} pts',
            textColor: Colors.white,
          ),

          const SizedBox(height: 12),

          // Bid Acceptance Rate
          LevelMetricRow(
            icon: Icons.check_circle,
            label: 'Bid Acceptance Rate',
            value: '${(status.bidAcceptance * 100).toInt()}%',
            textColor: Colors.white,
          ),

          const SizedBox(height: 12),

          // Trip Completion Rate
          LevelMetricRow(
            icon: Icons.verified,
            label: 'Trip Completion Rate',
            value: '${(status.tripCompletion * 100).toInt()}%',
            textColor: Colors.white,
          ),
        ],
      ),
    );
  }

  /// Builds the last updated information
  Widget _buildLastUpdatedInfo() {
    return Align(
      alignment: Alignment.bottomRight,
      child: Padding(
        padding: const EdgeInsets.only(top: 12),
        child: Text(
          'Updated: ${DateFormat('dd MMM yyyy, HH:mm').format(lastRefreshed)}',
          style: const TextStyle(color: Colors.white70, fontSize: 12),
        ),
      ),
    );
  }

  /// Returns the gradient colors for the given level
  List<Color> _getLevelGradient(DriverLevel level) {
    switch (level) {
      case DriverLevel.basic:
        return AppColors.basicLevelGradient;
      case DriverLevel.silver:
        return AppColors.silverLevelGradient;
      case DriverLevel.gold:
        return AppColors.goldLevelGradient;
      case DriverLevel.platinum:
        return AppColors.platinumLevelGradient;
    }
  }
}

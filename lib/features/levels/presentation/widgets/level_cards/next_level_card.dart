import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../core/theme/app_colors.dart';
import '../../../domain/entities/level.dart';
import '../../providers/level_providers.dart';
import 'level_requirements_card.dart';

/// A card that displays the requirements for the next driver level with swipe functionality
class NextLevelCard extends ConsumerStatefulWidget {
  final DriverStatus status;
  final Map<String, dynamic> nextLevelReq;
  final bool isLoading;

  const NextLevelCard({
    super.key,
    required this.status,
    required this.nextLevelReq,
    this.isLoading = false,
  });

  @override
  ConsumerState<NextLevelCard> createState() => _NextLevelCardState();
}

class _NextLevelCardState extends ConsumerState<NextLevelCard> {
  late PageController _pageController;
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: 0);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Get all upcoming level requirements
    // Create a list of upcoming levels manually since the provider might not be generated yet
    final driverStatusAsync = ref.watch(driverStatusProvider);
    final upcomingLevelsAsync = driverStatusAsync.whenData((status) {
      final List<Map<String, dynamic>> upcomingLevels = [];

      // If already at the highest level, return an empty list
      if (status.currentLevel == DriverLevel.platinum) {
        return upcomingLevels;
      }

      // Add requirements for all levels above the current level
      switch (status.currentLevel) {
        case DriverLevel.basic:
          // Add Silver level requirements
          upcomingLevels.add({
            'level': DriverLevel.silver,
            'pointsReq': status.requirements.silverPointsReq,
            'bidReq': status.requirements.silverBidReq,
            'tripReq': status.requirements.silverTripReq,
          });

          // Add Gold level requirements
          upcomingLevels.add({
            'level': DriverLevel.gold,
            'pointsReq': status.requirements.goldPointsReq,
            'bidReq': status.requirements.goldBidReq,
            'tripReq': status.requirements.goldTripReq,
          });

          // Add Platinum level requirements
          upcomingLevels.add({
            'level': DriverLevel.platinum,
            'pointsReq': status.requirements.platinumPointsReq,
            'bidReq': status.requirements.platinumBidReq,
            'tripReq': status.requirements.platinumTripReq,
          });
          break;

        case DriverLevel.silver:
          // Add Gold level requirements
          upcomingLevels.add({
            'level': DriverLevel.gold,
            'pointsReq': status.requirements.goldPointsReq,
            'bidReq': status.requirements.goldBidReq,
            'tripReq': status.requirements.goldTripReq,
          });

          // Add Platinum level requirements
          upcomingLevels.add({
            'level': DriverLevel.platinum,
            'pointsReq': status.requirements.platinumPointsReq,
            'bidReq': status.requirements.platinumBidReq,
            'tripReq': status.requirements.platinumTripReq,
          });
          break;

        case DriverLevel.gold:
          // Add Platinum level requirements
          upcomingLevels.add({
            'level': DriverLevel.platinum,
            'pointsReq': status.requirements.platinumPointsReq,
            'bidReq': status.requirements.platinumBidReq,
            'tripReq': status.requirements.platinumTripReq,
          });
          break;

        case DriverLevel.platinum:
          // Already at highest level, no upcoming levels
          break;
      }

      return upcomingLevels;
    });

    return upcomingLevelsAsync.when(
      data: (upcomingLevels) {
        // If there are no upcoming levels (user is at highest level), just show the current level
        if (upcomingLevels.isEmpty) {
          return _buildSingleLevelCard(widget.nextLevelReq);
        }

        // Extract next level requirements for the first page
        final nextLevel = widget.nextLevelReq['level'] as DriverLevel;
        final Color levelColor = _getLevelGradient(nextLevel)[0];

        return Card(
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: BorderSide(color: levelColor.withAlpha(30), width: 1),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with swipe indicator
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      'Next Level',
                      style: TextStyle(
                        color: levelColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),

              // PageView for swiping between levels
              SizedBox(
                height: 400, // Fixed height for PageView
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: PageView.builder(
                    controller: _pageController,
                    itemCount: upcomingLevels.length,
                    onPageChanged: (index) {
                      setState(() {
                        _currentPage = index;
                      });
                    },
                    itemBuilder: (context, index) {
                      final levelReq = upcomingLevels[index];
                      return LevelRequirementsCard(
                        status: widget.status,
                        targetLevel: levelReq['level'] as DriverLevel,
                        pointsReq: levelReq['pointsReq'] as int,
                        bidReq: levelReq['bidReq'] as double,
                        tripReq: levelReq['tripReq'] as double,
                        isLoading: widget.isLoading,
                      );
                    },
                  ),
                ),
              ),

              // Page indicator dots
              if (upcomingLevels.length > 1)
                Padding(
                  padding: const EdgeInsets.only(bottom: 16, top: 8),
                  child: Center(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: List.generate(
                          upcomingLevels.length,
                          (index) => _buildPageIndicator(
                            index == _currentPage,
                            levelColor,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => _buildSingleLevelCard(widget.nextLevelReq),
    );
  }

  /// Builds a page indicator dot
  Widget _buildPageIndicator(bool isActive, Color levelColor) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      height: 8,
      width: isActive ? 24 : 8,
      decoration: BoxDecoration(
        color: isActive ? levelColor : Colors.grey.shade300,
        borderRadius: BorderRadius.circular(4),
        boxShadow: isActive
            ? [
                BoxShadow(
                  color: levelColor.withAlpha(76), // 0.3 * 255 = ~76
                  blurRadius: 3,
                  offset: const Offset(0, 1),
                ),
              ]
            : null,
      ),
    );
  }

  /// Builds a single level card when there are no upcoming levels or on error
  Widget _buildSingleLevelCard(Map<String, dynamic> levelReq) {
    final nextLevel = levelReq['level'] as DriverLevel;
    final pointsReq = levelReq['pointsReq'] as int;
    final bidReq = levelReq['bidReq'] as double;
    final tripReq = levelReq['tripReq'] as double;
    final Color levelColor = _getLevelGradient(nextLevel)[0];

    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: levelColor.withAlpha(30), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Next Level Header
            Text(
              'Next Level',
              style: TextStyle(
                color: levelColor,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),

            // Level Requirements Card
            SizedBox(
              height: 400,
              child: LevelRequirementsCard(
                status: widget.status,
                targetLevel: nextLevel,
                pointsReq: pointsReq,
                bidReq: bidReq,
                tripReq: tripReq,
                isLoading: widget.isLoading,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Returns the gradient colors for the given level
  List<Color> _getLevelGradient(DriverLevel level) {
    switch (level) {
      case DriverLevel.basic:
        return AppColors.basicLevelGradient;
      case DriverLevel.silver:
        return AppColors.silverLevelGradient;
      case DriverLevel.gold:
        return AppColors.goldLevelGradient;
      case DriverLevel.platinum:
        return AppColors.platinumLevelGradient;
    }
  }
}

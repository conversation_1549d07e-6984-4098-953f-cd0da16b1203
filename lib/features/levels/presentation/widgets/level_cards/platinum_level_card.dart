import 'package:flutter/material.dart';
import '../../../../../core/theme/app_colors.dart';

/// A card that displays when the driver has reached the maximum level (Platinum)
class PlatinumLevelCard extends StatelessWidget {
  const PlatinumLevelCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: AppColors.success.withAlpha(30), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            const Icon(Icons.emoji_events, color: AppColors.success, size: 48),
            const SizedBox(height: 16),
            Text(
              'Maximum Level Achieved!',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: AppColors.success,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: AppColors.success.withAl<PERSON>(20),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                'PLATINUM',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: AppColors.success,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'You\'ve reached Platinum, the highest driver level. Keep up the excellent work!',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[700]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.star, color: AppColors.success, size: 20),
                SizedBox(width: 12),
                Icon(Icons.verified, color: AppColors.success, size: 20),
                SizedBox(width: 12),
                Icon(
                  Icons.workspace_premium,
                  color: AppColors.success,
                  size: 20,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

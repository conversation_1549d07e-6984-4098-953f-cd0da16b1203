import 'package:flutter/material.dart';

import '../../../../../core/theme/app_colors.dart';
import '../../../domain/entities/level.dart';
import '../level_progress_bar.dart';

/// A card that displays the requirements for a specific driver level
class LevelRequirementsCard extends StatelessWidget {
  final DriverStatus status;
  final DriverLevel targetLevel;
  final int pointsReq;
  final double bidReq;
  final double tripReq;
  final bool isLoading;

  const LevelRequirementsCard({
    super.key,
    required this.status,
    required this.targetLevel,
    required this.pointsReq,
    required this.bidReq,
    required this.tripReq,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    // Calculate progress percentages
    final pointsProgress = status.totalPoints / pointsReq;
    final bidProgress = status.bidAcceptance / bidReq;
    final tripProgress = status.tripCompletion / tripReq;

    // Check if requirements are met
    final isPointsMet = status.totalPoints >= pointsReq;
    final isBidMet = status.bidAcceptance >= bidReq;
    final isTripMet = status.tripCompletion >= tripReq;

    // Get level colors
    final List<Color> levelGradient = _getLevelGradient(targetLevel);
    final Color levelColor = levelGradient[0];

    // Calculate points needed
    final int pointsNeeded = isPointsMet ? 0 : pointsReq - status.totalPoints;

    // Calculate orders needed for points
    final int ordersNeededForPoints = (pointsNeeded / status.pointsPerOrder)
        .ceil();

    // Calculate orders needed for bid acceptance rate
    int ordersNeededForBidRate = 0;
    if (!isBidMet) {
      // Use the formula to calculate orders needed for bid acceptance
      ordersNeededForBidRate = status.calculateOrdersNeededForBidAcceptance(
        bidReq,
      );
    }

    // Calculate orders needed for trip completion rate
    int ordersNeededForTripRate = 0;
    if (!isTripMet) {
      // Use the formula to calculate orders needed for trip completion
      ordersNeededForTripRate = status.calculateOrdersNeededForTripCompletion(
        tripReq,
      );
    }

    // Get the maximum orders needed across all requirements
    final int totalOrdersNeeded = [
      ordersNeededForPoints,
      ordersNeededForBidRate,
      ordersNeededForTripRate,
    ].reduce((a, b) => a > b ? a : b);

    // Calculate remaining days in the current month
    final now = DateTime.now();
    final lastDayOfMonth = DateTime(now.year, now.month + 1, 0);
    final int remainingDays = lastDayOfMonth.day - now.day + 1;

    // Calculate required orders per day for the remaining days in the month
    final double ordersPerDay = remainingDays > 0
        ? totalOrdersNeeded / remainingDays
        : totalOrdersNeeded.toDouble();
    final int roundedOrdersPerDay = ordersPerDay.ceil();

    return ConstrainedBox(
      constraints: const BoxConstraints(maxHeight: 400),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Level Name
            _buildLevelName(levelColor, targetLevel),
            const SizedBox(height: 12),

            // Level Description
            _buildLevelDescription(levelColor, context),
            const SizedBox(height: 16),

            // Orders needed to reach next level
            if (totalOrdersNeeded > 0) ...[
              _buildOrdersNeededSection(
                context: context,
                totalOrdersNeeded: totalOrdersNeeded,
                remainingDays: remainingDays,
                ordersPerDay: roundedOrdersPerDay,
                levelColor: levelColor,
              ),
              const SizedBox(height: 16),
            ],

            // Monthly Points
            _buildRequirementRow(
              context: context,
              label: 'Monthly Points',
              currentValue: status.totalPoints,
              requiredValue: pointsReq,
              isMet: isPointsMet,
              progress: pointsProgress,
              levelColor: levelColor,
              valueFormatter: (value) => '$value pts',
              additionalInfo: isPointsMet
                  ? null
                  : '${(pointsNeeded / 150).ceil()} orders to earn $pointsNeeded more points needed',
              tip: 'Complete more orders to earn points',
            ),
            const SizedBox(height: 16),

            // Bid Acceptance Rate
            _buildRequirementRow(
              context: context,
              label: 'Bid Acceptance Rate',
              currentValue: status.bidAcceptance,
              requiredValue: bidReq,
              isMet: isBidMet,
              progress: bidProgress,
              levelColor: levelColor,
              valueFormatter: (value) => '${(value * 100).toInt()}%',
              additionalInfo: isBidMet
                  ? null
                  : 'Accept $ordersNeededForBidRate more orders to reach ${(bidReq * 100).toInt()}%',
              tip: 'Respond quickly to incoming order requests',
            ),
            const SizedBox(height: 16),

            // Trip Completion Rate
            _buildRequirementRow(
              context: context,
              label: 'Trip Completion Rate',
              currentValue: status.tripCompletion,
              requiredValue: tripReq,
              isMet: isTripMet,
              progress: tripProgress,
              levelColor: levelColor,
              valueFormatter: (value) => '${(value * 100).toInt()}%',
              additionalInfo: isTripMet
                  ? null
                  : 'Complete $ordersNeededForTripRate more orders to reach ${(tripReq * 100).toInt()}%',
              tip: 'Avoid cancelling orders after accepting them',
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the level name row
  Widget _buildLevelName(Color levelColor, DriverLevel targetLevel) {
    return Row(
      children: [
        Icon(Icons.military_tech, color: levelColor, size: 20),
        const SizedBox(width: 8),
        Text(
          targetLevel.displayName.toUpperCase(),
          style: const TextStyle(
            color: Colors.black87,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  /// Builds the level description row
  Widget _buildLevelDescription(Color levelColor, BuildContext context) {
    return Row(
      children: [
        Icon(Icons.star, color: levelColor, size: 14),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            'Premium earnings and exclusive promotions',
            style: TextStyle(
              color: Colors.grey.shade700,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  /// Builds a section showing orders needed to reach next level
  Widget _buildOrdersNeededSection({
    required BuildContext context,
    required int totalOrdersNeeded,
    required int remainingDays,
    required int ordersPerDay,
    required Color levelColor,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: levelColor.withAlpha(26), // 0.1 * 255 = ~26
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.directions_car, color: levelColor, size: 16),
              const SizedBox(width: 8),
              Text(
                'Orders Needed for ${targetLevel.displayName} Level',
                style: TextStyle(
                  color: levelColor,
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '$totalOrdersNeeded orders',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'to reach ${targetLevel.displayName} level',
                    style: TextStyle(fontSize: 12, color: Colors.grey.shade700),
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '$remainingDays days left',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    'need $ordersPerDay orders/day',
                    style: TextStyle(fontSize: 12, color: Colors.grey.shade700),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Builds a requirement row with progress bar
  Widget _buildRequirementRow({
    required BuildContext context,
    required String label,
    required dynamic currentValue,
    required dynamic requiredValue,
    required bool isMet,
    required double progress,
    required Color levelColor,
    required String Function(dynamic value) valueFormatter,
    String? additionalInfo,
    String? tip,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
            ),
            Row(
              children: [
                Text(
                  '${valueFormatter(currentValue)} / ${valueFormatter(requiredValue)}',
                  style: const TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(width: 6),
                if (isMet)
                  const Icon(Icons.check_circle, color: Colors.green, size: 14)
                else
                  Container(
                    width: 14,
                    height: 14,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.grey.shade300,
                    ),
                  ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 6),
        LevelProgressBar(
          progress: progress,
          color: levelColor,
          backgroundColor: Colors.grey.shade200,
        ),

        // Additional info and tips
        if (additionalInfo != null) ...[
          const SizedBox(height: 4),
          Row(
            children: [
              Icon(Icons.info_outline, size: 12, color: levelColor),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  additionalInfo,
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.grey.shade700,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            ],
          ),
        ],

        // Tips for improvement
        if (tip != null && !isMet) ...[
          const SizedBox(height: 4),
          Row(
            children: [
              Icon(
                Icons.lightbulb_outline,
                size: 12,
                color: Colors.amber.shade700,
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  'Tip: $tip',
                  style: TextStyle(fontSize: 11, color: Colors.amber.shade700),
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  /// Returns the gradient colors for the given level
  List<Color> _getLevelGradient(DriverLevel level) {
    switch (level) {
      case DriverLevel.basic:
        return AppColors.basicLevelGradient;
      case DriverLevel.silver:
        return AppColors.silverLevelGradient;
      case DriverLevel.gold:
        return AppColors.goldLevelGradient;
      case DriverLevel.platinum:
        return AppColors.platinumLevelGradient;
    }
  }
}

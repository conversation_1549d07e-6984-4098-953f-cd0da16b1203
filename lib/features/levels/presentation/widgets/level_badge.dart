import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../domain/entities/level.dart';

class LevelBadge extends StatelessWidget {
  final DriverLevel level;
  final double size;
  final bool showLabel;

  const LevelBadge({
    super.key,
    required this.level,
    this.size = 64,
    this.showLabel = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: _getBadgeColor().withAlpha((0.3 * 255).toInt()),
                blurRadius: 10,
                spreadRadius: 2,
              ),
            ],
            border: Border.all(color: _getBadgeColor(), width: size / 16),
          ),
          child: Center(child: _buildBadgeIcon()),
        ),
        if (showLabel) ...[
          const SizedBox(height: 8),
          Text(
            level.displayName,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: _getBadgeColor(),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildBadgeIcon() {
    switch (level) {
      case DriverLevel.basic:
        return Icon(
          Icons.emoji_events_outlined,
          color: _getBadgeColor(),
          size: size * 0.5,
        );
      case DriverLevel.silver:
        return Icon(
          Icons.emoji_events,
          color: _getBadgeColor(),
          size: size * 0.5,
        );
      case DriverLevel.gold:
        return Stack(
          alignment: Alignment.center,
          children: [
            Icon(Icons.emoji_events, color: _getBadgeColor(), size: size * 0.5),
            Icon(Icons.star, color: _getBadgeColor(), size: size * 0.25),
          ],
        );
      case DriverLevel.platinum:
        return Stack(
          alignment: Alignment.center,
          children: [
            Icon(Icons.emoji_events, color: _getBadgeColor(), size: size * 0.5),
            Icon(
              Icons.workspace_premium,
              color: _getBadgeColor(),
              size: size * 0.25,
            ),
          ],
        );
    }
  }

  Color _getBadgeColor() {
    switch (level) {
      case DriverLevel.basic:
        return AppColors.basicLevelBadge;
      case DriverLevel.silver:
        return AppColors.silverLevelBadge;
      case DriverLevel.gold:
        return AppColors.goldLevelBadge;
      case DriverLevel.platinum:
        return AppColors.platinumLevelBadge;
    }
  }
}

import 'package:flutter/material.dart';

/// A small widget to display an "Updated: date" label
class UpdatedDateLabel extends StatelessWidget {
  final String formattedDate;
  final Color textColor;
  final double fontSize;
  final double iconSize;
  final double letterSpacing;

  const UpdatedDateLabel({
    super.key,
    required this.formattedDate,
    this.textColor = Colors.white,
    this.fontSize = 10,
    this.iconSize = 10,
    this.letterSpacing = 0.0,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
      decoration: BoxDecoration(
        color: Colors.white.withAlpha((0.2 * 255).toInt()),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.access_time, color: textColor, size: iconSize),
          const SizedBox(width: 4),
          Text(
            'Updated: $formattedDate',
            style: TextStyle(
              color: textColor,
              fontSize: fontSize,
              letterSpacing: letterSpacing,
            ),
          ),
        ],
      ),
    );
  }
}

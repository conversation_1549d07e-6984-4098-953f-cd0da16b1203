import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/level.dart';

/// Use case to evaluate the driver's level based on performance metrics
class EvaluateLevel {
  /// Evaluate the driver's level based on points, bid acceptance rate, and trip completion rate
  Either<Failure, DriverStatus> execute({
    required int totalPoints,
    required double bidAcceptance,
    required double tripCompletion,
    required LevelRequirements requirements,
  }) {
    try {
      // Determine the driver's level based on requirements
      final level = _determineLevel(
        totalPoints: totalPoints,
        bidAcceptance: bidAcceptance,
        tripCompletion: tripCompletion,
        requirements: requirements,
      );
      
      // Create and return the driver status
      return Right(
        DriverStatus(
          currentLevel: level,
          totalPoints: totalPoints,
          bidAcceptance: bidAcceptance,
          tripCompletion: tripCompletion,
          requirements: requirements,
        ),
      );
    } catch (e) {
      return Left(Failure.businessLogic(message: 'Error evaluating driver level: $e'));
    }
  }
  
  /// Determine the driver's level based on the given metrics and requirements
  DriverLevel _determineLevel({
    required int totalPoints,
    required double bidAcceptance,
    required double tripCompletion,
    required LevelRequirements requirements,
  }) {
    // Check Platinum requirements
    if (totalPoints >= requirements.platinumPointsReq &&
        bidAcceptance >= requirements.platinumBidReq &&
        tripCompletion >= requirements.platinumTripReq) {
      return DriverLevel.platinum;
    }
    
    // Check Gold requirements
    if (totalPoints >= requirements.goldPointsReq &&
        bidAcceptance >= requirements.goldBidReq &&
        tripCompletion >= requirements.goldTripReq) {
      return DriverLevel.gold;
    }
    
    // Check Silver requirements
    if (totalPoints >= requirements.silverPointsReq &&
        bidAcceptance >= requirements.silverBidReq &&
        tripCompletion >= requirements.silverTripReq) {
      return DriverLevel.silver;
    }
    
    // Default to Basic
    return DriverLevel.basic;
  }
}

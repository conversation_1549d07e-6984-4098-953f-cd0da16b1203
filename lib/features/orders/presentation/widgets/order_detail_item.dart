import 'package:flutter/material.dart';

import '../../../../../core/theme/app_colors.dart';

class OrderDetailItem extends StatelessWidget {
  final String label;
  final String value;
  final IconData icon;
  final bool isTotal;
  final Color? valueColor;

  const OrderDetailItem({
    super.key,
    required this.label,
    required this.value,
    required this.icon,
    this.isTotal = false,
    this.valueColor,
  });

  @override
  Widget build(BuildContext context) {
    final itemColor =
        valueColor ?? (isTotal ? AppColors.success : Colors.grey[700]);
    final fontWeight = isTotal ? FontWeight.bold : FontWeight.w500;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, color: itemColor, size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: isTotal ? FontWeight.w600 : FontWeight.w500,
                color: isTotal ? itemColor : null,
              ),
            ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: fontWeight,
              color: itemColor,
            ),
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/widgets/shimmer/shimmer_components.dart';

/// Widget that displays a standardized shimmer loading effect for the entire orders screen
class OrderStandardizedShimmerLoading extends StatelessWidget {
  const OrderStandardizedShimmerLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return SliverFillRemaining(
      child: ShimmerWrapper(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Summary section shimmer
              _buildSummarySectionShimmer(context),

              // History header shimmer
              _buildHistoryHeaderShimmer(context),

              // Order list shimmer
              _buildOrderListShimmer(context),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds the shimmer effect for the summary section
  Widget _buildSummarySectionShimmer(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 0.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Summary title
          ShimmerText.title(),
          const SizedBox(height: 8),

          // Summary subtitle
          ShimmerText.subtitle(),
          const SizedBox(height: 16),

          // Performance Metrics Card
          _buildMetricsCardShimmer(
            context,
            AppColors.primary.withAlpha(25), // 0.1 * 255 = 25
          ),
          const SizedBox(height: 16),

          // Financial Summary Card
          _buildMetricsCardShimmer(
            context,
            AppColors.success.withAlpha(25), // 0.1 * 255 = 25
          ),

          const SizedBox(height: 20),

          // Analytics Card
          const ShimmerCard(
            height: 200,
            padding: EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Card title
                ShimmerText(width: 150, height: 18),
                SizedBox(height: 16),

                // Chart placeholder
                Expanded(
                  child: ShimmerContainer(
                    width: double.infinity,
                    height: double.infinity,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the shimmer effect for a metrics card
  Widget _buildMetricsCardShimmer(BuildContext context, Color cardColor) {
    return ShimmerCard(
      borderColor: Colors.grey.withAlpha(25),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Card header
            Row(
              children: [
                const ShimmerCircle(size: 20),
                const SizedBox(width: 8),
                const ShimmerText(width: 150, height: 18),
                const Spacer(),
                ShimmerContainer(
                  width: 24,
                  height: 24,
                  borderRadius: BorderRadius.circular(4),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Metrics row - simplified to one row with 3 items
            Row(
              children: [
                Expanded(child: _buildMetricItem()),
                Expanded(child: _buildMetricItem()),
                Expanded(child: _buildMetricItem()),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Builds a metric item for the metrics card - simplified version
  Widget _buildMetricItem() {
    return const Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min, // Use minimum vertical space
      children: [
        ShimmerCircle(size: 16),
        SizedBox(height: 6),
        ShimmerText(width: 50, height: 14),
      ],
    );
  }

  /// Builds the shimmer effect for the history header
  Widget _buildHistoryHeaderShimmer(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16.0, 24.0, 16.0, 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // History title
          ShimmerText.title(),
          const SizedBox(height: 8),

          // History subtitle
          const ShimmerText(width: 180, height: 14),
        ],
      ),
    );
  }

  /// Builds the shimmer effect for the order list
  Widget _buildOrderListShimmer(BuildContext context) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: 5,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: ShimmerCard(
            constraints: const BoxConstraints(minHeight: 120),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header row
                  const Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      ShimmerText(width: 80, height: 16),
                      ShimmerText(width: 60, height: 16),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // Order counts row
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildOrderCountItem(),
                      _buildOrderCountItem(),
                      _buildOrderCountItem(),
                      _buildOrderCountItem(),
                    ],
                  ),

                  const SizedBox(height: 12),
                  const ShimmerDivider(),
                  const SizedBox(height: 12),

                  // Progress bars
                  Row(
                    children: [
                      Expanded(child: _buildProgressBar()),
                      const SizedBox(width: 16),
                      Expanded(child: _buildProgressBar()),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildOrderCountItem() {
    return ShimmerContainer(
      width: 40,
      height: 20,
      borderRadius: BorderRadius.circular(4),
    );
  }

  Widget _buildProgressBar() {
    return ShimmerContainer(
      width: double.infinity,
      height: 8,
      borderRadius: BorderRadius.circular(4),
    );
  }
}

import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/calculations.dart';
import '../../../../core/widgets/expandable_card.dart';
import '../../domain/use_cases/get_order_summary.dart';

class OrderMetrics extends StatefulWidget {
  final OrderSummary summary;

  const OrderMetrics({super.key, required this.summary});

  @override
  State<OrderMetrics> createState() => _OrderMetricsState();
}

class _OrderMetricsState extends State<OrderMetrics> {
  bool _showFinancialCard = false;

  @override
  void initState() {
    super.initState();
    // Add a delay before showing the financial card
    // This ensures the performance metrics card is positioned first
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        setState(() {
          _showFinancialCard = true;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildMetricsCard(context),
        const SizedBox(height: 20),
        // Use AnimatedOpacity for smooth appearance of the financial card
        AnimatedOpacity(
          opacity: _showFinancialCard ? 1.0 : 0.0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            transform: Matrix4.translationValues(
              0.0,
              _showFinancialCard ? 0.0 : 20.0,
              0.0,
            ),
            child: _buildFinancialCard(context),
          ),
        ),
      ],
    );
  }

  Widget _buildMetricsCard(BuildContext context) {
    return ExpandableCard(
      title: 'Performance Metrics',
      icon: Icons.analytics,
      iconColor: AppColors.primary,
      borderColor: AppColors.primary,
      collapsedChild: const SizedBox.shrink(), // Empty when collapsed
      expandedChild: Column(
        children: [
          // Main metrics
          Row(
            children: [
              Expanded(
                flex: 1,
                child: _buildMetricItem(
                  'BID Performance',
                  Calculations.formatPercentage(
                    widget.summary.avgBidAcceptance,
                  ),
                  Icons.speed,
                  valueColor: AppColors.primary,
                ),
              ),
              Expanded(
                flex: 1,
                child: _buildMetricItem(
                  'Trip Completion',
                  Calculations.formatPercentage(
                    widget.summary.avgTripCompletion,
                  ),
                  Icons.done_all,
                  valueColor: AppColors.primary,
                ),
              ),
              Expanded(
                flex: 1,
                child: _buildMetricItem(
                  'Total Points',
                  widget.summary.totalPoints.toString(),
                  Icons.stars,
                  valueColor: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Order statistics
          Row(
            children: [
              Expanded(
                flex: 1,
                child: _buildOrderStatistic(
                  'Incoming',
                  widget.summary.totalIncomingOrders.toString(),
                  valueColor: AppColors.primary,
                ),
              ),
              Expanded(
                flex: 1,
                child: _buildOrderStatistic(
                  'Received',
                  widget.summary.totalOrdersReceived.toString(),
                  valueColor: AppColors.primary,
                ),
              ),
              Expanded(
                flex: 1,
                child: _buildOrderStatistic(
                  'Completed',
                  widget.summary.totalOrdersCompleted.toString(),
                  valueColor: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                flex: 1,
                child: _buildOrderStatistic(
                  'Missed',
                  widget.summary.totalOrdersMissed.toString(),
                  valueColor: AppColors.primary,
                ),
              ),
              Expanded(
                flex: 1,
                child: _buildOrderStatistic(
                  'Canceled',
                  widget.summary.totalOrdersCanceled.toString(),
                  valueColor: AppColors.primary,
                ),
              ),
              Expanded(
                flex: 1,
                child: _buildOrderStatistic(
                  'CBS',
                  widget.summary.totalCbsOrders.toString(),
                  valueColor: AppColors.primary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildOrderStatistic(String label, String value, {Color? valueColor}) {
    return Builder(
      builder: (context) => Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: valueColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 2),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
              fontSize: 11,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFinancialCard(BuildContext context) {
    return ExpandableCard(
      title: 'Financial Summary',
      icon: Icons.account_balance_wallet,
      iconColor: AppColors.success,
      borderColor: AppColors.success,
      collapsedChild: const SizedBox.shrink(), // Empty when collapsed
      expandedChild: Column(
        children: [
          // Main financial metrics
          Row(
            children: [
              Expanded(
                flex: 1,
                child: _buildMetricItem(
                  'Total Income',
                  Calculations.formatCurrency(widget.summary.totalIncome),
                  Icons.payments,
                  valueColor: AppColors.success,
                ),
              ),
              Expanded(
                flex: 1,
                child: _buildMetricItem(
                  'Trip',
                  Calculations.formatCurrency(widget.summary.totalTrip),
                  Icons.directions_car,
                  valueColor: AppColors.success,
                ),
              ),
              Expanded(
                flex: 1,
                child: _buildMetricItem(
                  'Bonus & Tips',
                  Calculations.formatCurrency(
                    widget.summary.totalBonus + widget.summary.totalTips,
                  ),
                  Icons.card_giftcard,
                  valueColor: AppColors.success,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Additional financial details
          Row(
            children: [
              Expanded(
                flex: 1,
                child: _buildOrderStatistic(
                  'Bonus',
                  Calculations.formatCurrency(widget.summary.totalBonus),
                  valueColor: AppColors.success,
                ),
              ),
              Expanded(
                flex: 1,
                child: _buildOrderStatistic(
                  'Tips',
                  Calculations.formatCurrency(widget.summary.totalTips),
                  valueColor: AppColors.success,
                ),
              ),
              Expanded(
                flex: 1,
                child: _buildOrderStatistic(
                  'Avg Income',
                  Calculations.formatCurrency(
                    widget.summary.recordCount > 0
                        ? widget.summary.totalIncome /
                              widget.summary.recordCount
                        : 0,
                  ),
                  valueColor: AppColors.success,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMetricItem(
    String label,
    String value,
    IconData icon, {
    Color? valueColor,
  }) {
    return Builder(
      builder: (context) => Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Icon(icon, color: valueColor ?? AppColors.primary, size: 18),
          const SizedBox(height: 6),
          Text(
            value,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: valueColor ?? AppColors.primary,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            label,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
              fontSize: 11,
            ),
          ),
        ],
      ),
    );
  }
}

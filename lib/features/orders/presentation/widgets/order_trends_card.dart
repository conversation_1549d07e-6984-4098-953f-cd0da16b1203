import 'dart:math' as math;

import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/date_helper.dart';
import '../../../../core/widgets/expandable_card.dart';
import '../../domain/entities/order.dart';

/// Widget that displays order trends chart with daily and weekly visualizations
class OrderTrendsCard extends StatefulWidget {
  final List<Order> orders;

  const OrderTrendsCard({super.key, required this.orders});

  @override
  State<OrderTrendsCard> createState() => _OrderTrendsCardState();
}

class _OrderTrendsCardState extends State<OrderTrendsCard>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Sort orders by date (oldest first) for trend charts
    final sortedOrders = List<Order>.from(widget.orders)
      ..sort((a, b) => a.date.compareTo(b.date));

    return ExpandableCard(
      title: 'Order Trends',
      icon: Icons.insights,
      iconColor: AppColors.secondary,
      borderColor: AppColors.secondary,
      collapsedChild: const SizedBox.shrink(), // Empty when collapsed
      expandedChild: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Tab bar for switching between daily and weekly views
          TabBar(
            controller: _tabController,
            labelColor: AppColors.secondary,
            unselectedLabelColor: Colors.grey,
            indicatorColor: AppColors.secondary,
            indicatorSize: TabBarIndicatorSize.tab,
            tabs: const [
              Tab(text: 'Daily Trends'),
              Tab(text: 'Weekly Trends'),
            ],
          ),
          const SizedBox(height: 16),

          // Tab content
          SizedBox(
            height: 350, // Adjusted height for better proportions
            child: TabBarView(
              controller: _tabController,
              physics:
                  const NeverScrollableScrollPhysics(), // Prevent swiping to avoid layout issues
              children: [
                // Daily trends tab
                _buildDailyOrderTrendsSection(context, sortedOrders),

                // Weekly trends tab
                _buildWeeklyOrderTrendsSection(context, sortedOrders),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLegendItem(String label, Color color, int value) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(color: color, shape: BoxShape.circle),
        ),
        const SizedBox(width: 8),
        Text('$label: $value', style: const TextStyle(fontSize: 12)),
      ],
    );
  }

  // Helper method to calculate a nice rounded maximum Y value for charts
  double _calculateNiceMaxY(double maxY) {
    // Add some padding to the max Y value for better visualization
    maxY = maxY * 1.2; // Increase padding to 20% for better spacing at the top

    // Round maxY to a nice number for better tick marks
    if (maxY > 0) {
      final magnitude = maxY.abs() > 0
          ? (math.log(maxY.abs()) / math.ln10).floor()
          : 0;
      final power = math.pow(10, magnitude).toDouble();
      maxY = (maxY / power).ceil() * power;
    } else {
      // If all values are 0, set a default range
      maxY = 5.0;
    }

    return maxY;
  }

  // Build daily order trends section - only showing completed orders
  Widget _buildDailyOrderTrendsSection(
    BuildContext context,
    List<Order> sortedOrders,
  ) {
    // Skip if no data
    if (sortedOrders.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Text('No order data available for the selected period.'),
        ),
      );
    }

    // Prepare data for line chart - only completed orders
    final completedSpots = <FlSpot>[];

    // For Y-axis range calculation
    double maxY = 0;

    // Daily data points
    for (int i = 0; i < sortedOrders.length; i++) {
      final order = sortedOrders[i];
      completedSpots.add(FlSpot(i.toDouble(), order.orderCompleted.toDouble()));

      // Update max Y value for chart scaling
      maxY = math.max(maxY, order.orderCompleted.toDouble());
    }

    // Calculate daily average of completed orders
    final int totalCompleted = sortedOrders.fold<int>(
      0,
      (sum, order) => sum + order.orderCompleted,
    );
    final double dailyAverage = sortedOrders.isNotEmpty
        ? totalCompleted / sortedOrders.length
        : 0;

    // Calculate nice max Y value
    maxY = _calculateNiceMaxY(maxY);

    return _buildTrendChart(
      context: context,
      title: 'Daily Completed Orders',
      spots: completedSpots,
      sortedOrders: sortedOrders,
      maxY: maxY,
      isWeekly: false,
      average: dailyAverage,
    );
  }

  // Build weekly order trends section - only showing completed orders
  Widget _buildWeeklyOrderTrendsSection(
    BuildContext context,
    List<Order> sortedOrders,
  ) {
    // Skip if no data
    if (sortedOrders.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Text('No order data available for the selected period.'),
        ),
      );
    }

    // Prepare data for line chart - only completed orders
    final completedSpots = <FlSpot>[];

    // For Y-axis range calculation
    double maxY = 0;

    // Group orders by week with consistent starting day (Monday)
    final Map<int, List<Order>> weeklyOrders = {};

    // Define the starting day of the week (1 = Monday, 7 = Sunday)
    // Using Monday as the default starting day (ISO 8601 standard)
    const int startingWeekday = DateTime.monday;

    // Find the first date that matches our starting weekday
    DateTime firstWeekStart = sortedOrders.first.date;
    while (firstWeekStart.weekday != startingWeekday) {
      firstWeekStart = firstWeekStart.subtract(const Duration(days: 1));
    }

    for (final order in sortedOrders) {
      // Calculate which week this order belongs to
      final daysSinceFirstWeekStart = order.date
          .difference(firstWeekStart)
          .inDays;
      final weekNumber = daysSinceFirstWeekStart ~/ 7;

      if (!weeklyOrders.containsKey(weekNumber)) {
        weeklyOrders[weekNumber] = [];
      }

      weeklyOrders[weekNumber]!.add(order);
    }

    // Sort weeks
    final sortedWeeks = weeklyOrders.keys.toList()..sort();

    // Create data points for each week - only completed orders
    for (int i = 0; i < sortedWeeks.length; i++) {
      final weekNumber = sortedWeeks[i];
      final orders = weeklyOrders[weekNumber]!;

      int weeklyCompleted = 0;

      for (final order in orders) {
        weeklyCompleted += order.orderCompleted;
      }

      completedSpots.add(FlSpot(i.toDouble(), weeklyCompleted.toDouble()));

      // Update max Y value for chart scaling
      maxY = math.max(maxY, weeklyCompleted.toDouble());
    }

    // Calculate weekly average of completed orders
    final int totalCompleted = sortedOrders.fold<int>(
      0,
      (sum, order) => sum + order.orderCompleted,
    );
    final double weeklyAverage = sortedWeeks.isNotEmpty
        ? totalCompleted / sortedWeeks.length
        : 0;

    // Calculate nice max Y value
    maxY = _calculateNiceMaxY(maxY);

    return _buildTrendChart(
      context: context,
      title: 'Weekly Completed Orders',
      spots: completedSpots,
      sortedOrders: sortedOrders,
      maxY: maxY,
      isWeekly: true,
      weeklyGroups: weeklyOrders,
      sortedWeeks: sortedWeeks,
      average: weeklyAverage,
    );
  }

  // Build trend chart with the given data
  Widget _buildTrendChart({
    required BuildContext context,
    required String title,
    required List<FlSpot> spots,
    required List<Order> sortedOrders,
    required double maxY,
    required bool isWeekly,
    required double average,
    List<int>? sortedWeeks,
    Map<int, List<Order>>? weeklyGroups,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        Center(
          // Center the chart horizontally
          child: Container(
            height: 250,
            width: double.infinity, // Take full width
            margin: const EdgeInsets.symmetric(
              horizontal: 4.0,
            ), // Add horizontal margin
            child: Padding(
              padding: const EdgeInsets.symmetric(
                vertical: 8.0,
              ), // Only vertical padding
              child: LineChart(
                LineChartData(
                  gridData: FlGridData(
                    show: true,
                    drawVerticalLine: true,
                    horizontalInterval:
                        maxY / 4, // Match Y-axis labels interval
                    getDrawingHorizontalLine: (value) {
                      return FlLine(
                        color: Colors.grey.shade200,
                        strokeWidth: 0.8,
                      );
                    },
                    getDrawingVerticalLine: (value) {
                      return FlLine(
                        color: Colors.grey.shade200,
                        strokeWidth: 0.8,
                      );
                    },
                  ),
                  minY: 0, // Start at 0
                  maxY: maxY, // Use calculated max Y value
                  titlesData: FlTitlesData(
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 35, // Increased for better spacing
                        interval: maxY / 4, // Keep 4 labels
                        getTitlesWidget: (value, meta) {
                          // Format Y-axis labels
                          String formattedValue;
                          if (value == 0) {
                            formattedValue = '0';
                          } else if (value.abs() < 1000) {
                            formattedValue = value.round().toString();
                          } else {
                            // For all values over 1000, show as "1K", "10K", etc.
                            formattedValue = '${(value / 1000).round()}K';
                          }

                          return Padding(
                            padding: const EdgeInsets.only(
                              right: 6.0,
                            ), // Increased padding between label and grid
                            child: SizedBox(
                              width:
                                  30, // Slightly wider container for better spacing
                              child: Text(
                                formattedValue,
                                style: const TextStyle(
                                  fontSize: 8, // Keep small font
                                  fontWeight: FontWeight.w500,
                                ),
                                textAlign: TextAlign.end, // Right align text
                                maxLines: 1,
                                overflow: TextOverflow
                                    .visible, // Allow overflow if needed
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          // Show fewer labels for readability
                          if (value % 2 != 0) {
                            return const SizedBox.shrink();
                          }

                          final index = value.toInt();

                          // For weekly view - show formatted week labels
                          if (isWeekly &&
                              sortedWeeks != null &&
                              index >= 0 &&
                              index < sortedWeeks.length) {
                            // Check if this is the last label (rightmost)
                            final isLastLabel = index == sortedWeeks.length - 1;

                            // Get the first order in this week to display its date
                            final weekNumber = sortedWeeks[index];
                            final weekOrders = weeklyGroups![weekNumber]!;
                            final firstOrderDate = weekOrders.first.date;

                            return Padding(
                              // Add extra padding for the last label to prevent overflow
                              padding: EdgeInsets.only(
                                top: 8.0,
                                right: isLastLabel ? 8.0 : 0.0,
                              ),
                              child: Text(
                                'W${index + 1}: ${DateFormat('dd/MM').format(firstOrderDate)}',
                                style: const TextStyle(
                                  fontSize: 9,
                                ), // Slightly smaller
                                textAlign: isLastLabel
                                    ? TextAlign.end
                                    : TextAlign.center,
                              ),
                            );
                          }
                          // For daily view
                          else if (!isWeekly &&
                              index >= 0 &&
                              index < sortedOrders.length) {
                            // Check if this is the last label (rightmost)
                            final isLastLabel =
                                index == sortedOrders.length - 1;

                            return Padding(
                              // Add extra padding for the last label to prevent overflow
                              padding: EdgeInsets.only(
                                top: 8.0,
                                right: isLastLabel ? 8.0 : 0.0,
                              ),
                              child: Text(
                                DateFormat(
                                  'dd/MM',
                                ).format(sortedOrders[index].date),
                                style: const TextStyle(
                                  fontSize: 9,
                                ), // Slightly smaller
                                textAlign: isLastLabel
                                    ? TextAlign.end
                                    : TextAlign.center,
                              ),
                            );
                          }
                          return const SizedBox.shrink();
                        },
                        reservedSize: 25, // Adjusted to 25px for better spacing
                      ),
                    ),
                    rightTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    topTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                  ),
                  lineBarsData: [
                    LineChartBarData(
                      spots: spots,
                      isCurved: true,
                      color: AppColors.success,
                      barWidth: 3,
                      isStrokeCapRound: true,
                      dotData: const FlDotData(show: false),
                      belowBarData: BarAreaData(
                        show: true,
                        color: AppColors.success.withAlpha((0.1 * 255).toInt()),
                      ),
                    ),
                  ],
                  lineTouchData: LineTouchData(
                    touchTooltipData: LineTouchTooltipData(
                      getTooltipColor: (touchedSpot) =>
                          Colors.black.withAlpha((0.8 * 255).toInt()),
                      getTooltipItems: (touchedSpots) {
                        return touchedSpots.map((spot) {
                          final index = spot.x.toInt();
                          String date = '';

                          // For weekly view
                          if (isWeekly &&
                              sortedWeeks != null &&
                              index >= 0 &&
                              index < sortedWeeks.length) {
                            final weekNumber = sortedWeeks[index];
                            final weekOrders = weeklyGroups![weekNumber]!;
                            final firstDate = weekOrders.first.date;
                            final lastDate = weekOrders.last.date;
                            date =
                                'Week ${index + 1} (${DateFormat('EEE dd/MM').format(firstDate)} - ${DateFormat('EEE dd/MM').format(lastDate)})';
                          }
                          // For daily view
                          else if (!isWeekly &&
                              index >= 0 &&
                              index < sortedOrders.length) {
                            date = DateHelper.formatForDisplay(
                              sortedOrders[index].date,
                            );
                          }

                          return LineTooltipItem(
                            '$date\nCompleted: ${spot.y.toInt()}',
                            const TextStyle(
                              color: AppColors.success,
                              fontWeight: FontWeight.bold,
                            ),
                          );
                        }).toList();
                      },
                    ),
                  ),
                  borderData: FlBorderData(
                    show: true,
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  minX: 0,
                  maxX: spots.isNotEmpty ? spots.length - 1.0 : 0,
                  clipData:
                      const FlClipData.all(), // Ensure chart is clipped properly
                ),
              ),
            ),
          ),
        ),
        const SizedBox(height: 8),
        // Legend and average
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _buildLegendItem(
              'Total Completed',
              AppColors.success,
              sortedOrders.fold<int>(
                0,
                (sum, order) => sum + order.orderCompleted,
              ),
            ),
            Text(
              'Avg: ${average.round()}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.success,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ],
    );
  }
}

import 'package:flutter/material.dart';

import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/utils/calculations.dart';
import '../../../../../core/utils/date_helper.dart';
import '../../domain/entities/order.dart';
import 'order_detail_item.dart';
import 'order_detail_section.dart';
import 'order_metric_progress.dart';

class OrderDetailsBottomSheet extends StatelessWidget {
  final Order order;

  const OrderDetailsBottomSheet({super.key, required this.order});

  static void show(BuildContext context, Order order) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => OrderDetailsBottomSheet(order: order),
    );
  }

  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      initialChildSize: 0.65,
      minChildSize: 0.3,
      maxChildSize: 0.95,
      expand: false,
      builder: (context, scrollController) {
        return Container(
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
          ),
          child: Column(
            children: [
              // Drag handle
              Container(
                margin: const EdgeInsets.only(top: 8, bottom: 4),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey.withAlpha(80),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  physics: const BouncingScrollPhysics(),
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(16.0, 8.0, 16.0, 24.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header with date
                        Padding(
                          padding: const EdgeInsets.only(bottom: 16.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: [
                                  const Icon(
                                    Icons.assignment,
                                    color: AppColors.primary,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Order Details',
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleLarge
                                        ?.copyWith(fontWeight: FontWeight.bold),
                                  ),
                                ],
                              ),
                              IconButton(
                                icon: const Icon(Icons.close, size: 20),
                                onPressed: () => Navigator.pop(context),
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints(
                                  minWidth: 32,
                                  minHeight: 32,
                                ),
                              ),
                            ],
                          ),
                        ),

                        // Date and summary
                        Card(
                          elevation: 0,
                          color: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                            side: BorderSide(
                              color: AppColors.primary.withAlpha(30),
                              width: 1,
                            ),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Date
                                Text(
                                  DateHelper.formatForDisplay(order.date),
                                  style: Theme.of(context).textTheme.titleMedium
                                      ?.copyWith(
                                        fontWeight: FontWeight.bold,
                                        color: AppColors.primary,
                                      ),
                                ),

                                const SizedBox(height: 10),
                                const Divider(height: 1),
                                const SizedBox(height: 10),

                                // Stats row in one line: completed orders, points, income
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    // Orders completed
                                    Text(
                                      '${order.orderCompleted} orders',
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodyMedium
                                          ?.copyWith(
                                            fontWeight: FontWeight.w500,
                                          ),
                                    ),

                                    // Points
                                    Text(
                                      '${order.points} pts',
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodyMedium
                                          ?.copyWith(
                                            fontWeight: FontWeight.w500,
                                          ),
                                    ),

                                    // Income
                                    Text(
                                      Calculations.formatCurrency(
                                        order.income ?? 0,
                                      ),
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodyMedium
                                          ?.copyWith(
                                            fontWeight: FontWeight.w500,
                                            color: AppColors.success,
                                          ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Order Statistics Section
                        OrderDetailSection(
                          title: 'Order Statistics',
                          icon: Icons.assignment,
                          color: AppColors.info,
                          children: [
                            OrderDetailItem(
                              label: 'Incoming Orders',
                              value: '${order.incomingOrder}',
                              icon: Icons.arrow_downward,
                            ),
                            OrderDetailItem(
                              label: 'Orders Received',
                              value: '${order.orderReceived}',
                              icon: Icons.inbox,
                            ),
                            OrderDetailItem(
                              label: 'Completed Orders',
                              value: '${order.orderCompleted}',
                              icon: Icons.check_circle_outline,
                            ),
                            OrderDetailItem(
                              label: 'Missed Orders',
                              value: '${order.orderMissed}',
                              icon: Icons.cancel_outlined,
                            ),
                            OrderDetailItem(
                              label: 'Canceled Orders',
                              value: '${order.orderCanceled}',
                              icon: Icons.block,
                            ),
                            OrderDetailItem(
                              label: 'CBS Orders',
                              value: '${order.cbsOrder}',
                              icon: Icons.local_shipping,
                            ),
                          ],
                        ),

                        const SizedBox(height: 16),

                        // Performance Metrics Section
                        OrderDetailSection(
                          title: 'Performance Metrics',
                          icon: Icons.trending_up,
                          color: AppColors.primary,
                          children: [
                            OrderMetricProgress(
                              label: 'BID Acceptance',
                              value: Calculations.formatPercentage(
                                order.bidAcceptance ?? 0,
                              ),
                              progress: (order.bidAcceptance ?? 0),
                              color: AppColors.primary,
                            ),
                            const SizedBox(height: 12),
                            OrderMetricProgress(
                              label: 'Trip Completion',
                              value: Calculations.formatPercentage(
                                order.tripCompletion ?? 0,
                              ),
                              progress: (order.tripCompletion ?? 0),
                              color: AppColors.secondary,
                            ),
                            const SizedBox(height: 12),
                            OrderDetailItem(
                              label: 'Points Earned',
                              value: '${order.points}',
                              icon: Icons.stars,
                            ),
                          ],
                        ),

                        const SizedBox(height: 16),

                        // Financial Section
                        OrderDetailSection(
                          title: 'Financial',
                          icon: Icons.account_balance_wallet,
                          color: AppColors.primary,
                          children: [
                            OrderDetailItem(
                              label: 'Trip Income',
                              value: Calculations.formatCurrency(order.trip),
                              icon: Icons.directions_car,
                            ),
                            OrderDetailItem(
                              label: 'Bonus',
                              value: Calculations.formatCurrency(order.bonus),
                              icon: Icons.card_giftcard,
                            ),
                            OrderDetailItem(
                              label: 'Tips',
                              value: Calculations.formatCurrency(order.tips),
                              icon: Icons.thumb_up,
                            ),
                            const Divider(),
                            OrderDetailItem(
                              label: 'Total Income',
                              value: Calculations.formatCurrency(
                                order.income ?? 0,
                              ),
                              icon: Icons.payments,
                              isTotal: true,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

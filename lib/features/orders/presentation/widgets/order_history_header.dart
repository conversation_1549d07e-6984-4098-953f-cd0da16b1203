import 'package:flutter/material.dart';

class OrderHistoryHeader extends StatefulWidget {
  const OrderHistoryHeader({super.key});

  @override
  State<OrderHistoryHeader> createState() => _OrderHistoryHeaderState();
}

class _OrderHistoryHeaderState extends State<OrderHistoryHeader> {
  bool _showHeader = false;

  @override
  void initState() {
    super.initState();
    // Add a delay before showing the header
    // This ensures the metrics section is fully rendered first
    Future.delayed(const Duration(milliseconds: 250), () {
      if (mounted) {
        setState(() {
          _showHeader = true;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedOpacity(
      opacity: _showHeader ? 1.0 : 0.0,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        transform: Matrix4.translationValues(
          0.0,
          _showHeader ? 0.0 : 20.0,
          0.0,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Order History',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: Text(
                'Your past order records',
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/utils/calculations.dart';
import '../../../../../core/utils/date_helper.dart';
import '../../domain/entities/order.dart';
import 'order_metric_progress.dart';

class OrderListItem extends ConsumerWidget {
  final Order order;
  final void Function(BuildContext, Order) onTap;
  final void Function(BuildContext, Order) onLongPress;

  const OrderListItem({
    super.key,
    required this.order,
    required this.onTap,
    required this.onLongPress,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: AppColors.primary.withAlpha(30), width: 1),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => onTap(context, order),
        onLongPress: () => onLongPress(context, order),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with date
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    DateHelper.formatForDisplay(order.date),
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 8),

              // Stats row in one line: completed orders, points, income
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Orders completed
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        '${order.orderCompleted} orders',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),

                  // Points
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        '${order.points} pts',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),

                  // Income
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        Calculations.formatCurrency(order.income ?? 0),
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                          color: AppColors.success,
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 10),
              const Divider(height: 1),
              const SizedBox(height: 10),

              // Performance metrics with progress bars
              Row(
                children: [
                  Expanded(
                    child: OrderMetricProgress(
                      label: 'BID',
                      value: Calculations.formatPercentage(
                        order.bidAcceptance ?? 0,
                      ),
                      progress: (order.bidAcceptance ?? 0),
                      color: AppColors.primary,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: OrderMetricProgress(
                      label: 'Trip',
                      value: Calculations.formatPercentage(
                        order.tripCompletion ?? 0,
                      ),
                      progress: (order.tripCompletion ?? 0),
                      color: AppColors.secondary,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 10),

              // Income section
              Row(
                children: [
                  const Icon(
                    Icons.account_balance_wallet,
                    color: AppColors.success,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Total Income:',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    Calculations.formatCurrency(order.income ?? 0),
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.success,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

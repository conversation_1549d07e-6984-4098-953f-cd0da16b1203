import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../../../core/widgets/base_form_screen.dart';
import '../../../../core/widgets/currency_input_field.dart';
import '../../../../core/widgets/integer_input_field.dart';
import '../../domain/entities/order.dart';
import '../providers/order_providers.dart';

class OrderFormScreen extends BaseFormScreen<Order> {
  const OrderFormScreen({super.key, Order? order}) : super(entity: order);

  @override
  ConsumerState<OrderFormScreen> createState() => _OrderFormScreenState();
}

class _OrderFormScreenState
    extends BaseFormScreenState<Order, OrderFormScreen> {
  // Order statistics controllers
  final _orderCompletedController = TextEditingController();
  final _orderMissedController = TextEditingController();
  final _orderCanceledController = TextEditingController();
  final _cbsOrderController = TextEditingController();
  final _pointsController = TextEditingController();

  // Financial controllers
  final _tripController = TextEditingController();
  final _bonusController = TextEditingController();
  final _tipsController = TextEditingController();

  @override
  DateTime getInitialDate() {
    return widget.entity?.date ?? DateTime.now();
  }

  @override
  void initializeControllers() {
    if (isEditing) {
      // Populate form with existing data, but don't show 0 values
      _orderCompletedController.text = widget.entity!.orderCompleted == 0
          ? ''
          : widget.entity!.orderCompleted.toString();
      _orderMissedController.text = widget.entity!.orderMissed == 0
          ? ''
          : widget.entity!.orderMissed.toString();
      _orderCanceledController.text = widget.entity!.orderCanceled == 0
          ? ''
          : widget.entity!.orderCanceled.toString();
      _cbsOrderController.text = widget.entity!.cbsOrder == 0
          ? ''
          : widget.entity!.cbsOrder.toString();
      _pointsController.text = widget.entity!.points == 0
          ? ''
          : widget.entity!.points.toString();

      _tripController.text = widget.entity!.trip == 0
          ? ''
          : widget.entity!.trip.toString();
      _bonusController.text = widget.entity!.bonus == 0
          ? ''
          : widget.entity!.bonus.toString();
      _tipsController.text = widget.entity!.tips == 0
          ? ''
          : widget.entity!.tips.toString();
    }
  }

  @override
  void disposeControllers() {
    _orderCompletedController.dispose();
    _orderMissedController.dispose();
    _orderCanceledController.dispose();
    _cbsOrderController.dispose();
    _pointsController.dispose();

    _tripController.dispose();
    _bonusController.dispose();
    _tipsController.dispose();
  }

  @override
  String getFormTitle() {
    return isEditing ? 'Edit Order Record' : 'Add Order Record';
  }

  @override
  Future<bool> checkDateExists() async {
    final repository = ref.read(orderRepositoryProvider);
    final dateExistsResult = await repository.checkDateExists(
      selectedDate,
      excludeId: isEditing ? widget.entity!.id : null,
    );

    return dateExistsResult.fold((failure) {
      // If there was an error checking the date, show an error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error checking date: ${failure.toString()}'),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return false; // Assume date doesn't exist if there was an error
    }, (exists) => exists);
  }

  @override
  Future<void> submitForm() async {
    final order = Order(
      id: widget.entity?.id,
      date: selectedDate,
      orderCompleted: int.parse(
        _orderCompletedController.text.isEmpty
            ? '0'
            : _orderCompletedController.text,
      ),
      orderMissed: int.parse(
        _orderMissedController.text.isEmpty ? '0' : _orderMissedController.text,
      ),
      orderCanceled: int.parse(
        _orderCanceledController.text.isEmpty
            ? '0'
            : _orderCanceledController.text,
      ),
      cbsOrder: int.parse(
        _cbsOrderController.text.isEmpty ? '0' : _cbsOrderController.text,
      ),
      points: int.parse(
        _pointsController.text.isEmpty ? '0' : _pointsController.text,
      ),
      trip: double.parse(
        _tripController.text.isEmpty ? '0' : _tripController.text,
      ),
      bonus: double.parse(
        _bonusController.text.isEmpty ? '0' : _bonusController.text,
      ),
      tips: double.parse(
        _tipsController.text.isEmpty ? '0' : _tipsController.text,
      ),
    );

    bool success;

    if (isEditing) {
      success = await ref.read(orderListProvider.notifier).updateOrder(order);
    } else {
      success = await ref.read(orderListProvider.notifier).addOrder(order);
    }

    // Show success or error message
    if (mounted) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '${isEditing ? 'Updated' : 'Added'} order record successfully',
            ),
            behavior: SnackBarBehavior.floating,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to ${isEditing ? 'update' : 'add'} order record',
            ),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  @override
  List<Widget> buildFormFields() {
    return [
      buildSectionTitle('Order Counts', icon: Icons.format_list_numbered),
      IntegerInputField(
        label: 'Completed Orders',
        controller: _orderCompletedController,
        isRequired: true,
      ),
      IntegerInputField(
        label: 'Missed Orders',
        controller: _orderMissedController,
      ),
      IntegerInputField(
        label: 'Canceled Orders',
        controller: _orderCanceledController,
      ),
      IntegerInputField(label: 'CBS Orders', controller: _cbsOrderController),

      const SizedBox(height: 24),
      buildSectionTitle('Earnings', icon: Icons.payments),
      IntegerInputField(
        label: 'Points Earned',
        controller: _pointsController,
        isRequired: true,
      ),
      CurrencyInputField(
        label: 'Trip Income',
        controller: _tripController,
        allowDecimal: true,
        isRequired: true,
      ),
      CurrencyInputField(
        label: 'Bonus',
        controller: _bonusController,
        allowDecimal: true,
      ),
      CurrencyInputField(
        label: 'Tips',
        controller: _tipsController,
        allowDecimal: true,
      ),
    ];
  }
}

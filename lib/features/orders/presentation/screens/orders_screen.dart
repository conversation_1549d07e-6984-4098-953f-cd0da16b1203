import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../core/providers/global_date_range_provider.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/date_helper.dart';
import '../../../../core/utils/snackbar_utils.dart';
import '../../../../core/widgets/date_range_selector_field.dart';
import '../../../../core/widgets/item_actions_bottom_sheet.dart';
import '../../../../core/widgets/shimmer/shimmer_components.dart';
import '../../domain/entities/order.dart';
import '../providers/order_providers.dart';
import '../widgets/empty_order_list.dart';
import '../widgets/order_details_bottom_sheet.dart';
import '../widgets/order_history_header.dart';
import '../widgets/order_list_item.dart';
import '../widgets/order_metrics.dart';
import '../widgets/order_standardized_shimmer_loading.dart';
import '../widgets/order_trends_card.dart';
import 'order_form_screen.dart';

class OrdersScreen extends ConsumerStatefulWidget {
  const OrdersScreen({super.key});

  @override
  ConsumerState<OrdersScreen> createState() => _OrdersScreenState();
}

class _OrdersScreenState extends ConsumerState<OrdersScreen> {
  @override
  Widget build(BuildContext context) {
    // Watch providers for reactive updates
    final orderListAsync = ref.watch(filteredOrderListProvider);
    final dateRangeAsync = ref.watch(globalDateRangeProvider);
    final orderSummaryAsync = ref.watch(orderSummaryProvider);

    // Check if any of the async values are in loading state
    final bool isLoading =
        orderListAsync.isLoading ||
        dateRangeAsync.isLoading ||
        orderSummaryAsync.isLoading;

    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () async {
          ref.invalidate(orderListProvider);
        },
        child: CustomScrollView(
          slivers: [
            _buildAppBar(dateRangeAsync),

            // If loading, show unified shimmer loading, otherwise show content
            if (isLoading)
              _buildLoadingState()
            else ...[
              _buildSummarySection(context, orderSummaryAsync),
              _buildHistoryHeader(),
              _buildOrderListSection(orderListAsync),
            ],
          ],
        ),
      ),
    );
  }

  /// Builds the app bar with date range selector
  SliverAppBar _buildAppBar(AsyncValue<DateTimeRange> dateRangeAsync) {
    return SliverAppBar(
      floating: true,
      pinned: true,
      elevation: 0,
      backgroundColor: AppColors.primary,
      title: const Text('Orders'),
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(60),
        child: Padding(
          padding: const EdgeInsets.fromLTRB(16.0, 4.0, 16.0, 12.0),
          child: dateRangeAsync.when(
            data: (dateRange) =>
                _buildDateRangeSelector(context, ref, dateRange),
            loading: () => _buildDateRangeSelectorShimmer(),
            error: (error, stack) => _buildErrorContainer(error),
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.add),
          tooltip: 'Add new order',
          onPressed: () => _navigateToOrderForm(context),
        ),
      ],
    );
  }

  /// Builds a loading state for the shimmer effect
  Widget _buildLoadingState() {
    return const OrderStandardizedShimmerLoading();
  }

  /// Builds the date range selector widget
  Widget _buildDateRangeSelector(
    BuildContext context,
    WidgetRef ref,
    DateTimeRange dateRange,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withAlpha(30),
        borderRadius: BorderRadius.circular(8),
      ),
      child: DateRangeSelectorField(
        dateRange: dateRange,
        onDateRangeSelected: (newRange) async {
          await ref
              .read(globalDateRangeProvider.notifier)
              .setDateRange(newRange);
        },
      ),
    );
  }

  /// Builds a shimmer effect for the date range selector
  Widget _buildDateRangeSelectorShimmer() {
    return const ShimmerDateRangeSelector();
  }

  /// Builds an error container with the given error message
  Widget _buildErrorContainer(Object error) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.red.withAlpha(30),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          const Icon(Icons.error_outline, color: Colors.red, size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'Error: $error',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  // Removed unused shimmer methods as they've been replaced by OrderStandardizedShimmerLoading

  /// Builds the summary section with order metrics and analytics
  Widget _buildSummarySection(
    BuildContext context,
    AsyncValue<dynamic> orderSummaryAsync,
  ) {
    // Get the filtered order list for analytics
    final orderListAsync = ref.watch(filteredOrderListProvider);

    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 0.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Summary',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            Padding(
              padding: const EdgeInsets.only(bottom: 12.0),
              child: Text(
                'Overview of your order performance and earnings',
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
              ),
            ),
            // Order metrics card
            orderSummaryAsync.maybeWhen(
              data: (summary) => OrderMetrics(summary: summary),
              error: (error, stack) => _buildErrorContainer(error),
              orElse: () => const SizedBox.shrink(),
            ),

            // Add spacing between cards
            const SizedBox(height: 20),

            // Order trends card
            orderListAsync.maybeWhen(
              data: (orderList) => orderList.isNotEmpty
                  ? OrderTrendsCard(orders: orderList)
                  : const SizedBox.shrink(),
              error: (error, stack) => _buildErrorContainer(error),
              orElse: () => const SizedBox.shrink(),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the history header section
  Widget _buildHistoryHeader() {
    return const SliverToBoxAdapter(
      child: Padding(
        padding: EdgeInsets.fromLTRB(16.0, 24.0, 16.0, 8.0),
        child: OrderHistoryHeader(),
      ),
    );
  }

  /// Builds the order list section
  Widget _buildOrderListSection(AsyncValue<List<Order>> orderListAsync) {
    return orderListAsync.maybeWhen(
      data: (orderList) => _buildOrderList(context, ref, orderList),
      error: (error, stack) =>
          SliverFillRemaining(child: Center(child: Text('Error: $error'))),
      orElse: () => const SliverToBoxAdapter(child: SizedBox.shrink()),
    );
  }

  SliverList _buildOrderList(
    BuildContext context,
    WidgetRef ref,
    List<Order> orderList,
  ) {
    if (orderList.isEmpty) {
      return SliverList(
        delegate: SliverChildListDelegate([
          EmptyOrderList(onAddPressed: () => _navigateToOrderForm(context)),
        ]),
      );
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate((context, index) {
        final order = orderList[index];
        return OrderListItem(
          order: order,
          onTap: _showOrderDetails,
          onLongPress: _showActionsBottomSheet,
        );
      }, childCount: orderList.length),
    );
  }

  void _navigateToOrderForm(BuildContext context, {Order? order}) {
    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (context) => OrderFormScreen(order: order),
          ),
        )
        .then((_) {
          // Refresh the list when returning from the form screen
          ref.invalidate(orderListProvider);
        });
  }

  void _showActionsBottomSheet(BuildContext context, Order order) {
    ItemActionsBottomSheet.show(
      context: context,
      title: 'Order Record',
      subtitle: 'Date: ${DateHelper.formatForDisplay(order.date)}',
      onEdit: () => _navigateToOrderForm(context, order: order),
      onDelete: () => _showDeleteConfirmation(context, order),
      itemIcon: Icons.assignment,
    );
  }

  void _showDeleteConfirmation(BuildContext context, Order order) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Order Record'),
        content: Text(
          'Are you sure you want to delete the order record for ${DateHelper.formatForDisplay(order.date)}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _deleteOrder(order);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _deleteOrder(Order order) async {
    if (order.id == null) return;

    // Show loading indicator
    SnackbarUtils.showLoading(message: 'Deleting...');

    // Use the provider to delete the order record
    final success = await ref
        .read(orderListProvider.notifier)
        .deleteOrder(order.id!);

    if (mounted) {
      if (success) {
        SnackbarUtils.showSuccess('Order record deleted successfully');
      } else {
        SnackbarUtils.showError('Failed to delete order record');
      }
    }
  }

  void _showOrderDetails(BuildContext context, Order order) {
    OrderDetailsBottomSheet.show(context, order);
  }
}

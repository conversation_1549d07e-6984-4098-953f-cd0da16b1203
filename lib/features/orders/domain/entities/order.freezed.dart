// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'order.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Order {

 int? get id; DateTime get date; int get orderCompleted; int get orderMissed; int get orderCanceled; int get cbsOrder; int? get incomingOrder; int? get orderReceived; double? get bidAcceptance; double? get tripCompletion; int get points; double get trip; double get bonus; double get tips; double? get income;
/// Create a copy of Order
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$OrderCopyWith<Order> get copyWith => _$OrderCopyWithImpl<Order>(this as Order, _$identity);

  /// Serializes this Order to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Order&&(identical(other.id, id) || other.id == id)&&(identical(other.date, date) || other.date == date)&&(identical(other.orderCompleted, orderCompleted) || other.orderCompleted == orderCompleted)&&(identical(other.orderMissed, orderMissed) || other.orderMissed == orderMissed)&&(identical(other.orderCanceled, orderCanceled) || other.orderCanceled == orderCanceled)&&(identical(other.cbsOrder, cbsOrder) || other.cbsOrder == cbsOrder)&&(identical(other.incomingOrder, incomingOrder) || other.incomingOrder == incomingOrder)&&(identical(other.orderReceived, orderReceived) || other.orderReceived == orderReceived)&&(identical(other.bidAcceptance, bidAcceptance) || other.bidAcceptance == bidAcceptance)&&(identical(other.tripCompletion, tripCompletion) || other.tripCompletion == tripCompletion)&&(identical(other.points, points) || other.points == points)&&(identical(other.trip, trip) || other.trip == trip)&&(identical(other.bonus, bonus) || other.bonus == bonus)&&(identical(other.tips, tips) || other.tips == tips)&&(identical(other.income, income) || other.income == income));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,date,orderCompleted,orderMissed,orderCanceled,cbsOrder,incomingOrder,orderReceived,bidAcceptance,tripCompletion,points,trip,bonus,tips,income);

@override
String toString() {
  return 'Order(id: $id, date: $date, orderCompleted: $orderCompleted, orderMissed: $orderMissed, orderCanceled: $orderCanceled, cbsOrder: $cbsOrder, incomingOrder: $incomingOrder, orderReceived: $orderReceived, bidAcceptance: $bidAcceptance, tripCompletion: $tripCompletion, points: $points, trip: $trip, bonus: $bonus, tips: $tips, income: $income)';
}


}

/// @nodoc
abstract mixin class $OrderCopyWith<$Res>  {
  factory $OrderCopyWith(Order value, $Res Function(Order) _then) = _$OrderCopyWithImpl;
@useResult
$Res call({
 int? id, DateTime date, int orderCompleted, int orderMissed, int orderCanceled, int cbsOrder, int? incomingOrder, int? orderReceived, double? bidAcceptance, double? tripCompletion, int points, double trip, double bonus, double tips, double? income
});




}
/// @nodoc
class _$OrderCopyWithImpl<$Res>
    implements $OrderCopyWith<$Res> {
  _$OrderCopyWithImpl(this._self, this._then);

  final Order _self;
  final $Res Function(Order) _then;

/// Create a copy of Order
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? date = null,Object? orderCompleted = null,Object? orderMissed = null,Object? orderCanceled = null,Object? cbsOrder = null,Object? incomingOrder = freezed,Object? orderReceived = freezed,Object? bidAcceptance = freezed,Object? tripCompletion = freezed,Object? points = null,Object? trip = null,Object? bonus = null,Object? tips = null,Object? income = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,date: null == date ? _self.date : date // ignore: cast_nullable_to_non_nullable
as DateTime,orderCompleted: null == orderCompleted ? _self.orderCompleted : orderCompleted // ignore: cast_nullable_to_non_nullable
as int,orderMissed: null == orderMissed ? _self.orderMissed : orderMissed // ignore: cast_nullable_to_non_nullable
as int,orderCanceled: null == orderCanceled ? _self.orderCanceled : orderCanceled // ignore: cast_nullable_to_non_nullable
as int,cbsOrder: null == cbsOrder ? _self.cbsOrder : cbsOrder // ignore: cast_nullable_to_non_nullable
as int,incomingOrder: freezed == incomingOrder ? _self.incomingOrder : incomingOrder // ignore: cast_nullable_to_non_nullable
as int?,orderReceived: freezed == orderReceived ? _self.orderReceived : orderReceived // ignore: cast_nullable_to_non_nullable
as int?,bidAcceptance: freezed == bidAcceptance ? _self.bidAcceptance : bidAcceptance // ignore: cast_nullable_to_non_nullable
as double?,tripCompletion: freezed == tripCompletion ? _self.tripCompletion : tripCompletion // ignore: cast_nullable_to_non_nullable
as double?,points: null == points ? _self.points : points // ignore: cast_nullable_to_non_nullable
as int,trip: null == trip ? _self.trip : trip // ignore: cast_nullable_to_non_nullable
as double,bonus: null == bonus ? _self.bonus : bonus // ignore: cast_nullable_to_non_nullable
as double,tips: null == tips ? _self.tips : tips // ignore: cast_nullable_to_non_nullable
as double,income: freezed == income ? _self.income : income // ignore: cast_nullable_to_non_nullable
as double?,
  ));
}

}


/// Adds pattern-matching-related methods to [Order].
extension OrderPatterns on Order {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Order value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Order() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Order value)  $default,){
final _that = this;
switch (_that) {
case _Order():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Order value)?  $default,){
final _that = this;
switch (_that) {
case _Order() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int? id,  DateTime date,  int orderCompleted,  int orderMissed,  int orderCanceled,  int cbsOrder,  int? incomingOrder,  int? orderReceived,  double? bidAcceptance,  double? tripCompletion,  int points,  double trip,  double bonus,  double tips,  double? income)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Order() when $default != null:
return $default(_that.id,_that.date,_that.orderCompleted,_that.orderMissed,_that.orderCanceled,_that.cbsOrder,_that.incomingOrder,_that.orderReceived,_that.bidAcceptance,_that.tripCompletion,_that.points,_that.trip,_that.bonus,_that.tips,_that.income);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int? id,  DateTime date,  int orderCompleted,  int orderMissed,  int orderCanceled,  int cbsOrder,  int? incomingOrder,  int? orderReceived,  double? bidAcceptance,  double? tripCompletion,  int points,  double trip,  double bonus,  double tips,  double? income)  $default,) {final _that = this;
switch (_that) {
case _Order():
return $default(_that.id,_that.date,_that.orderCompleted,_that.orderMissed,_that.orderCanceled,_that.cbsOrder,_that.incomingOrder,_that.orderReceived,_that.bidAcceptance,_that.tripCompletion,_that.points,_that.trip,_that.bonus,_that.tips,_that.income);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int? id,  DateTime date,  int orderCompleted,  int orderMissed,  int orderCanceled,  int cbsOrder,  int? incomingOrder,  int? orderReceived,  double? bidAcceptance,  double? tripCompletion,  int points,  double trip,  double bonus,  double tips,  double? income)?  $default,) {final _that = this;
switch (_that) {
case _Order() when $default != null:
return $default(_that.id,_that.date,_that.orderCompleted,_that.orderMissed,_that.orderCanceled,_that.cbsOrder,_that.incomingOrder,_that.orderReceived,_that.bidAcceptance,_that.tripCompletion,_that.points,_that.trip,_that.bonus,_that.tips,_that.income);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Order extends Order {
  const _Order({this.id, required this.date, required this.orderCompleted, required this.orderMissed, required this.orderCanceled, required this.cbsOrder, this.incomingOrder, this.orderReceived, this.bidAcceptance, this.tripCompletion, required this.points, required this.trip, required this.bonus, required this.tips, this.income}): super._();
  factory _Order.fromJson(Map<String, dynamic> json) => _$OrderFromJson(json);

@override final  int? id;
@override final  DateTime date;
@override final  int orderCompleted;
@override final  int orderMissed;
@override final  int orderCanceled;
@override final  int cbsOrder;
@override final  int? incomingOrder;
@override final  int? orderReceived;
@override final  double? bidAcceptance;
@override final  double? tripCompletion;
@override final  int points;
@override final  double trip;
@override final  double bonus;
@override final  double tips;
@override final  double? income;

/// Create a copy of Order
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OrderCopyWith<_Order> get copyWith => __$OrderCopyWithImpl<_Order>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$OrderToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Order&&(identical(other.id, id) || other.id == id)&&(identical(other.date, date) || other.date == date)&&(identical(other.orderCompleted, orderCompleted) || other.orderCompleted == orderCompleted)&&(identical(other.orderMissed, orderMissed) || other.orderMissed == orderMissed)&&(identical(other.orderCanceled, orderCanceled) || other.orderCanceled == orderCanceled)&&(identical(other.cbsOrder, cbsOrder) || other.cbsOrder == cbsOrder)&&(identical(other.incomingOrder, incomingOrder) || other.incomingOrder == incomingOrder)&&(identical(other.orderReceived, orderReceived) || other.orderReceived == orderReceived)&&(identical(other.bidAcceptance, bidAcceptance) || other.bidAcceptance == bidAcceptance)&&(identical(other.tripCompletion, tripCompletion) || other.tripCompletion == tripCompletion)&&(identical(other.points, points) || other.points == points)&&(identical(other.trip, trip) || other.trip == trip)&&(identical(other.bonus, bonus) || other.bonus == bonus)&&(identical(other.tips, tips) || other.tips == tips)&&(identical(other.income, income) || other.income == income));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,date,orderCompleted,orderMissed,orderCanceled,cbsOrder,incomingOrder,orderReceived,bidAcceptance,tripCompletion,points,trip,bonus,tips,income);

@override
String toString() {
  return 'Order(id: $id, date: $date, orderCompleted: $orderCompleted, orderMissed: $orderMissed, orderCanceled: $orderCanceled, cbsOrder: $cbsOrder, incomingOrder: $incomingOrder, orderReceived: $orderReceived, bidAcceptance: $bidAcceptance, tripCompletion: $tripCompletion, points: $points, trip: $trip, bonus: $bonus, tips: $tips, income: $income)';
}


}

/// @nodoc
abstract mixin class _$OrderCopyWith<$Res> implements $OrderCopyWith<$Res> {
  factory _$OrderCopyWith(_Order value, $Res Function(_Order) _then) = __$OrderCopyWithImpl;
@override @useResult
$Res call({
 int? id, DateTime date, int orderCompleted, int orderMissed, int orderCanceled, int cbsOrder, int? incomingOrder, int? orderReceived, double? bidAcceptance, double? tripCompletion, int points, double trip, double bonus, double tips, double? income
});




}
/// @nodoc
class __$OrderCopyWithImpl<$Res>
    implements _$OrderCopyWith<$Res> {
  __$OrderCopyWithImpl(this._self, this._then);

  final _Order _self;
  final $Res Function(_Order) _then;

/// Create a copy of Order
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? date = null,Object? orderCompleted = null,Object? orderMissed = null,Object? orderCanceled = null,Object? cbsOrder = null,Object? incomingOrder = freezed,Object? orderReceived = freezed,Object? bidAcceptance = freezed,Object? tripCompletion = freezed,Object? points = null,Object? trip = null,Object? bonus = null,Object? tips = null,Object? income = freezed,}) {
  return _then(_Order(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,date: null == date ? _self.date : date // ignore: cast_nullable_to_non_nullable
as DateTime,orderCompleted: null == orderCompleted ? _self.orderCompleted : orderCompleted // ignore: cast_nullable_to_non_nullable
as int,orderMissed: null == orderMissed ? _self.orderMissed : orderMissed // ignore: cast_nullable_to_non_nullable
as int,orderCanceled: null == orderCanceled ? _self.orderCanceled : orderCanceled // ignore: cast_nullable_to_non_nullable
as int,cbsOrder: null == cbsOrder ? _self.cbsOrder : cbsOrder // ignore: cast_nullable_to_non_nullable
as int,incomingOrder: freezed == incomingOrder ? _self.incomingOrder : incomingOrder // ignore: cast_nullable_to_non_nullable
as int?,orderReceived: freezed == orderReceived ? _self.orderReceived : orderReceived // ignore: cast_nullable_to_non_nullable
as int?,bidAcceptance: freezed == bidAcceptance ? _self.bidAcceptance : bidAcceptance // ignore: cast_nullable_to_non_nullable
as double?,tripCompletion: freezed == tripCompletion ? _self.tripCompletion : tripCompletion // ignore: cast_nullable_to_non_nullable
as double?,points: null == points ? _self.points : points // ignore: cast_nullable_to_non_nullable
as int,trip: null == trip ? _self.trip : trip // ignore: cast_nullable_to_non_nullable
as double,bonus: null == bonus ? _self.bonus : bonus // ignore: cast_nullable_to_non_nullable
as double,tips: null == tips ? _self.tips : tips // ignore: cast_nullable_to_non_nullable
as double,income: freezed == income ? _self.income : income // ignore: cast_nullable_to_non_nullable
as double?,
  ));
}


}

// dart format on

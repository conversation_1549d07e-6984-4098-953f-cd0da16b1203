import 'package:freezed_annotation/freezed_annotation.dart';

part 'order.freezed.dart';
part 'order.g.dart';

@freezed
sealed class Order with _$Order {
  const Order._();

  const factory Order({
    int? id,
    required DateTime date,
    required int orderCompleted,
    required int orderMissed,
    required int orderCanceled,
    required int cbsOrder,
    int? incomingOrder,
    int? orderReceived,
    double? bidAcceptance,
    double? tripCompletion,
    required int points,
    required double trip,
    required double bonus,
    required double tips,
    double? income,
  }) = _Order;

  factory Order.fromJson(Map<String, dynamic> json) => _$OrderFromJson(json);
}

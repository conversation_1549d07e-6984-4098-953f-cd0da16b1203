// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Order _$OrderFromJson(Map<String, dynamic> json) => _Order(
  id: (json['id'] as num?)?.toInt(),
  date: DateTime.parse(json['date'] as String),
  orderCompleted: (json['orderCompleted'] as num).toInt(),
  orderMissed: (json['orderMissed'] as num).toInt(),
  orderCanceled: (json['orderCanceled'] as num).toInt(),
  cbsOrder: (json['cbsOrder'] as num).toInt(),
  incomingOrder: (json['incomingOrder'] as num?)?.toInt(),
  orderReceived: (json['orderReceived'] as num?)?.toInt(),
  bidAcceptance: (json['bidAcceptance'] as num?)?.toDouble(),
  tripCompletion: (json['tripCompletion'] as num?)?.toDouble(),
  points: (json['points'] as num).toInt(),
  trip: (json['trip'] as num).toDouble(),
  bonus: (json['bonus'] as num).toDouble(),
  tips: (json['tips'] as num).toDouble(),
  income: (json['income'] as num?)?.toDouble(),
);

Map<String, dynamic> _$OrderToJson(_Order instance) => <String, dynamic>{
  'id': instance.id,
  'date': instance.date.toIso8601String(),
  'orderCompleted': instance.orderCompleted,
  'orderMissed': instance.orderMissed,
  'orderCanceled': instance.orderCanceled,
  'cbsOrder': instance.cbsOrder,
  'incomingOrder': instance.incomingOrder,
  'orderReceived': instance.orderReceived,
  'bidAcceptance': instance.bidAcceptance,
  'tripCompletion': instance.tripCompletion,
  'points': instance.points,
  'trip': instance.trip,
  'bonus': instance.bonus,
  'tips': instance.tips,
  'income': instance.income,
};

import 'package:dartz/dartz.dart' hide Order;

import '../../../../core/errors/failures.dart';
import '../../../../core/utils/calculations.dart';
import '../entities/order.dart' as domain;

/// Use case to calculate bid acceptance rate and other derived order metrics
class CalculateBidAcceptance {
  /// Calculate all derived values for an order entity
  /// - incomingOrder
  /// - orderReceived
  /// - bidAcceptance
  /// - tripCompletion
  /// - income
  Either<Failure, domain.Order> execute(domain.Order order) {
    try {
      // Calculate incoming order
      final incomingOrder = Calculations.calculateIncomingOrder(
        orderCompleted: order.orderCompleted,
        orderMissed: order.orderMissed,
        orderCanceled: order.orderCanceled,
        cbsOrder: order.cbsOrder,
      );
      
      // Calculate order received
      final orderReceived = Calculations.calculateOrderReceived(
        orderCompleted: order.orderCompleted,
        orderCanceled: order.orderCanceled,
      );
      
      // Calculate bid acceptance
      final bidAcceptance = Calculations.calculateBidAcceptance(
        orderReceived: orderReceived,
        cbsOrder: order.cbsOrder,
        incomingOrder: incomingOrder,
      );
      
      // Calculate trip completion
      final tripCompletion = Calculations.calculateTripCompletion(
        orderCompleted: order.orderCompleted,
        orderReceived: orderReceived,
      );
      
      // Calculate total income
      final income = Calculations.calculateTotalIncome(
        trip: order.trip,
        bonus: order.bonus,
        tips: order.tips,
      );
      
      // Return updated order with calculated values
      return Right(
        order.copyWith(
          incomingOrder: incomingOrder,
          orderReceived: orderReceived,
          bidAcceptance: bidAcceptance,
          tripCompletion: tripCompletion,
          income: income,
        ),
      );
    } catch (e) {
      return Left(Failure.businessLogic(message: 'Error calculating order metrics: $e'));
    }
  }
}

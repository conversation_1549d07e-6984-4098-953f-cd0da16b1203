import 'package:dartz/dartz.dart' hide Order;
import 'package:drift/drift.dart';
import 'package:uuid/uuid.dart';

import '../../../../core/datasources/app_database.dart' as db;
import '../../../../core/errors/exceptions.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/services/sync/sync_operations.dart';
import '../../../../core/services/sync/sync_service.dart';
import '../../domain/entities/order.dart' as domain;
import '../../domain/repositories/order_repository.dart';
import '../../domain/use_cases/calculate_bid_acceptance.dart';

// Re-export aliases to make mapping easier
const int missingId = -1;

class OrderRepositoryImpl implements OrderRepository {
  final db.AppDatabase database;
  final CalculateBidAcceptance calculateBidAcceptance;
  final SyncService syncService;

  OrderRepositoryImpl({
    required this.database,
    required this.calculateBidAcceptance,
    required this.syncService,
  });

  @override
  Future<Either<Failure, List<domain.Order>>> getAllOrders() async {
    try {
      final ordersList = await database.getAllOrders();

      return Right(ordersList.map((data) => _mapFromData(data)).toList());
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, domain.Order>> getOrderById(int id) async {
    try {
      final query = database.select(database.orders)
        ..where((tbl) => tbl.id.equals(id));

      final orderData = await query.getSingleOrNull();

      if (orderData == null) {
        return const Left(Failure.notFound(message: 'Order record not found'));
      }

      return Right(_mapFromData(orderData));
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, domain.Order>> saveOrder(domain.Order order) async {
    try {
      // Calculate all derived fields
      final calculatedOrder = calculateBidAcceptance.execute(order);

      return calculatedOrder.fold(
        (failure) => Left(failure),
        (calculatedOrder) async {
          final id = await database.insertOrder(_mapToCompanion(calculatedOrder)); // For new records, no need to pass uuid or originalCreatedAt

          return Right(calculatedOrder.copyWith(id: id));
        },
      );
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, domain.Order>> updateOrder(domain.Order order) async {
    try {
      if (order.id == null) {
        return const Left(Failure.invalidInput(message: 'Order ID cannot be null for update'));
      }

      // Get the existing record to preserve the UUID
      final query = database.select(database.orders)
        ..where((tbl) => tbl.id.equals(order.id!));
      final existingRecord = await query.getSingleOrNull();

      if (existingRecord == null) {
        return const Left(Failure.notFound(message: 'Order record not found'));
      }

      // Calculate all derived fields
      final calculatedOrder = calculateBidAcceptance.execute(order);

      return calculatedOrder.fold(
        (failure) => Left(failure),
        (calculatedOrder) async {
          // Convert to Order data model first, then update with existing UUID and original createdAt
          final orderData = _mapToDataWithUuid(
            calculatedOrder,
            existingRecord.uuid,
            originalCreatedAt: existingRecord.createdAt
          );
          final success = await database.updateOrder(orderData);

          if (!success) {
            return const Left(Failure.notFound(message: 'Order record not found'));
          }

          return Right(calculatedOrder);
        },
      );
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> deleteOrder(int id) async {
    try {
      // Use soft delete instead of hard delete
      final success = await database.softDeleteOrder(id);

      if (!success) {
        return const Left(Failure.notFound(message: 'Order record not found'));
      }

      // Trigger sync after deleting a record
      syncService.syncData(SyncOperation.upload);

      return const Right(true);
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<domain.Order>>> getOrdersForDateRange(
    DateTime start,
    DateTime end,
  ) async {
    try {
      final query = database.select(database.orders)
        ..where((tbl) => tbl.date.isBetweenValues(
          start,
          end.add(const Duration(days: 1)).subtract(const Duration(microseconds: 1)),
        ))
        ..where((tbl) => tbl.deletedAt.isNull())
        ..orderBy([(t) => OrderingTerm.desc(t.date)]);

      final ordersList = await query.get();

      return Right(ordersList.map((data) => _mapFromData(data)).toList());
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<domain.Order>>> getOrdersForPerformanceCalculation(DateTime endDate) async {
    try {
      // Calculate the date range for the last 14 days (not including the current date)
      // If endDate is March 25, 2025, we want orders from March 11, 2025 to March 24, 2025
      final adjustedEndDate = endDate.subtract(const Duration(days: 1)); // Exclude current date
      final startDate = adjustedEndDate.subtract(const Duration(days: 13)); // 14 days total

      return await getOrdersForDateRange(startDate, adjustedEndDate);
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, int>> getTotalCompletedOrdersForLast14Days(DateTime endDate) async {
    try {
      // Get orders for the last 14 days
      final ordersResult = await getOrdersForPerformanceCalculation(endDate);

      return ordersResult.fold(
        (failure) => Left(failure),
        (orders) {
          // Calculate total completed orders
          int totalOrdersCompleted = 0;
          for (final order in orders) {
            totalOrdersCompleted += order.orderCompleted;
          }

          return Right(totalOrdersCompleted);
        },
      );
    } catch (e) {
      return Left(Failure.unexpected(message: 'Error calculating total completed orders: $e'));
    }
  }

  @override
  Future<Either<Failure, int>> getTotalPointsForMonth(int year, int month) async {
    try {
      final startDate = DateTime(year, month, 1);
      final endDate = DateTime(year, month + 1, 0); // Last day of the month

      final result = await getOrdersForDateRange(startDate, endDate);

      return result.fold(
        (failure) => Left(failure),
        (orders) {
          int totalPoints = 0;
          for (final order in orders) {
            totalPoints += order.points.toInt();
          }
          return Right(totalPoints);
        },
      );
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> checkDateExists(DateTime date, {int? excludeId}) async {
    try {
      // Create a query to find records with the same date
      final query = database.select(database.orders)
        ..where((tbl) => tbl.date.equals(date))
        ..where((tbl) => tbl.deletedAt.isNull());

      // If excludeId is provided, exclude that record from the check
      // This is useful when updating an existing record
      if (excludeId != null) {
        query.where((tbl) => tbl.id.isNotValue(excludeId));
      }

      // Get the count of records with the same date
      final count = await query.get().then((records) => records.length);

      // Return true if any records were found with the same date
      return Right(count > 0);
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  // Helper methods to map between entity and database models
  domain.Order _mapFromData(db.Order data) {
    return domain.Order(
      id: data.id,
      date: data.date,
      orderCompleted: data.orderCompleted,
      orderMissed: data.orderMissed,
      orderCanceled: data.orderCanceled,
      cbsOrder: data.cbsOrder,
      incomingOrder: data.incomingOrder,
      orderReceived: data.orderReceived,
      bidAcceptance: data.bidAcceptance,
      tripCompletion: data.tripCompletion,
      points: data.points,
      trip: data.trip,
      bonus: data.bonus,
      tips: data.tips,
      income: data.income,
    );
  }



  // Map domain Order to database Order with a specific UUID (for updates)
  db.Order _mapToDataWithUuid(domain.Order order, String uuid, {DateTime? originalCreatedAt}) {
    return db.Order(
      id: order.id!,
      uuid: uuid,
      date: order.date,
      orderCompleted: order.orderCompleted,
      orderMissed: order.orderMissed,
      orderCanceled: order.orderCanceled,
      cbsOrder: order.cbsOrder,
      incomingOrder: order.incomingOrder,
      orderReceived: order.orderReceived,
      bidAcceptance: order.bidAcceptance,
      tripCompletion: order.tripCompletion,
      points: order.points,
      trip: order.trip,
      bonus: order.bonus,
      tips: order.tips,
      income: order.income,
      createdAt: originalCreatedAt ?? DateTime.now(), // Preserve original createdAt if provided
      updatedAt: DateTime.now(),
      deletedAt: null,
      syncStatus: db.SyncStatus.pendingUpload,
    );
  }

  db.OrdersCompanion _mapToCompanion(domain.Order order, {String? uuid, DateTime? originalCreatedAt}) {
    return db.OrdersCompanion(
      id: order.id != null ? Value(order.id!) : const Value.absent(),
      uuid: uuid != null ? Value(uuid) : Value(const Uuid().v4()),
      date: Value(order.date),
      orderCompleted: Value(order.orderCompleted),
      orderMissed: Value(order.orderMissed),
      orderCanceled: Value(order.orderCanceled),
      cbsOrder: Value(order.cbsOrder),
      incomingOrder: Value(order.incomingOrder),
      orderReceived: Value(order.orderReceived),
      bidAcceptance: order.bidAcceptance != null ? Value(order.bidAcceptance!) : const Value.absent(),
      tripCompletion: order.tripCompletion != null ? Value(order.tripCompletion!) : const Value.absent(),
      points: Value(order.points),
      trip: Value(order.trip),
      bonus: Value(order.bonus),
      tips: Value(order.tips),
      income: order.income != null ? Value(order.income!) : const Value.absent(),
      createdAt: originalCreatedAt != null ? Value(originalCreatedAt) : Value(DateTime.now()), // Preserve original createdAt if provided
      updatedAt: Value(DateTime.now()),
      syncStatus: const Value(db.SyncStatus.pendingUpload),
    );
  }
}

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:path_provider/path_provider.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../backup/presentation/providers/backup_providers.dart';
import 'settings_card.dart';

/// A card widget for backup directory settings
class BackupDirectorySettingsCard extends ConsumerWidget {
  final String? currentPath;

  const BackupDirectorySettingsCard({super.key, required this.currentPath});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Extract the last part of the path for a more user-friendly display
    String displayName = currentPath ?? 'Default directory';
    if (currentPath != null) {
      final pathParts = currentPath!.split('/');
      if (pathParts.length > 2) {
        // Show the last two directories in the path
        displayName =
            '.../${pathParts[pathParts.length - 2]}/${pathParts.last}';
      }
    }

    return SettingsCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(context),
          const SizedBox(height: 8),
          _buildDescription(context),
          const SizedBox(height: 16),
          _buildDirectorySelector(context, ref, displayName),
          if (currentPath != null) ...[
            const SizedBox(height: 12),
            _buildResetButton(context, ref),
            const SizedBox(height: 16),
            _buildFullPathInfo(context),
          ],
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        const Icon(Icons.folder, color: AppColors.warning, size: 18),
        const SizedBox(width: 8),
        Text(
          'Backup Directory',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  Widget _buildDescription(BuildContext context) {
    return Text(
      'Choose where to store your backup files',
      style: Theme.of(
        context,
      ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
    );
  }

  Widget _buildDirectorySelector(
    BuildContext context,
    WidgetRef ref,
    String displayName,
  ) {
    return InkWell(
      onTap: () => _selectBackupDirectory(context, ref),
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.grey.shade50,
          border: Border.all(color: Colors.grey.withAlpha(50)),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            const Icon(Icons.folder, color: AppColors.primary, size: 16),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Current Directory',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    displayName,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            const Icon(Icons.edit, color: Colors.grey, size: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildResetButton(BuildContext context, WidgetRef ref) {
    return ElevatedButton.icon(
      onPressed: () {
        _clearBackupDirectory(ref);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Reset to default backup directory'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      },
      icon: const Icon(Icons.restore, size: 16),
      label: const Text('Reset to Default'),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.grey.shade200,
        foregroundColor: Colors.black87,
        padding: const EdgeInsets.symmetric(vertical: 10),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        minimumSize: const Size(double.infinity, 40),
        elevation: 0,
      ),
    );
  }

  Widget _buildFullPathInfo(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Icon(Icons.info_outline, color: AppColors.warning, size: 14),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Full Path Information',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.warning,
                ),
              ),
              const SizedBox(height: 4),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(
                    color: Colors.grey.withAlpha(30),
                    width: 1,
                  ),
                ),
                child: Text(
                  currentPath!,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[700],
                    fontFamily: 'monospace',
                    fontSize: 11,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Future<void> _selectBackupDirectory(
    BuildContext context,
    WidgetRef ref,
  ) async {
    try {
      // Use our updated method that handles Android 10 properly
      final success = await ref
          .read(backupDirectoryPathProvider.notifier)
          .setBackupDirectory();

      if (success) {
        // Get the updated path to show in the snackbar
        final updatedPath = await ref.read(backupDirectoryPathProvider.future);

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Backup directory set to: ${updatedPath ?? "Default directory"}',
              ),
            ),
          );
        }
      } else {
        if (context.mounted) {
          // Show a dialog explaining the issue
          await showDialog(
            context: context,
            builder: (dialogContext) {
              return AlertDialog(
                title: const Text('Permission Error'),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Cannot access the selected directory. This may be because:',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    _buildErrorListItem(
                      dialogContext,
                      'The app does not have access permissions for this location',
                    ),
                    const SizedBox(height: 4),
                    _buildErrorListItem(
                      dialogContext,
                      'The directory is protected by the system',
                    ),
                    const SizedBox(height: 4),
                    _buildErrorListItem(
                      dialogContext,
                      'You canceled the directory selection',
                    ),
                    const SizedBox(height: 12),
                    const Text(
                      'Please try selecting a different directory or use the app\'s default storage location.',
                      style: TextStyle(fontSize: 14),
                    ),
                    const SizedBox(height: 8),
                    const Row(
                      children: [
                        Icon(
                          Icons.lightbulb_outline,
                          color: AppColors.primary,
                          size: 16,
                        ),
                        SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'You can also enter a directory path manually',
                            style: TextStyle(
                              color: AppColors.primary,
                              fontWeight: FontWeight.w500,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(dialogContext),
                    child: const Text('Cancel'),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.pop(dialogContext);
                      if (context.mounted) {
                        _showManualDirectoryInputDialog(context, ref);
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Enter Manually'),
                  ),
                ],
              );
            },
          );
        }
      }
    } catch (e) {
      // Fallback to manual input if directory picker fails
      if (context.mounted) {
        _showManualDirectoryInputDialog(context, ref);
      }
    }
  }

  // Fallback method to show manual directory input dialog
  Future<void> _showManualDirectoryInputDialog(
    BuildContext context,
    WidgetRef ref,
  ) async {
    // Get default external storage directory as a suggestion
    String suggestedPath = '';
    try {
      if (Platform.isAndroid) {
        final directory = await getExternalStorageDirectory();
        if (directory != null) {
          suggestedPath = '${directory.path}/BidTrakr/backups';
        }
      } else {
        final directory = await getApplicationDocumentsDirectory();
        suggestedPath = '${directory.path}/backups';
      }
    } catch (e) {
      // Continue with empty suggested path
    }

    if (context.mounted) {
      final TextEditingController pathController = TextEditingController();
      pathController.text = suggestedPath;

      await showDialog(
        context: context,
        builder: (dialogContext) => AlertDialog(
          title: const Text('Set Backup Directory'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Enter the full path where you want to store backup files:',
                style: TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 12),
              TextField(
                controller: pathController,
                decoration: InputDecoration(
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: Colors.grey.shade300),
                  ),
                  filled: true,
                  fillColor: Colors.grey.shade50,
                  hintText: 'Enter directory path',
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 12,
                  ),
                  prefixIcon: const Icon(
                    Icons.folder_open,
                    color: AppColors.primary,
                  ),
                ),
                style: const TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 12),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Icon(
                    Icons.lightbulb_outline,
                    color: AppColors.primary,
                    size: 14,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Suggested Path',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: AppColors.primary,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          suggestedPath,
                          style: TextStyle(
                            fontSize: 11,
                            color: Colors.grey.shade700,
                            fontFamily: 'monospace',
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              const Row(
                children: [
                  Icon(Icons.info_outline, color: AppColors.primary, size: 14),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Make sure the path is accessible and writable by the app',
                      style: TextStyle(fontSize: 11, color: AppColors.primary),
                    ),
                  ),
                ],
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(dialogContext),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                final path = pathController.text.trim();
                if (path.isNotEmpty) {
                  // Check if the directory exists or can be created
                  try {
                    final directory = Directory(path);

                    // Create the directory if it doesn't exist
                    if (!directory.existsSync()) {
                      directory.createSync(recursive: true);
                    }

                    // Check if the directory is writable by creating a test file
                    final testFile = File('$path/.test_write_permission');
                    try {
                      await testFile.writeAsString('test');
                      await testFile.delete(); // Clean up after test

                      // Directory is writable, update the backup directory path
                      final backupService = ref.read(backupServiceProvider);
                      final success = await backupService
                          .setCustomBackupDirectory(path);

                      if (success) {
                        ref.invalidate(backupDirectoryPathProvider);
                      } else {
                        throw Exception("Failed to set backup directory");
                      }

                      if (dialogContext.mounted) {
                        Navigator.pop(dialogContext);
                        if (context.mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Backup directory set to: $path'),
                              behavior: SnackBarBehavior.floating,
                            ),
                          );
                        }
                      }
                    } catch (writeError) {
                      if (dialogContext.mounted) {
                        ScaffoldMessenger.of(dialogContext).showSnackBar(
                          const SnackBar(
                            content: Text(
                              'Cannot write to this directory. Please choose a different location.',
                            ),
                            backgroundColor: AppColors.error,
                            behavior: SnackBarBehavior.floating,
                          ),
                        );
                      }
                    }
                  } catch (error) {
                    if (dialogContext.mounted) {
                      ScaffoldMessenger.of(dialogContext).showSnackBar(
                        SnackBar(
                          content: Text('Error creating directory: $error'),
                          backgroundColor: AppColors.error,
                          behavior: SnackBarBehavior.floating,
                        ),
                      );
                    }
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
              ),
              child: const Text('Save'),
            ),
          ],
        ),
      );
    }
  }

  void _clearBackupDirectory(WidgetRef ref) {
    ref.read(backupDirectoryPathProvider.notifier).clearBackupDirectory();
  }

  Widget _buildErrorListItem(BuildContext context, String text) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          margin: const EdgeInsets.only(top: 2),
          width: 4,
          height: 4,
          decoration: const BoxDecoration(
            color: AppColors.error,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: Colors.grey.shade700),
          ),
        ),
      ],
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/providers/global_date_range_provider.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/widgets/date_range_selector_field.dart';
import 'settings_card.dart';

/// A card widget for date range settings
class DateRangeSettingsCard extends ConsumerWidget {
  final DateTimeRange dateRange;

  const DateRangeSettingsCard({super.key, required this.dateRange});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SettingsCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(context),
          const SizedBox(height: 8),
          _buildDescription(context),
          const SizedBox(height: 16),
          _buildDateRangeSelector(context, ref),
          const SizedBox(height: 16),
          _buildInfoNote(context),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        const Icon(Icons.calendar_today, color: AppColors.info, size: 18),
        const SizedBox(width: 8),
        Text(
          'Default Date Range',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  Widget _buildDescription(BuildContext context) {
    return Text(
      'Set the default date range for filtering data across the app',
      style: Theme.of(
        context,
      ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
    );
  }

  Widget _buildDateRangeSelector(BuildContext context, WidgetRef ref) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withAlpha(30), width: 1),
      ),
      padding: const EdgeInsets.all(12),
      child: DateRangeSelectorField(
        dateRange: dateRange,
        onDateRangeSelected: (newRange) async {
          await ref
              .read(globalDateRangeProvider.notifier)
              .setDateRange(newRange);
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Default date range updated'),
                behavior: SnackBarBehavior.floating,
              ),
            );
          }
        },
        borderColor: Colors.grey.withAlpha(50),
      ),
    );
  }

  Widget _buildInfoNote(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Icon(Icons.info_outline, color: AppColors.info, size: 14),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            'This date range will be used as the default filter when you open income, orders, and performance screens.',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
              fontSize: 11,
            ),
          ),
        ),
      ],
    );
  }
}

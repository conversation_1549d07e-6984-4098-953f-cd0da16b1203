import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/utils/calculations.dart';
import '../entities/income.dart';

/// Use case to calculate the net income based on financial data
class CalculateNetIncome {
  /// Calculate all the derived values for an income entity
  /// - initialCapital
  /// - finalResult
  /// - mileage
  /// - netIncome
  Either<Failure, Income> execute(Income income) {
    try {
      // Calculate initial capital
      final initialCapital = Calculations.calculateInitialCapital(
        initialGopay: income.initialGopay,
        initialBca: income.initialBca,
        initialCash: income.initialCash,
        initialOvo: income.initialOvo,
        initialBri: income.initialBri,
        initialRekpon: income.initialRekpon,
      );
      
      // Calculate final result
      final finalResult = Calculations.calculateFinalResult(
        finalGopay: income.finalGopay,
        finalBca: income.finalBca,
        finalCash: income.finalCash,
        finalOvo: income.finalOvo,
        finalBri: income.finalBri,
        finalRekpon: income.finalRekpon,
      );
      
      // Calculate mileage
      final mileage = Calculations.calculateMileage(
        finalMileage: income.finalMileage,
        initialMileage: income.initialMileage,
      );
      
      // Calculate net income
      final netIncome = Calculations.calculateNetIncome(
        finalResult: finalResult,
        initialCapital: initialCapital,
      );
      
      // Return updated income object with calculated values
      return Right(
        income.copyWith(
          initialCapital: initialCapital,
          finalResult: finalResult,
          mileage: mileage,
          netIncome: netIncome,
        ),
      );
    } catch (e) {
      return Left(Failure.businessLogic(message: 'Error calculating net income: $e'));
    }
  }
}

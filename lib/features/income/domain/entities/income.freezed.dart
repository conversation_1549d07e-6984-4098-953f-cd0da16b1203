// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'income.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Income {

 int? get id; DateTime get date; int get initialMileage; int get finalMileage; double get initialGopay; double get initialBca; double get initialCash; double get initialOvo; double get initialBri; double get initialRekpon; double get finalGopay; double get finalBca; double get finalCash; double get finalOvo; double get finalBri; double get finalRekpon; double? get initialCapital; double? get finalResult; int? get mileage; double? get netIncome;
/// Create a copy of Income
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$IncomeCopyWith<Income> get copyWith => _$IncomeCopyWithImpl<Income>(this as Income, _$identity);

  /// Serializes this Income to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Income&&(identical(other.id, id) || other.id == id)&&(identical(other.date, date) || other.date == date)&&(identical(other.initialMileage, initialMileage) || other.initialMileage == initialMileage)&&(identical(other.finalMileage, finalMileage) || other.finalMileage == finalMileage)&&(identical(other.initialGopay, initialGopay) || other.initialGopay == initialGopay)&&(identical(other.initialBca, initialBca) || other.initialBca == initialBca)&&(identical(other.initialCash, initialCash) || other.initialCash == initialCash)&&(identical(other.initialOvo, initialOvo) || other.initialOvo == initialOvo)&&(identical(other.initialBri, initialBri) || other.initialBri == initialBri)&&(identical(other.initialRekpon, initialRekpon) || other.initialRekpon == initialRekpon)&&(identical(other.finalGopay, finalGopay) || other.finalGopay == finalGopay)&&(identical(other.finalBca, finalBca) || other.finalBca == finalBca)&&(identical(other.finalCash, finalCash) || other.finalCash == finalCash)&&(identical(other.finalOvo, finalOvo) || other.finalOvo == finalOvo)&&(identical(other.finalBri, finalBri) || other.finalBri == finalBri)&&(identical(other.finalRekpon, finalRekpon) || other.finalRekpon == finalRekpon)&&(identical(other.initialCapital, initialCapital) || other.initialCapital == initialCapital)&&(identical(other.finalResult, finalResult) || other.finalResult == finalResult)&&(identical(other.mileage, mileage) || other.mileage == mileage)&&(identical(other.netIncome, netIncome) || other.netIncome == netIncome));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,date,initialMileage,finalMileage,initialGopay,initialBca,initialCash,initialOvo,initialBri,initialRekpon,finalGopay,finalBca,finalCash,finalOvo,finalBri,finalRekpon,initialCapital,finalResult,mileage,netIncome]);

@override
String toString() {
  return 'Income(id: $id, date: $date, initialMileage: $initialMileage, finalMileage: $finalMileage, initialGopay: $initialGopay, initialBca: $initialBca, initialCash: $initialCash, initialOvo: $initialOvo, initialBri: $initialBri, initialRekpon: $initialRekpon, finalGopay: $finalGopay, finalBca: $finalBca, finalCash: $finalCash, finalOvo: $finalOvo, finalBri: $finalBri, finalRekpon: $finalRekpon, initialCapital: $initialCapital, finalResult: $finalResult, mileage: $mileage, netIncome: $netIncome)';
}


}

/// @nodoc
abstract mixin class $IncomeCopyWith<$Res>  {
  factory $IncomeCopyWith(Income value, $Res Function(Income) _then) = _$IncomeCopyWithImpl;
@useResult
$Res call({
 int? id, DateTime date, int initialMileage, int finalMileage, double initialGopay, double initialBca, double initialCash, double initialOvo, double initialBri, double initialRekpon, double finalGopay, double finalBca, double finalCash, double finalOvo, double finalBri, double finalRekpon, double? initialCapital, double? finalResult, int? mileage, double? netIncome
});




}
/// @nodoc
class _$IncomeCopyWithImpl<$Res>
    implements $IncomeCopyWith<$Res> {
  _$IncomeCopyWithImpl(this._self, this._then);

  final Income _self;
  final $Res Function(Income) _then;

/// Create a copy of Income
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? date = null,Object? initialMileage = null,Object? finalMileage = null,Object? initialGopay = null,Object? initialBca = null,Object? initialCash = null,Object? initialOvo = null,Object? initialBri = null,Object? initialRekpon = null,Object? finalGopay = null,Object? finalBca = null,Object? finalCash = null,Object? finalOvo = null,Object? finalBri = null,Object? finalRekpon = null,Object? initialCapital = freezed,Object? finalResult = freezed,Object? mileage = freezed,Object? netIncome = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,date: null == date ? _self.date : date // ignore: cast_nullable_to_non_nullable
as DateTime,initialMileage: null == initialMileage ? _self.initialMileage : initialMileage // ignore: cast_nullable_to_non_nullable
as int,finalMileage: null == finalMileage ? _self.finalMileage : finalMileage // ignore: cast_nullable_to_non_nullable
as int,initialGopay: null == initialGopay ? _self.initialGopay : initialGopay // ignore: cast_nullable_to_non_nullable
as double,initialBca: null == initialBca ? _self.initialBca : initialBca // ignore: cast_nullable_to_non_nullable
as double,initialCash: null == initialCash ? _self.initialCash : initialCash // ignore: cast_nullable_to_non_nullable
as double,initialOvo: null == initialOvo ? _self.initialOvo : initialOvo // ignore: cast_nullable_to_non_nullable
as double,initialBri: null == initialBri ? _self.initialBri : initialBri // ignore: cast_nullable_to_non_nullable
as double,initialRekpon: null == initialRekpon ? _self.initialRekpon : initialRekpon // ignore: cast_nullable_to_non_nullable
as double,finalGopay: null == finalGopay ? _self.finalGopay : finalGopay // ignore: cast_nullable_to_non_nullable
as double,finalBca: null == finalBca ? _self.finalBca : finalBca // ignore: cast_nullable_to_non_nullable
as double,finalCash: null == finalCash ? _self.finalCash : finalCash // ignore: cast_nullable_to_non_nullable
as double,finalOvo: null == finalOvo ? _self.finalOvo : finalOvo // ignore: cast_nullable_to_non_nullable
as double,finalBri: null == finalBri ? _self.finalBri : finalBri // ignore: cast_nullable_to_non_nullable
as double,finalRekpon: null == finalRekpon ? _self.finalRekpon : finalRekpon // ignore: cast_nullable_to_non_nullable
as double,initialCapital: freezed == initialCapital ? _self.initialCapital : initialCapital // ignore: cast_nullable_to_non_nullable
as double?,finalResult: freezed == finalResult ? _self.finalResult : finalResult // ignore: cast_nullable_to_non_nullable
as double?,mileage: freezed == mileage ? _self.mileage : mileage // ignore: cast_nullable_to_non_nullable
as int?,netIncome: freezed == netIncome ? _self.netIncome : netIncome // ignore: cast_nullable_to_non_nullable
as double?,
  ));
}

}


/// Adds pattern-matching-related methods to [Income].
extension IncomePatterns on Income {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Income value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Income() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Income value)  $default,){
final _that = this;
switch (_that) {
case _Income():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Income value)?  $default,){
final _that = this;
switch (_that) {
case _Income() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int? id,  DateTime date,  int initialMileage,  int finalMileage,  double initialGopay,  double initialBca,  double initialCash,  double initialOvo,  double initialBri,  double initialRekpon,  double finalGopay,  double finalBca,  double finalCash,  double finalOvo,  double finalBri,  double finalRekpon,  double? initialCapital,  double? finalResult,  int? mileage,  double? netIncome)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Income() when $default != null:
return $default(_that.id,_that.date,_that.initialMileage,_that.finalMileage,_that.initialGopay,_that.initialBca,_that.initialCash,_that.initialOvo,_that.initialBri,_that.initialRekpon,_that.finalGopay,_that.finalBca,_that.finalCash,_that.finalOvo,_that.finalBri,_that.finalRekpon,_that.initialCapital,_that.finalResult,_that.mileage,_that.netIncome);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int? id,  DateTime date,  int initialMileage,  int finalMileage,  double initialGopay,  double initialBca,  double initialCash,  double initialOvo,  double initialBri,  double initialRekpon,  double finalGopay,  double finalBca,  double finalCash,  double finalOvo,  double finalBri,  double finalRekpon,  double? initialCapital,  double? finalResult,  int? mileage,  double? netIncome)  $default,) {final _that = this;
switch (_that) {
case _Income():
return $default(_that.id,_that.date,_that.initialMileage,_that.finalMileage,_that.initialGopay,_that.initialBca,_that.initialCash,_that.initialOvo,_that.initialBri,_that.initialRekpon,_that.finalGopay,_that.finalBca,_that.finalCash,_that.finalOvo,_that.finalBri,_that.finalRekpon,_that.initialCapital,_that.finalResult,_that.mileage,_that.netIncome);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int? id,  DateTime date,  int initialMileage,  int finalMileage,  double initialGopay,  double initialBca,  double initialCash,  double initialOvo,  double initialBri,  double initialRekpon,  double finalGopay,  double finalBca,  double finalCash,  double finalOvo,  double finalBri,  double finalRekpon,  double? initialCapital,  double? finalResult,  int? mileage,  double? netIncome)?  $default,) {final _that = this;
switch (_that) {
case _Income() when $default != null:
return $default(_that.id,_that.date,_that.initialMileage,_that.finalMileage,_that.initialGopay,_that.initialBca,_that.initialCash,_that.initialOvo,_that.initialBri,_that.initialRekpon,_that.finalGopay,_that.finalBca,_that.finalCash,_that.finalOvo,_that.finalBri,_that.finalRekpon,_that.initialCapital,_that.finalResult,_that.mileage,_that.netIncome);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Income extends Income {
  const _Income({this.id, required this.date, required this.initialMileage, required this.finalMileage, required this.initialGopay, required this.initialBca, required this.initialCash, required this.initialOvo, required this.initialBri, required this.initialRekpon, required this.finalGopay, required this.finalBca, required this.finalCash, required this.finalOvo, required this.finalBri, required this.finalRekpon, this.initialCapital, this.finalResult, this.mileage, this.netIncome}): super._();
  factory _Income.fromJson(Map<String, dynamic> json) => _$IncomeFromJson(json);

@override final  int? id;
@override final  DateTime date;
@override final  int initialMileage;
@override final  int finalMileage;
@override final  double initialGopay;
@override final  double initialBca;
@override final  double initialCash;
@override final  double initialOvo;
@override final  double initialBri;
@override final  double initialRekpon;
@override final  double finalGopay;
@override final  double finalBca;
@override final  double finalCash;
@override final  double finalOvo;
@override final  double finalBri;
@override final  double finalRekpon;
@override final  double? initialCapital;
@override final  double? finalResult;
@override final  int? mileage;
@override final  double? netIncome;

/// Create a copy of Income
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$IncomeCopyWith<_Income> get copyWith => __$IncomeCopyWithImpl<_Income>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$IncomeToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Income&&(identical(other.id, id) || other.id == id)&&(identical(other.date, date) || other.date == date)&&(identical(other.initialMileage, initialMileage) || other.initialMileage == initialMileage)&&(identical(other.finalMileage, finalMileage) || other.finalMileage == finalMileage)&&(identical(other.initialGopay, initialGopay) || other.initialGopay == initialGopay)&&(identical(other.initialBca, initialBca) || other.initialBca == initialBca)&&(identical(other.initialCash, initialCash) || other.initialCash == initialCash)&&(identical(other.initialOvo, initialOvo) || other.initialOvo == initialOvo)&&(identical(other.initialBri, initialBri) || other.initialBri == initialBri)&&(identical(other.initialRekpon, initialRekpon) || other.initialRekpon == initialRekpon)&&(identical(other.finalGopay, finalGopay) || other.finalGopay == finalGopay)&&(identical(other.finalBca, finalBca) || other.finalBca == finalBca)&&(identical(other.finalCash, finalCash) || other.finalCash == finalCash)&&(identical(other.finalOvo, finalOvo) || other.finalOvo == finalOvo)&&(identical(other.finalBri, finalBri) || other.finalBri == finalBri)&&(identical(other.finalRekpon, finalRekpon) || other.finalRekpon == finalRekpon)&&(identical(other.initialCapital, initialCapital) || other.initialCapital == initialCapital)&&(identical(other.finalResult, finalResult) || other.finalResult == finalResult)&&(identical(other.mileage, mileage) || other.mileage == mileage)&&(identical(other.netIncome, netIncome) || other.netIncome == netIncome));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,date,initialMileage,finalMileage,initialGopay,initialBca,initialCash,initialOvo,initialBri,initialRekpon,finalGopay,finalBca,finalCash,finalOvo,finalBri,finalRekpon,initialCapital,finalResult,mileage,netIncome]);

@override
String toString() {
  return 'Income(id: $id, date: $date, initialMileage: $initialMileage, finalMileage: $finalMileage, initialGopay: $initialGopay, initialBca: $initialBca, initialCash: $initialCash, initialOvo: $initialOvo, initialBri: $initialBri, initialRekpon: $initialRekpon, finalGopay: $finalGopay, finalBca: $finalBca, finalCash: $finalCash, finalOvo: $finalOvo, finalBri: $finalBri, finalRekpon: $finalRekpon, initialCapital: $initialCapital, finalResult: $finalResult, mileage: $mileage, netIncome: $netIncome)';
}


}

/// @nodoc
abstract mixin class _$IncomeCopyWith<$Res> implements $IncomeCopyWith<$Res> {
  factory _$IncomeCopyWith(_Income value, $Res Function(_Income) _then) = __$IncomeCopyWithImpl;
@override @useResult
$Res call({
 int? id, DateTime date, int initialMileage, int finalMileage, double initialGopay, double initialBca, double initialCash, double initialOvo, double initialBri, double initialRekpon, double finalGopay, double finalBca, double finalCash, double finalOvo, double finalBri, double finalRekpon, double? initialCapital, double? finalResult, int? mileage, double? netIncome
});




}
/// @nodoc
class __$IncomeCopyWithImpl<$Res>
    implements _$IncomeCopyWith<$Res> {
  __$IncomeCopyWithImpl(this._self, this._then);

  final _Income _self;
  final $Res Function(_Income) _then;

/// Create a copy of Income
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? date = null,Object? initialMileage = null,Object? finalMileage = null,Object? initialGopay = null,Object? initialBca = null,Object? initialCash = null,Object? initialOvo = null,Object? initialBri = null,Object? initialRekpon = null,Object? finalGopay = null,Object? finalBca = null,Object? finalCash = null,Object? finalOvo = null,Object? finalBri = null,Object? finalRekpon = null,Object? initialCapital = freezed,Object? finalResult = freezed,Object? mileage = freezed,Object? netIncome = freezed,}) {
  return _then(_Income(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,date: null == date ? _self.date : date // ignore: cast_nullable_to_non_nullable
as DateTime,initialMileage: null == initialMileage ? _self.initialMileage : initialMileage // ignore: cast_nullable_to_non_nullable
as int,finalMileage: null == finalMileage ? _self.finalMileage : finalMileage // ignore: cast_nullable_to_non_nullable
as int,initialGopay: null == initialGopay ? _self.initialGopay : initialGopay // ignore: cast_nullable_to_non_nullable
as double,initialBca: null == initialBca ? _self.initialBca : initialBca // ignore: cast_nullable_to_non_nullable
as double,initialCash: null == initialCash ? _self.initialCash : initialCash // ignore: cast_nullable_to_non_nullable
as double,initialOvo: null == initialOvo ? _self.initialOvo : initialOvo // ignore: cast_nullable_to_non_nullable
as double,initialBri: null == initialBri ? _self.initialBri : initialBri // ignore: cast_nullable_to_non_nullable
as double,initialRekpon: null == initialRekpon ? _self.initialRekpon : initialRekpon // ignore: cast_nullable_to_non_nullable
as double,finalGopay: null == finalGopay ? _self.finalGopay : finalGopay // ignore: cast_nullable_to_non_nullable
as double,finalBca: null == finalBca ? _self.finalBca : finalBca // ignore: cast_nullable_to_non_nullable
as double,finalCash: null == finalCash ? _self.finalCash : finalCash // ignore: cast_nullable_to_non_nullable
as double,finalOvo: null == finalOvo ? _self.finalOvo : finalOvo // ignore: cast_nullable_to_non_nullable
as double,finalBri: null == finalBri ? _self.finalBri : finalBri // ignore: cast_nullable_to_non_nullable
as double,finalRekpon: null == finalRekpon ? _self.finalRekpon : finalRekpon // ignore: cast_nullable_to_non_nullable
as double,initialCapital: freezed == initialCapital ? _self.initialCapital : initialCapital // ignore: cast_nullable_to_non_nullable
as double?,finalResult: freezed == finalResult ? _self.finalResult : finalResult // ignore: cast_nullable_to_non_nullable
as double?,mileage: freezed == mileage ? _self.mileage : mileage // ignore: cast_nullable_to_non_nullable
as int?,netIncome: freezed == netIncome ? _self.netIncome : netIncome // ignore: cast_nullable_to_non_nullable
as double?,
  ));
}


}

// dart format on

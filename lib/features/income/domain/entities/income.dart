import 'package:freezed_annotation/freezed_annotation.dart';

part 'income.freezed.dart';
part 'income.g.dart';

@freezed
sealed class Income with _$Income {
  const Income._();

  const factory Income({
    int? id,
    required DateTime date,
    required int initialMileage,
    required int finalMileage,
    required double initialGopay,
    required double initialBca,
    required double initialCash,
    required double initialOvo,
    required double initialBri,
    required double initialRekpon,
    required double finalGopay,
    required double finalBca,
    required double finalCash,
    required double finalOvo,
    required double finalBri,
    required double finalRekpon,
    double? initialCapital,
    double? finalResult,
    int? mileage,
    double? netIncome,
  }) = _Income;

  factory Income.fromJson(Map<String, dynamic> json) => _$IncomeFromJson(json);
}

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'income.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Income _$IncomeFromJson(Map<String, dynamic> json) => _Income(
  id: (json['id'] as num?)?.toInt(),
  date: DateTime.parse(json['date'] as String),
  initialMileage: (json['initialMileage'] as num).toInt(),
  finalMileage: (json['finalMileage'] as num).toInt(),
  initialGopay: (json['initialGopay'] as num).toDouble(),
  initialBca: (json['initialBca'] as num).toDouble(),
  initialCash: (json['initialCash'] as num).toDouble(),
  initialOvo: (json['initialOvo'] as num).toDouble(),
  initialBri: (json['initialBri'] as num).toDouble(),
  initialRekpon: (json['initialRekpon'] as num).toDouble(),
  finalGopay: (json['finalGopay'] as num).toDouble(),
  finalBca: (json['finalBca'] as num).toDouble(),
  finalCash: (json['finalCash'] as num).toDouble(),
  finalOvo: (json['finalOvo'] as num).toDouble(),
  finalBri: (json['finalBri'] as num).toDouble(),
  finalRekpon: (json['finalRekpon'] as num).toDouble(),
  initialCapital: (json['initialCapital'] as num?)?.toDouble(),
  finalResult: (json['finalResult'] as num?)?.toDouble(),
  mileage: (json['mileage'] as num?)?.toInt(),
  netIncome: (json['netIncome'] as num?)?.toDouble(),
);

Map<String, dynamic> _$IncomeToJson(_Income instance) => <String, dynamic>{
  'id': instance.id,
  'date': instance.date.toIso8601String(),
  'initialMileage': instance.initialMileage,
  'finalMileage': instance.finalMileage,
  'initialGopay': instance.initialGopay,
  'initialBca': instance.initialBca,
  'initialCash': instance.initialCash,
  'initialOvo': instance.initialOvo,
  'initialBri': instance.initialBri,
  'initialRekpon': instance.initialRekpon,
  'finalGopay': instance.finalGopay,
  'finalBca': instance.finalBca,
  'finalCash': instance.finalCash,
  'finalOvo': instance.finalOvo,
  'finalBri': instance.finalBri,
  'finalRekpon': instance.finalRekpon,
  'initialCapital': instance.initialCapital,
  'finalResult': instance.finalResult,
  'mileage': instance.mileage,
  'netIncome': instance.netIncome,
};

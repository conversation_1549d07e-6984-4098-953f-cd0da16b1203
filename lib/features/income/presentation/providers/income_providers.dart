import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/providers/app_settings_provider.dart';
import '../../../../core/providers/global_date_range_provider.dart';
import '../../../../core/services/calculation/providers/calculation_providers.dart';
import '../../../../core/services/sync_service.dart';
import '../../../../core/utils/date_helper.dart';
import '../../data/repositories/income_repository_impl.dart' as impl;
import '../../domain/entities/income.dart' as domain;
import '../../domain/repositories/income_repository.dart';
import '../../domain/use_cases/get_income_history.dart';

part 'income_providers.g.dart';

// Setup provider for the repository
@riverpod
IncomeRepository incomeRepository(Ref ref) {
  final database = ref.watch(databaseProvider);
  final calculationService = ref.watch(incomeCalculationServiceProvider);
  final syncService = ref.watch(syncServiceProvider);

  // Cast to the domain IncomeRepository interface
  return impl.IncomeRepositoryImpl(
        database: database,
        calculationService: calculationService,
        syncService: syncService,
      )
      as IncomeRepository;
}

// Provider for the income use cases
@riverpod
GetIncomeHistory getIncomeHistory(Ref ref) {
  final repository = ref.watch(incomeRepositoryProvider);
  return GetIncomeHistory(repository);
}

// Provider for the income list
@riverpod
class IncomeList extends _$IncomeList {
  @override
  Future<List<domain.Income>> build() async {
    return _fetchIncomeList();
  }

  Future<List<domain.Income>> _fetchIncomeList() async {
    final getIncomeHistory = ref.watch(getIncomeHistoryProvider);
    final result = await getIncomeHistory.getAll();

    return result.fold((failure) {
      // Log the error
      _handleFailure(failure);
      return [];
    }, (incomeList) => incomeList);
  }

  void _handleFailure(Failure failure) {
    // Log the error or show a notification
    debugPrint('Error fetching income: ${failure.toString()}');
  }

  Future<void> refreshIncomeList() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => _fetchIncomeList());
  }

  Future<bool> addIncome(domain.Income income) async {
    final repository = ref.read(incomeRepositoryProvider);
    final result = await repository.save(income);

    return result.fold(
      (failure) {
        _handleFailure(failure);
        return false;
      },
      (_) async {
        await refreshIncomeList();
        return true;
      },
    );
  }

  Future<bool> updateIncome(domain.Income income) async {
    final repository = ref.read(incomeRepositoryProvider);
    final result = await repository.update(income);

    return result.fold(
      (failure) {
        _handleFailure(failure);
        return false;
      },
      (_) async {
        await refreshIncomeList();
        return true;
      },
    );
  }

  Future<bool> deleteIncome(int id) async {
    final repository = ref.read(incomeRepositoryProvider);
    final result = await repository.delete(id);

    return result.fold(
      (failure) {
        _handleFailure(failure);
        return false;
      },
      (_) async {
        await refreshIncomeList();
        return true;
      },
    );
  }
}

// Provider for date range selection - now using global date range
@Deprecated('Use globalDateRangeProvider instead')
@riverpod
class DateRange extends _$DateRange {
  @override
  AsyncValue<DateTimeRange> build() {
    // Forward to global date range provider
    return ref.watch(globalDateRangeProvider);
  }

  Future<void> setDateRange(DateTimeRange range) async {
    // Forward to global date range provider
    await ref.read(globalDateRangeProvider.notifier).setDateRange(range);
  }
}

// Provider for paginated income list
@riverpod
class PaginatedIncomeList extends _$PaginatedIncomeList {
  static const int defaultPageSize = 20;

  @override
  Future<PaginatedIncomeResult> build() async {
    return _fetchPage(1);
  }

  Future<PaginatedIncomeResult> _fetchPage(int page) async {
    final dateRangeAsync = ref.watch(globalDateRangeProvider);
    final getIncomeHistory = ref.watch(getIncomeHistoryProvider);

    // Get date range if available
    DateTime? start;
    DateTime? end;

    if (dateRangeAsync.hasValue) {
      final dateRange = dateRangeAsync.value!;
      start = dateRange.start;
      end = dateRange.end
          .add(const Duration(days: 1))
          .subtract(const Duration(microseconds: 1));
    }

    // Fetch paginated data
    final result = await getIncomeHistory.getPaginated(
      page: page,
      pageSize: defaultPageSize,
      start: start,
      end: end,
    );

    return result.fold(
      (failure) {
        // Log the error
        debugPrint('Error fetching paginated income: ${failure.toString()}');
        return PaginatedIncomeResult(
          incomeList: [],
          currentPage: page,
          hasMorePages: false,
        );
      },
      (incomeList) {
        // If we got fewer items than the page size, we've reached the end
        final hasMorePages = incomeList.length >= defaultPageSize;

        return PaginatedIncomeResult(
          incomeList: incomeList,
          currentPage: page,
          hasMorePages: hasMorePages,
        );
      },
    );
  }

  Future<void> loadNextPage() async {
    // Don't do anything if we're already loading or there are no more pages
    if (state.isLoading || (state.hasValue && !state.value!.hasMorePages)) {
      return;
    }

    // Get the current page from state
    final currentPage = state.valueOrNull?.currentPage ?? 0;
    final nextPage = currentPage + 1;

    // Set loading state
    state = const AsyncValue.loading();

    // Load the next page
    state = await AsyncValue.guard(() => _fetchPage(nextPage));
  }

  Future<void> refresh() async {
    // Reset to page 1
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => _fetchPage(1));
  }
}

// Class to hold paginated income result
class PaginatedIncomeResult {
  final List<domain.Income> incomeList;
  final int currentPage;
  final bool hasMorePages;

  PaginatedIncomeResult({
    required this.incomeList,
    required this.currentPage,
    required this.hasMorePages,
  });
}

// Provider for filtered income list
@riverpod
Future<List<domain.Income>> filteredIncomeList(Ref ref) async {
  final dateRangeAsync = ref.watch(globalDateRangeProvider);
  final allIncomeAsync = await ref.watch(incomeListProvider.future);

  // No filtering needed if list is empty
  if (allIncomeAsync.isEmpty) {
    return [];
  }

  // Wait for date range to be available
  if (!dateRangeAsync.hasValue) {
    // Sort by date (newest first) even when no date range is selected
    final sortedList = List<domain.Income>.from(allIncomeAsync)
      ..sort((a, b) => b.date.compareTo(a.date));
    return sortedList;
  }

  final dateRange = dateRangeAsync.value!;

  // Ensure date range is in UTC for consistent comparison
  final startUtc = DateHelper.ensureUtc(dateRange.start);
  final endUtc = DateHelper.ensureUtc(
    dateRange.end.add(const Duration(days: 1)),
  );

  // Filter by date range - make sure to only include non-null income
  final filteredList = allIncomeAsync.where((income) {
    // Ensure income date is in UTC for consistent comparison
    final dateUtc = DateHelper.ensureUtc(income.date);

    return dateUtc.isAtSameMomentAs(startUtc) ||
        dateUtc.isAtSameMomentAs(endUtc) ||
        (dateUtc.isAfter(startUtc) && dateUtc.isBefore(endUtc));
  }).toList();

  // Sort by date (newest first)
  filteredList.sort((a, b) => b.date.compareTo(a.date));

  return filteredList;
}

// Provider for income totals
@riverpod
IncomeSummary incomeSummary(Ref ref) {
  // Listen to filtered income records
  final filteredIncomeAsync = ref.watch(filteredIncomeListProvider);

  return filteredIncomeAsync.when(
    data: (incomeRecords) {
      if (incomeRecords.isEmpty) {
        return IncomeSummary.empty();
      }

      double totalNetIncome = 0;
      int totalMileage = 0;
      double mileageRate = 0;
      double highestIncome = 0;

      // Filter out incomplete entries (where final values haven't been entered)
      final completeIncomeRecords = incomeRecords
          .where(_isIncomeEntryComplete)
          .toList();
      final int completeRecordCount = completeIncomeRecords.length;

      for (final income in completeIncomeRecords) {
        if (income.netIncome != null) {
          totalNetIncome += income.netIncome!;

          // Track highest income
          if (income.netIncome! > highestIncome) {
            highestIncome = income.netIncome!;
          }
        }

        if (income.mileage != null) {
          totalMileage += income.mileage!;
        }
      }

      if (totalMileage > 0) {
        mileageRate = totalNetIncome / totalMileage;
      }

      return IncomeSummary(
        totalNetIncome: totalNetIncome,
        totalMileage: totalMileage,
        mileageRate: mileageRate,
        recordCount: completeRecordCount,
        totalRecordCount: incomeRecords.length,
        highestIncome: highestIncome,
      );
    },
    loading: () => IncomeSummary.empty(),
    error: (_, _) => IncomeSummary.empty(),
  );
}

/// Determines if an income entry is complete (has both initial and final values entered)
bool _isIncomeEntryComplete(domain.Income income) {
  // An entry is considered complete if it has both initial and final values
  // and the final values have been properly entered (not just default zeros)

  // Check if finalResult is calculated - this indicates the entry has been fully processed
  if (income.finalResult == null || income.netIncome == null) {
    return false;
  }

  // Check if any of the final values are missing or zero
  // We're checking if at least one final value is non-zero to determine if the user has entered final values
  final bool hasFinalValues =
      income.finalGopay > 0 ||
      income.finalBca > 0 ||
      income.finalCash > 0 ||
      income.finalOvo > 0 ||
      income.finalBri > 0 ||
      income.finalRekpon > 0;

  // Check if final mileage is greater than or equal to initial mileage
  // This is another indicator that the entry is complete
  final bool hasFinalMileage = income.finalMileage >= income.initialMileage;

  return hasFinalValues && hasFinalMileage;
}

// Simple class to hold income summary data
class IncomeSummary {
  final double totalNetIncome;
  final int totalMileage;
  final double mileageRate;
  final int recordCount; // Number of complete records
  final int totalRecordCount; // Total number of records (complete + incomplete)
  final double highestIncome;

  IncomeSummary({
    required this.totalNetIncome,
    required this.totalMileage,
    required this.mileageRate,
    required this.recordCount,
    required this.highestIncome,
    this.totalRecordCount = 0,
  });

  factory IncomeSummary.empty() {
    return IncomeSummary(
      totalNetIncome: 0,
      totalMileage: 0,
      mileageRate: 0,
      recordCount: 0,
      totalRecordCount: 0,
      highestIncome: 0,
    );
  }
}

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'income_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$incomeRepositoryHash() => r'cfc7dcd17faadef2bad467cb658d905e55226db1';

/// See also [incomeRepository].
@ProviderFor(incomeRepository)
final incomeRepositoryProvider = AutoDisposeProvider<IncomeRepository>.internal(
  incomeRepository,
  name: r'incomeRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$incomeRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IncomeRepositoryRef = AutoDisposeProviderRef<IncomeRepository>;
String _$getIncomeHistoryHash() => r'30d34a018adc899337e57f9cc4954cba44756675';

/// See also [getIncomeHistory].
@ProviderFor(getIncomeHistory)
final getIncomeHistoryProvider = AutoDisposeProvider<GetIncomeHistory>.internal(
  getIncomeHistory,
  name: r'getIncomeHistoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$getIncomeHistoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef GetIncomeHistoryRef = AutoDisposeProviderRef<GetIncomeHistory>;
String _$filteredIncomeListHash() =>
    r'e5034cf2c3075f03bb92112e8bbabbe5a2bc30b9';

/// See also [filteredIncomeList].
@ProviderFor(filteredIncomeList)
final filteredIncomeListProvider =
    AutoDisposeFutureProvider<List<domain.Income>>.internal(
      filteredIncomeList,
      name: r'filteredIncomeListProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$filteredIncomeListHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FilteredIncomeListRef =
    AutoDisposeFutureProviderRef<List<domain.Income>>;
String _$incomeSummaryHash() => r'67a7e2772c68460ab5eac74797504f7ab0e4cebd';

/// See also [incomeSummary].
@ProviderFor(incomeSummary)
final incomeSummaryProvider = AutoDisposeProvider<IncomeSummary>.internal(
  incomeSummary,
  name: r'incomeSummaryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$incomeSummaryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IncomeSummaryRef = AutoDisposeProviderRef<IncomeSummary>;
String _$incomeListHash() => r'75a78815135d7ee9d5b53b7c1f190b39ed68a889';

/// See also [IncomeList].
@ProviderFor(IncomeList)
final incomeListProvider =
    AutoDisposeAsyncNotifierProvider<IncomeList, List<domain.Income>>.internal(
      IncomeList.new,
      name: r'incomeListProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$incomeListHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$IncomeList = AutoDisposeAsyncNotifier<List<domain.Income>>;
String _$dateRangeHash() => r'b2cb599a5c9588849e910a32baab44085e39cb07';

/// See also [DateRange].
@ProviderFor(DateRange)
final dateRangeProvider =
    AutoDisposeNotifierProvider<DateRange, AsyncValue<DateTimeRange>>.internal(
      DateRange.new,
      name: r'dateRangeProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$dateRangeHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$DateRange = AutoDisposeNotifier<AsyncValue<DateTimeRange>>;
String _$paginatedIncomeListHash() =>
    r'df164fc2c7caa29f1750c64d2ffc63286df9bade';

/// See also [PaginatedIncomeList].
@ProviderFor(PaginatedIncomeList)
final paginatedIncomeListProvider =
    AutoDisposeAsyncNotifierProvider<
      PaginatedIncomeList,
      PaginatedIncomeResult
    >.internal(
      PaginatedIncomeList.new,
      name: r'paginatedIncomeListProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$paginatedIncomeListHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$PaginatedIncomeList = AutoDisposeAsyncNotifier<PaginatedIncomeResult>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package

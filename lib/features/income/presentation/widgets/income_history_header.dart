import 'package:flutter/material.dart';

class IncomeHistoryHeader extends StatefulWidget {
  const IncomeHistoryHeader({super.key});

  @override
  State<IncomeHistoryHeader> createState() => _IncomeHistoryHeaderState();
}

class _IncomeHistoryHeaderState extends State<IncomeHistoryHeader> {
  bool _showHeader = false;

  @override
  void initState() {
    super.initState();
    // Add a delay before showing the header
    // This ensures the summary sections are fully rendered first
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        setState(() {
          _showHeader = true;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedOpacity(
      opacity: _showHeader ? 1.0 : 0.0,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        transform: Matrix4.translationValues(
          0.0,
          _showHeader ? 0.0 : 20.0,
          0.0,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Income History',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: Text(
                'Your past income records',
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

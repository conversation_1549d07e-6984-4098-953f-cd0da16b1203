import 'package:flutter/material.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/calculations.dart';
import '../../../../core/utils/date_helper.dart';
import '../../domain/entities/income.dart';

/// A bottom sheet that displays detailed information about an income record
class IncomeDetailsSheet extends StatelessWidget {
  final Income income;

  const IncomeDetailsSheet({super.key, required this.income});

  @override
  Widget build(BuildContext context) {
    // Determine color based on income value
    final netIncome = income.netIncome ?? 0;
    Color incomeColor = AppColors.success;
    if (netIncome < 0) {
      incomeColor = AppColors.error;
    } else if (netIncome == 0) {
      incomeColor = Colors.grey;
    }

    return DraggableScrollableSheet(
      initialChildSize: 0.65,
      minChildSize: 0.3,
      maxChildSize: 0.95,
      expand: false,
      builder: (context, scrollController) {
        return Container(
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
          ),
          child: Column(
            children: [
              // Drag handle
              Container(
                margin: const EdgeInsets.only(top: 8, bottom: 4),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey.withAlpha(80),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  physics: const BouncingScrollPhysics(),
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(16.0, 8.0, 16.0, 24.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header with title
                        _buildHeader(context),

                        // Date and summary
                        _buildSummaryCard(context, incomeColor),
                        const SizedBox(height: 16),

                        // Mileage Section
                        _buildMileageSection(context),
                        const SizedBox(height: 16),

                        // Initial Balance Section
                        _buildInitialBalanceSection(context),
                        const SizedBox(height: 16),

                        // Final Balance Section
                        _buildFinalBalanceSection(context),
                        const SizedBox(height: 16),

                        // Result Section
                        _buildResultSection(context, incomeColor),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Builds the header with title and close button
  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              const Icon(
                Icons.account_balance_wallet,
                color: AppColors.success,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Income Details',
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          IconButton(
            icon: const Icon(Icons.close, size: 20),
            onPressed: () => Navigator.pop(context),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
          ),
        ],
      ),
    );
  }

  /// Builds the summary card with date and key metrics
  Widget _buildSummaryCard(BuildContext context, Color incomeColor) {
    return Card(
      elevation: 0,
      color: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: incomeColor.withAlpha(30), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.calendar_today,
                  color: AppColors.textSecondary,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  DateHelper.formatForDisplay(income.date),
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    context,
                    Calculations.formatCurrency(income.netIncome ?? 0),
                    'Net Income',
                    Icons.account_balance_wallet,
                    incomeColor,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    context,
                    '${income.mileage} km',
                    'Distance',
                    Icons.speed,
                    AppColors.primary,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the mileage section
  Widget _buildMileageSection(BuildContext context) {
    return _buildDetailSection(
      context,
      'Mileage',
      Icons.speed,
      AppColors.primary,
      [
        _buildDetailItem(
          context,
          'Initial Mileage',
          '${income.initialMileage} km',
          Icons.play_circle_outline,
        ),
        _buildDetailItem(
          context,
          'Final Mileage',
          '${income.finalMileage} km',
          Icons.stop_circle_outlined,
        ),
        const Divider(),
        _buildDetailItem(
          context,
          'Distance',
          '${income.mileage} km',
          Icons.straighten,
          isTotal: true,
          valueColor: AppColors.primary,
        ),
      ],
    );
  }

  /// Builds the initial balance section
  Widget _buildInitialBalanceSection(BuildContext context) {
    return _buildDetailSection(
      context,
      'Initial Balance',
      Icons.account_balance_wallet,
      Colors.blue,
      [
        _buildDetailItem(
          context,
          'Gopay',
          Calculations.formatCurrency(income.initialGopay),
          Icons.payment,
        ),
        _buildDetailItem(
          context,
          'BCA',
          Calculations.formatCurrency(income.initialBca),
          Icons.account_balance,
        ),
        _buildDetailItem(
          context,
          'Cash',
          Calculations.formatCurrency(income.initialCash),
          Icons.money,
        ),
        _buildDetailItem(
          context,
          'OVO',
          Calculations.formatCurrency(income.initialOvo),
          Icons.payment,
        ),
        _buildDetailItem(
          context,
          'BRI',
          Calculations.formatCurrency(income.initialBri),
          Icons.account_balance,
        ),
        _buildDetailItem(
          context,
          'Rekpon',
          Calculations.formatCurrency(income.initialRekpon),
          Icons.savings,
        ),
        const Divider(),
        _buildDetailItem(
          context,
          'Total Initial',
          Calculations.formatCurrency(income.initialCapital ?? 0),
          Icons.calculate,
          isTotal: true,
          valueColor: Colors.blue,
        ),
      ],
    );
  }

  /// Builds the final balance section
  Widget _buildFinalBalanceSection(BuildContext context) {
    return _buildDetailSection(
      context,
      'Final Balance',
      Icons.account_balance_wallet,
      Colors.purple,
      [
        _buildDetailItem(
          context,
          'Gopay',
          Calculations.formatCurrency(income.finalGopay),
          Icons.payment,
        ),
        _buildDetailItem(
          context,
          'BCA',
          Calculations.formatCurrency(income.finalBca),
          Icons.account_balance,
        ),
        _buildDetailItem(
          context,
          'Cash',
          Calculations.formatCurrency(income.finalCash),
          Icons.money,
        ),
        _buildDetailItem(
          context,
          'OVO',
          Calculations.formatCurrency(income.finalOvo),
          Icons.payment,
        ),
        _buildDetailItem(
          context,
          'BRI',
          Calculations.formatCurrency(income.finalBri),
          Icons.account_balance,
        ),
        _buildDetailItem(
          context,
          'Rekpon',
          Calculations.formatCurrency(income.finalRekpon),
          Icons.savings,
        ),
        const Divider(),
        _buildDetailItem(
          context,
          'Total Final',
          Calculations.formatCurrency(income.finalResult ?? 0),
          Icons.calculate,
          isTotal: true,
          valueColor: Colors.purple,
        ),
      ],
    );
  }

  /// Builds the result section
  Widget _buildResultSection(BuildContext context, Color incomeColor) {
    return _buildDetailSection(
      context,
      'Result',
      Icons.trending_up,
      incomeColor,
      [
        _buildDetailItem(
          context,
          'Net Income',
          Calculations.formatCurrency(income.netIncome ?? 0),
          Icons.account_balance_wallet,
          isTotal: true,
          valueColor: incomeColor,
        ),
      ],
    );
  }

  /// Builds a summary item with icon, value and label
  Widget _buildSummaryItem(
    BuildContext context,
    String value,
    String label,
    IconData icon,
    Color color,
  ) {
    return Row(
      children: [
        Icon(icon, color: color, size: 16),
        const SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              value,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              label,
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
            ),
          ],
        ),
      ],
    );
  }

  /// Builds a detail section with title and children
  Widget _buildDetailSection(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    List<Widget> children,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 18),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: Theme.of(
                    context,
                  ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Padding(
              padding: EdgeInsets.symmetric(vertical: 8),
              child: Divider(height: 1),
            ),
            ...children,
          ],
        ),
      ),
    );
  }

  /// Builds a detail item with label, value and icon
  Widget _buildDetailItem(
    BuildContext context,
    String label,
    String value,
    IconData icon, {
    Color? valueColor,
    bool isTotal = false,
  }) {
    final itemColor =
        valueColor ?? (isTotal ? AppColors.success : Colors.grey[700]);
    final fontWeight = isTotal ? FontWeight.bold : FontWeight.w500;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, color: itemColor, size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: isTotal ? FontWeight.w600 : FontWeight.w500,
                color: isTotal ? itemColor : null,
              ),
            ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: fontWeight,
              color: itemColor,
            ),
          ),
        ],
      ),
    );
  }
}

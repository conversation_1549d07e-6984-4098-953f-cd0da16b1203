import 'package:flutter/material.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/calculations.dart';
import '../../../../core/utils/date_helper.dart';
import '../../domain/entities/income.dart';

/// A card widget that displays an income record item
class IncomeItemCard extends StatelessWidget {
  final Income income;
  final VoidCallback onTap;
  final VoidCallback onLongPress;

  const IncomeItemCard({
    super.key,
    required this.income,
    required this.onTap,
    required this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    // Determine color based on income value
    final netIncome = income.netIncome ?? 0;
    Color incomeColor = AppColors.success;
    if (netIncome < 0) {
      incomeColor = AppColors.error;
    } else if (netIncome == 0) {
      incomeColor = Colors.grey;
    }

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: incomeColor.withAlpha(30), width: 1),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: onTap,
        onLongPress: onLongPress,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with date and mileage
              _buildHeader(context),

              const SizedBox(height: 12),
              const Divider(height: 1),
              const SizedBox(height: 12),

              // Income section
              _buildIncomeSection(context, incomeColor),

              const SizedBox(height: 12),

              // Balance change
              _buildBalanceChangeSection(context, netIncome),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds the header section with date and mileage
  Widget _buildHeader(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            const Icon(
              Icons.calendar_today,
              color: AppColors.textSecondary,
              size: 16,
            ),
            const SizedBox(width: 8),
            Text(
              DateHelper.formatForDisplay(income.date),
              style: Theme.of(
                context,
              ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        Row(
          children: [
            const Icon(Icons.speed, color: AppColors.primary, size: 14),
            const SizedBox(width: 4),
            Text(
              '${income.mileage} km',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w700,
                color: AppColors.primary,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Builds the income section with net income
  Widget _buildIncomeSection(BuildContext context, Color incomeColor) {
    return Row(
      children: [
        Icon(Icons.account_balance_wallet, color: incomeColor, size: 16),
        const SizedBox(width: 8),
        Text(
          'Net Income:',
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
        ),
        const Spacer(),
        Text(
          Calculations.formatCurrency(income.netIncome ?? 0),
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: incomeColor,
          ),
        ),
      ],
    );
  }

  /// Builds the balance change section with progress indicator
  Widget _buildBalanceChangeSection(BuildContext context, double netIncome) {
    // Calculate percentage change relative to initial capital
    double percentageChange = 0.0;

    // Handle the case where initial capital is not null
    if (income.initialCapital != null) {
      if (income.initialCapital! != 0) {
        // Calculate percentage change based on net income and initial capital
        percentageChange =
            (income.netIncome ?? 0) / income.initialCapital!.abs();

        // For negative initial capital with positive net income, we want to show a positive change
        // For negative initial capital with negative net income, we want to show a negative change
        if (income.initialCapital! < 0) {
          // When initial capital is negative, we need to adjust the sign of the percentage
          // to ensure it reflects the actual financial improvement/deterioration
          if (netIncome >= 0) {
            // Positive net income with negative initial capital is an improvement
            percentageChange = percentageChange.abs();
          } else {
            // Negative net income with negative initial capital is a deterioration
            percentageChange = -percentageChange.abs();
          }
        }

        // For standard left-to-right progress indicator, we need to scale the value
        // to fit within 0.0 to 1.0 range while preserving the sign for color determination
        // We'll use a scale where 0.5 (50%) change fills the bar completely
        percentageChange = (percentageChange / 0.5).clamp(-1.0, 1.0);
      } else {
        // If initial capital is zero, base the indicator solely on whether there's profit or loss
        // Use 0.5 (50%) for a moderate fill level
        percentageChange = netIncome > 0 ? 0.5 : (netIncome < 0 ? -0.5 : 0.0);
      }
    }

    // Determine colors based on profit/loss
    const Color positiveColor = AppColors.success;
    const Color negativeColor = AppColors.error;
    final Color backgroundColor = Colors.grey.withAlpha(20);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Balance Change',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              netIncome >= 0 ? 'Profit' : 'Loss',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: netIncome >= 0 ? positiveColor : negativeColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 6),
        // Standard left-to-right progress indicator
        ClipRRect(
          borderRadius: BorderRadius.circular(2),
          child: LinearProgressIndicator(
            value: percentageChange.abs().clamp(0.0, 1.0),
            backgroundColor: backgroundColor,
            valueColor: AlwaysStoppedAnimation<Color>(
              netIncome >= 0 ? positiveColor : negativeColor,
            ),
            minHeight: 4,
          ),
        ),
        const SizedBox(height: 6),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Initial: ${Calculations.formatCurrency(income.initialCapital ?? 0)}',
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
            ),
            Text(
              'Final: ${Calculations.formatCurrency(income.finalResult ?? 0)}',
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
            ),
          ],
        ),
      ],
    );
  }
}

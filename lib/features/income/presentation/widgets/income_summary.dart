import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/calculations.dart';
import '../../../../core/widgets/expandable_card.dart';

class IncomeSummary extends StatefulWidget {
  final double totalIncome;
  final int recordCount;
  final int totalRecordCount;
  final double highestIncome;

  const IncomeSummary({
    super.key,
    required this.totalIncome,
    required this.recordCount,
    this.totalRecordCount = 0,
    required this.highestIncome,
  });

  @override
  State<IncomeSummary> createState() => _IncomeSummaryState();
}

class _IncomeSummaryState extends State<IncomeSummary> {
  bool _isVisible = false;

  @override
  void initState() {
    super.initState();
    // Show immediately as this is the first card
    Future.delayed(const Duration(milliseconds: 50), () {
      if (mounted) {
        setState(() {
          _isVisible = true;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedOpacity(
      opacity: _isVisible ? 1.0 : 0.0,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      child: _buildCard(context),
    );
  }

  Widget _buildCard(BuildContext context) {
    return ExpandableCard(
      title: 'Income Summary',
      icon: Icons.account_balance_wallet,
      iconColor: AppColors.primary,
      borderColor: AppColors.primary,
      collapsedChild: const SizedBox.shrink(), // Empty when collapsed
      expandedChild: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: _buildIncomeItem(
                  context,
                  'Total Income',
                  Calculations.formatCurrency(widget.totalIncome),
                  Icons.payments,
                ),
              ),
              Expanded(
                child: _buildIncomeItem(
                  context,
                  'Average',
                  widget.recordCount > 0
                      ? Calculations.formatCurrency(
                          widget.totalIncome / widget.recordCount,
                        )
                      : Calculations.formatCurrency(0),
                  Icons.trending_up,
                ),
              ),
              Expanded(
                child: _buildIncomeItem(
                  context,
                  'Highest',
                  Calculations.formatCurrency(widget.highestIncome),
                  Icons.emoji_events,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildIncomeItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Icon(icon, color: AppColors.primary, size: 18),
          const SizedBox(height: 8),
          Text(
            value,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          Text(
            label,
            textAlign: TextAlign.center,
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/widgets/expandable_card.dart';

class MileageSummary extends StatefulWidget {
  final int totalMileage;
  final int recordCount;

  const MileageSummary({
    super.key,
    required this.totalMileage,
    required this.recordCount,
  });

  @override
  State<MileageSummary> createState() => _MileageSummaryState();
}

class _MileageSummaryState extends State<MileageSummary> {
  bool _isVisible = false;

  @override
  void initState() {
    super.initState();
    // Add a delay before showing the mileage summary
    // This ensures the income summary is fully rendered first
    Future.delayed(const Duration(milliseconds: 150), () {
      if (mounted) {
        setState(() {
          _isVisible = true;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedOpacity(
      opacity: _isVisible ? 1.0 : 0.0,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        transform: Matrix4.translationValues(0.0, _isVisible ? 0.0 : 20.0, 0.0),
        child: _buildCard(context),
      ),
    );
  }

  Widget _buildCard(BuildContext context) {
    return ExpandableCard(
      title: 'Mileage Summary',
      icon: Icons.speed,
      iconColor: AppColors.success,
      borderColor: AppColors.success,
      collapsedChild: const SizedBox.shrink(), // Empty when collapsed
      expandedChild: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: _buildMileageItem(
                  context,
                  'Total Distance',
                  '${widget.totalMileage} km',
                  Icons.timeline,
                ),
              ),
              Expanded(
                child: _buildMileageItem(
                  context,
                  'Records',
                  '${widget.recordCount} entries',
                  Icons.assignment,
                ),
              ),
              Expanded(
                child: _buildMileageItem(
                  context,
                  'Average',
                  widget.recordCount > 0
                      ? '${(widget.totalMileage / widget.recordCount).round()} km'
                      : '0 km',
                  Icons.bar_chart,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMileageItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Icon(icon, color: AppColors.success, size: 18),
          const SizedBox(height: 8),
          Text(
            value,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.success,
            ),
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          Text(
            label,
            textAlign: TextAlign.center,
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}

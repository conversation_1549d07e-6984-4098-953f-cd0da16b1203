import 'dart:math' as math;

import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/calculations.dart';
import '../../../../core/utils/date_helper.dart';
import '../../../../core/widgets/expandable_card.dart';
import '../../domain/entities/income.dart';

/// Widget that displays income trends in chart form
class IncomeTrendsCard extends StatefulWidget {
  final List<Income> incomeRecords;

  const IncomeTrendsCard({super.key, required this.incomeRecords});

  @override
  State<IncomeTrendsCard> createState() => _IncomeTrendsCardState();
}

class _IncomeTrendsCardState extends State<IncomeTrendsCard> {
  bool _showChart = false;
  bool _groupByWeek = false;

  // Define the starting day of the week (1 = Monday, 7 = Sunday)
  // Using Monday as the default starting day (ISO 8601 standard)
  static const int _startingWeekday = DateTime.monday;

  // Store week labels and keys for display in the chart
  List<String> _weekLabels = [];
  List<String> _orderedWeekKeys = [];

  @override
  void initState() {
    super.initState();
    // Animate the chart after a short delay
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        setState(() {
          _showChart = true;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return ExpandableCard(
      title: 'Income Trends',
      icon: Icons.trending_up,
      iconColor: AppColors.secondary,
      borderColor: AppColors.secondary,
      collapsedChild: const SizedBox.shrink(), // Empty when collapsed
      expandedChild: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Group by toggle
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              const Text('Group by week'),
              const SizedBox(width: 8),
              Switch(
                value: _groupByWeek,
                onChanged: (value) {
                  setState(() {
                    _groupByWeek = value;
                  });
                },
                activeColor: AppColors.primary,
              ),
            ],
          ),

          // Income trends chart
          _buildIncomeChart(context),
        ],
      ),
    );
  }

  Widget _buildIncomeChart(BuildContext context) {
    // Sort income records by date (oldest first)
    final sortedIncome = List<Income>.from(widget.incomeRecords)
      ..sort((a, b) => a.date.compareTo(b.date));

    // Skip if no data
    if (sortedIncome.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Text('No income data available for the selected period.'),
        ),
      );
    }

    // Prepare data for the chart
    final List<FlSpot> incomeSpots = [];
    final List<FlSpot> averageSpots = [];

    if (_groupByWeek) {
      // Group by week with consistent starting day
      final Map<String, List<Income>> weeklyData = {};

      // Find the first date that matches our starting weekday
      DateTime firstWeekStart = sortedIncome.first.date;
      while (firstWeekStart.weekday != _startingWeekday) {
        firstWeekStart = firstWeekStart.subtract(const Duration(days: 1));
      }

      for (final income in sortedIncome) {
        // Calculate which week this income belongs to
        final daysSinceFirstWeekStart = income.date
            .difference(firstWeekStart)
            .inDays;
        final weekNumber = daysSinceFirstWeekStart ~/ 7;

        // Create a key that includes the start and end dates of the week
        final weekStart = firstWeekStart.add(Duration(days: weekNumber * 7));
        final weekEnd = weekStart.add(const Duration(days: 6));
        final weekKey =
            '${DateFormat('dd/MM').format(weekStart)}-${DateFormat('dd/MM').format(weekEnd)}';

        if (!weeklyData.containsKey(weekKey)) {
          weeklyData[weekKey] = [];
        }
        weeklyData[weekKey]!.add(income);
      }

      // Calculate weekly averages
      final List<MapEntry<String, double>> weeklyAverages = [];
      double runningTotal = 0;
      int recordCount = 0;

      // Store week keys and formatted labels in order for consistent display
      _orderedWeekKeys = weeklyData.keys.toList()
        ..sort((a, b) {
          // Extract start dates and compare them
          final aStart = DateFormat('dd/MM').parse(a.split('-')[0]);
          final bStart = DateFormat('dd/MM').parse(b.split('-')[0]);
          return aStart.compareTo(bStart);
        });

      // Create user-friendly week labels (W1: 28/04, W2: 05/05, etc.)
      _weekLabels = [];
      for (int i = 0; i < _orderedWeekKeys.length; i++) {
        final weekKey = _orderedWeekKeys[i];
        final startDate = DateFormat('dd/MM').parse(weekKey.split('-')[0]);
        _weekLabels.add('W${i + 1}: ${DateFormat('dd/MM').format(startDate)}');
      }

      // Process data in chronological order
      for (final weekKey in _orderedWeekKeys) {
        final incomes = weeklyData[weekKey]!;
        final weeklyTotal = incomes.fold<double>(
          0,
          (sum, income) => sum + (income.netIncome ?? 0),
        );
        final weeklyAverage = weeklyTotal / incomes.length;

        weeklyAverages.add(MapEntry(weekKey, weeklyAverage));

        // Add to running average calculation
        runningTotal += weeklyTotal;
        recordCount += incomes.length;
      }

      // Create spots for the chart
      for (int i = 0; i < weeklyAverages.length; i++) {
        final entry = weeklyAverages[i];
        incomeSpots.add(FlSpot(i.toDouble(), entry.value));

        // Calculate running average
        final runningAverage = runningTotal / recordCount;
        averageSpots.add(FlSpot(i.toDouble(), runningAverage));
      }

      // Week labels have already been created
    } else {
      // Daily data
      for (int i = 0; i < sortedIncome.length; i++) {
        final income = sortedIncome[i];
        if (income.netIncome != null) {
          incomeSpots.add(FlSpot(i.toDouble(), income.netIncome!));

          // Calculate running average up to this point
          double sum = 0;
          for (int j = 0; j <= i; j++) {
            sum += sortedIncome[j].netIncome ?? 0;
          }
          final average = sum / (i + 1);
          averageSpots.add(FlSpot(i.toDouble(), average));
        }
      }
    }

    // Find min and max values for Y axis
    double minY = 0;
    double maxY = 0;

    if (incomeSpots.isNotEmpty) {
      minY = incomeSpots.map((spot) => spot.y).reduce((a, b) => a < b ? a : b);
      maxY = incomeSpots.map((spot) => spot.y).reduce((a, b) => a > b ? a : b);

      // Add some padding
      minY = minY < 0 ? minY * 1.1 : 0; // Start at 0 if all values are positive
      maxY =
          maxY * 1.2; // Increase padding to 20% for better spacing at the top

      // Round maxY to a nice number for better tick marks
      final magnitude = maxY.abs() > 0
          ? (math.log(maxY.abs()) / math.ln10).floor()
          : 0;
      final power = math.pow(10, magnitude).toDouble();
      maxY = (maxY / power).ceil() * power;

      // Ensure minY is also rounded to a nice number if negative
      if (minY < 0) {
        final minMagnitude = minY.abs() > 0
            ? (math.log(minY.abs()) / math.ln10).floor()
            : 0;
        final minPower = math.pow(10, minMagnitude).toDouble();
        minY = (minY / minPower).floor() * minPower;
      }
    }

    // Calculate average income
    final averageIncome = sortedIncome.isNotEmpty
        ? sortedIncome.fold<double>(
                0,
                (sum, income) => sum + (income.netIncome ?? 0),
              ) /
              sortedIncome.length
        : 0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          _groupByWeek ? 'Weekly Income Trend' : 'Daily Income Trend',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        Center(
          // Center the chart horizontally
          child: Container(
            height: 250,
            width: double.infinity, // Take full width
            margin: const EdgeInsets.symmetric(
              horizontal: 4.0,
            ), // Add horizontal margin
            child: AnimatedOpacity(
              opacity: _showChart ? 1.0 : 0.0,
              duration: const Duration(milliseconds: 500),
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  vertical: 8.0,
                ), // Only vertical padding
                child: LineChart(
                  LineChartData(
                    gridData: FlGridData(
                      show: true,
                      drawVerticalLine: true,
                      horizontalInterval:
                          (maxY - minY) / 4, // Match Y-axis labels interval
                      getDrawingHorizontalLine: (value) {
                        return FlLine(
                          color: Colors.grey.shade200,
                          strokeWidth: 0.8,
                        );
                      },
                      getDrawingVerticalLine: (value) {
                        return FlLine(
                          color: Colors.grey.shade200,
                          strokeWidth: 0.8,
                        );
                      },
                    ),
                    titlesData: FlTitlesData(
                      leftTitles: AxisTitles(
                        sideTitles: SideTitles(
                          showTitles: true,
                          reservedSize:
                              35, // Adjusted to 35px to provide more space for labels
                          interval: (maxY - minY) / 4, // Keep 4 labels
                          getTitlesWidget: (value, meta) {
                            // Ultra-compact formatting for your data range
                            String formattedValue;
                            if (value == 0) {
                              formattedValue = '0';
                            } else if (value.abs() < 1000) {
                              formattedValue = value.round().toString();
                            } else {
                              // For all values over 1000, show as "1K", "10K", "100K" without decimals
                              formattedValue = '${(value / 1000).round()}K';
                            }

                            return Padding(
                              padding: const EdgeInsets.only(
                                right: 6.0,
                              ), // Increased padding between label and grid
                              child: SizedBox(
                                width:
                                    30, // Slightly wider container for better spacing
                                child: Text(
                                  formattedValue,
                                  style: const TextStyle(
                                    fontSize: 8, // Keep small font
                                    fontWeight: FontWeight.w500,
                                  ),
                                  textAlign: TextAlign.end, // Right align text
                                  maxLines: 1,
                                  overflow: TextOverflow
                                      .visible, // Allow overflow if needed
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                      bottomTitles: AxisTitles(
                        sideTitles: SideTitles(
                          showTitles: true,
                          getTitlesWidget: (value, meta) {
                            // Show fewer labels for readability
                            if (value % 2 != 0) {
                              return const SizedBox.shrink();
                            }

                            final index = value.toInt();
                            if (index >= 0 && index < sortedIncome.length) {
                              // Check if this is the last label (rightmost)
                              final isLastLabel =
                                  index == sortedIncome.length - 1;

                              return Padding(
                                // Add extra padding for the last label to prevent overflow
                                padding: EdgeInsets.only(
                                  top: 8.0,
                                  right: isLastLabel ? 8.0 : 0.0,
                                ),
                                child: Text(
                                  _groupByWeek
                                      ? (_weekLabels.isNotEmpty &&
                                                index < _weekLabels.length
                                            ? _weekLabels[index] // Show formatted week label (W1: 28/04)
                                            : 'W${index + 1}')
                                      : DateFormat(
                                          'dd/MM',
                                        ).format(sortedIncome[index].date),
                                  style: const TextStyle(
                                    fontSize: 9,
                                  ), // Slightly smaller
                                  textAlign: isLastLabel
                                      ? TextAlign.end
                                      : TextAlign.center,
                                ),
                              );
                            }
                            return const SizedBox.shrink();
                          },
                          reservedSize:
                              25, // Adjusted to 25px for better spacing
                        ),
                      ),
                      rightTitles: const AxisTitles(
                        sideTitles: SideTitles(showTitles: false),
                      ),
                      topTitles: const AxisTitles(
                        sideTitles: SideTitles(showTitles: false),
                      ),
                    ),
                    minY: minY,
                    maxY: maxY,
                    lineBarsData: [
                      // Daily income
                      LineChartBarData(
                        spots: incomeSpots,
                        isCurved: true,
                        color: AppColors.primary,
                        barWidth: 3,
                        isStrokeCapRound: true,
                        dotData: const FlDotData(show: false),
                        belowBarData: BarAreaData(
                          show: true,
                          color: AppColors.primary.withAlpha(
                            (0.1 * 255).toInt(),
                          ),
                        ),
                      ),
                      // Running average
                      LineChartBarData(
                        spots: averageSpots,
                        isCurved: true,
                        color: AppColors.secondary,
                        barWidth: 3,
                        isStrokeCapRound: true,
                        dotData: const FlDotData(show: false),
                        belowBarData: BarAreaData(
                          show: true,
                          color: AppColors.secondary.withAlpha(
                            (0.1 * 255).toInt(),
                          ),
                        ),
                      ),
                    ],
                    lineTouchData: LineTouchData(
                      touchTooltipData: LineTouchTooltipData(
                        getTooltipColor: (touchedSpot) =>
                            Colors.black.withAlpha((0.8 * 255).toInt()),
                        getTooltipItems: (touchedSpots) {
                          return touchedSpots.map((spot) {
                            final index = spot.x.toInt();
                            String date;
                            if (_groupByWeek) {
                              if (index < _orderedWeekKeys.length) {
                                // Get the original week range (e.g., "28/04-04/05")
                                final weekRange = _orderedWeekKeys[index];
                                final startDate = DateFormat(
                                  'dd/MM',
                                ).parse(weekRange.split('-')[0]);
                                final endDate = DateFormat(
                                  'dd/MM',
                                ).parse(weekRange.split('-')[1]);

                                // Format as "Week 1 (Mon 28/04 - Sun 04/05)"
                                date =
                                    'Week ${index + 1} (${DateFormat('EEE dd/MM').format(startDate)} - ${DateFormat('EEE dd/MM').format(endDate)})';
                              } else {
                                date = 'Week ${index + 1}';
                              }
                            } else {
                              date = index < sortedIncome.length
                                  ? DateHelper.formatForDisplay(
                                      sortedIncome[index].date,
                                    )
                                  : '';
                            }

                            final isIncome = spot.barIndex == 0;

                            return LineTooltipItem(
                              '$date\n${Calculations.formatCurrency(spot.y)}',
                              TextStyle(
                                color: isIncome
                                    ? AppColors.primary
                                    : AppColors.secondary,
                                fontWeight: FontWeight.bold,
                              ),
                            );
                          }).toList();
                        },
                      ),
                    ),
                    borderData: FlBorderData(
                      show: true,
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    minX: 0,
                    maxX: incomeSpots.isNotEmpty ? incomeSpots.length - 1.0 : 0,
                    clipData:
                        const FlClipData.all(), // Ensure chart is clipped properly
                  ),
                ),
              ),
            ),
          ),
        ),
        const SizedBox(height: 8),
        // Legend
        Wrap(
          spacing: 16,
          children: [
            _buildLegendItem(
              _groupByWeek ? 'Weekly Income' : 'Daily Income',
              AppColors.primary,
            ),
            _buildLegendItem('Running Average', AppColors.secondary),
          ],
        ),
        const SizedBox(height: 16),
        // Average income
        Text(
          'Average Income: ${Calculations.formatCurrency(averageIncome.toDouble())}',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  Widget _buildLegendItem(String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(color: color, shape: BoxShape.circle),
        ),
        const SizedBox(width: 8),
        Text(label, style: const TextStyle(fontSize: 12)),
      ],
    );
  }
}

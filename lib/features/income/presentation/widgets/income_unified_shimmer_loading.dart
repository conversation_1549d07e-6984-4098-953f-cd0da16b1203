import 'package:flutter/material.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/widgets/shimmer/shimmer_components.dart';

/// Widget that displays a unified shimmer loading effect for the entire income screen
class IncomeUnifiedShimmerLoading extends StatelessWidget {
  const IncomeUnifiedShimmerLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return SliverFillRemaining(
      child: ShimmerWrapper(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Summary section shimmer
              _buildSummarySectionShimmer(context),

              // History header shimmer
              _buildHistoryHeaderShimmer(context),

              // Income list shimmer
              _buildIncomeListShimmer(context),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds the shimmer effect for the summary section
  Widget _buildSummarySectionShimmer(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 0.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Summary title
          ShimmerText.title(),
          const SizedBox(height: 8),

          // Summary subtitle
          ShimmerText.subtitle(),
          const SizedBox(height: 16),

          // 1. Income summary card
          _buildSummaryCardShimmer(
            context,
            AppColors.primary.withAlpha(25), // 0.1 * 255 = 25
          ),
          const SizedBox(height: 16),

          // 2. Mileage summary card
          _buildSummaryCardShimmer(
            context,
            AppColors.success.withAlpha(25), // 0.1 * 255 = 25
          ),
          const SizedBox(height: 16),

          // 3. Income trends card
          const ShimmerCard(
            height: 200,
            padding: EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Card title
                ShimmerText(width: 150, height: 18),
                SizedBox(height: 16),

                // Chart placeholder
                Expanded(
                  child: ShimmerContainer(
                    width: double.infinity,
                    height: double.infinity,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the shimmer effect for a summary card
  Widget _buildSummaryCardShimmer(BuildContext context, Color cardColor) {
    return const ShimmerCard(
      height: 120,
      child: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Card title
            ShimmerText(width: 150, height: 18),
            SizedBox(height: 16),

            // Card content
            Row(
              children: [
                // Icon placeholder
                ShimmerCircle(size: 40),
                SizedBox(width: 16),

                // Value placeholder
                ShimmerText(width: 120, height: 24),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the shimmer effect for the history header
  Widget _buildHistoryHeaderShimmer(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16.0, 24.0, 16.0, 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // History title
          ShimmerText.title(),

          // Filter button
          ShimmerContainer(
            width: 80,
            height: 32,
            borderRadius: BorderRadius.circular(16),
          ),
        ],
      ),
    );
  }

  /// Builds the shimmer effect for the income list
  Widget _buildIncomeListShimmer(BuildContext context) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: 5,
      itemBuilder: (context, index) {
        return const Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: ShimmerCard(
            height: 100,
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: Row(
                children: [
                  // Left section - date
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ShimmerText(width: 40, height: 16),
                      SizedBox(height: 8),
                      ShimmerText(width: 30, height: 14),
                    ],
                  ),

                  // Vertical divider
                  ShimmerDivider(
                    width: 1,
                    height: 60,
                    margin: EdgeInsets.symmetric(horizontal: 16),
                  ),

                  // Middle section - income details
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        ShimmerText(width: 120, height: 16),
                        SizedBox(height: 8),
                        ShimmerText(width: 80, height: 14),
                      ],
                    ),
                  ),

                  // Right section - amount
                  ShimmerText(width: 80, height: 20),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Widget that displays a shimmer loading effect for the income list only
class IncomeListShimmerLoading extends StatelessWidget {
  const IncomeListShimmerLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return SliverFillRemaining(
      child: ShimmerWrapper(
        child: ListView.builder(
          itemCount: 8,
          itemBuilder: (context, index) {
            return const Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
              child: ShimmerCard(
                height: 100,
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Row(
                    children: [
                      // Left section - date
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          ShimmerText(width: 40, height: 16),
                          SizedBox(height: 8),
                          ShimmerText(width: 30, height: 14),
                        ],
                      ),

                      // Vertical divider
                      ShimmerDivider(
                        width: 1,
                        height: 60,
                        margin: EdgeInsets.symmetric(horizontal: 16),
                      ),

                      // Middle section - income details
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            ShimmerText(width: 120, height: 16),
                            SizedBox(height: 8),
                            ShimmerText(width: 80, height: 14),
                          ],
                        ),
                      ),

                      // Right section - amount
                      ShimmerText(width: 80, height: 20),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

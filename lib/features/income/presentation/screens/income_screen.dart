import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../core/providers/global_date_range_provider.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/date_helper.dart';
import '../../../../core/utils/snackbar_utils.dart';
import '../../../../core/widgets/date_range_selector_field.dart';
import '../../../../core/widgets/item_actions_bottom_sheet.dart';
import '../../../../core/widgets/shimmer/shimmer_components.dart';
import '../../domain/entities/income.dart';
import '../providers/income_providers.dart' as providers;
import '../widgets/income_details_sheet.dart';
import '../widgets/income_history_header.dart';
import '../widgets/income_item_card.dart';
// Using the unified shimmer loading instead of the old implementation
// import '../widgets/income_shimmer_loading.dart';
import '../widgets/income_summary.dart';
import '../widgets/income_trends_card.dart';
import '../widgets/income_unified_shimmer_loading.dart';
import '../widgets/mileage_summary.dart';
import 'income_form_screen.dart';

/// Screen that displays income records and summary information
class IncomeScreen extends ConsumerStatefulWidget {
  const IncomeScreen({super.key});

  @override
  ConsumerState<IncomeScreen> createState() => _IncomeScreenState();
}

class _IncomeScreenState extends ConsumerState<IncomeScreen> {
  // Controller for detecting when we reach the end of the list
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // Add scroll listener to detect when we reach the end of the list
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  // Called when the user scrolls
  void _onScroll() {
    // Check if we're at the bottom of the list
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      // Load more data when we're near the end
      final paginatedIncomeNotifier = ref.read(
        providers.paginatedIncomeListProvider.notifier,
      );
      paginatedIncomeNotifier.loadNextPage();
    }
  }

  @override
  Widget build(BuildContext context) {
    // Watch providers for reactive updates
    final paginatedIncomeAsync = ref.watch(
      providers.paginatedIncomeListProvider,
    );
    final dateRangeAsync = ref.watch(globalDateRangeProvider);
    final incomeSummary = ref.watch(providers.incomeSummaryProvider);

    // Check if any of the async values are in loading state
    final bool isLoading =
        paginatedIncomeAsync.isLoading || dateRangeAsync.isLoading;

    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () async {
          // Refresh both the regular income list (for summary) and paginated list
          ref.invalidate(providers.incomeListProvider);
          await ref
              .read(providers.paginatedIncomeListProvider.notifier)
              .refresh();
        },
        child: CustomScrollView(
          controller: _scrollController,
          slivers: [
            _buildAppBar(dateRangeAsync),

            // If loading, show unified shimmer loading, otherwise show content
            if (isLoading)
              const IncomeUnifiedShimmerLoading()
            else ...[
              _buildSummarySection(incomeSummary),
              _buildHistoryHeader(),
              _buildPaginatedIncomeListSection(paginatedIncomeAsync),
            ],
          ],
        ),
      ),
    );
  }

  /// Builds the app bar with date range selector
  SliverAppBar _buildAppBar(AsyncValue<DateTimeRange> dateRangeAsync) {
    return SliverAppBar(
      floating: true,
      pinned: true,
      elevation: 0,
      backgroundColor: AppColors.primary,
      title: const Text('Income'),
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(60),
        child: Padding(
          padding: const EdgeInsets.fromLTRB(16.0, 4.0, 16.0, 12.0),
          child: dateRangeAsync.when(
            data: (dateRange) =>
                _buildDateRangeSelector(context, ref, dateRange),
            loading: () => const ShimmerDateRangeSelector(),
            error: (error, stack) => _buildErrorContainer(error),
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.add),
          tooltip: 'Add new income record',
          onPressed: () => _navigateToIncomeForm(context),
        ),
      ],
    );
  }

  /// Builds an error container with the given error message
  Widget _buildErrorContainer(Object error) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.red.withAlpha(30),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          const Icon(Icons.error_outline, color: Colors.red, size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'Error: $error',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the summary section with income, forecast, and mileage cards
  SliverToBoxAdapter _buildSummarySection(
    providers.IncomeSummary incomeSummary,
  ) {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 0.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Summary',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            Padding(
              padding: const EdgeInsets.only(bottom: 12.0),
              child: Text(
                'Overview of your income and forecasts',
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
              ),
            ),
            _buildSummaryCards(incomeSummary),
          ],
        ),
      ),
    );
  }

  /// Builds the history header section
  SliverToBoxAdapter _buildHistoryHeader() {
    return const SliverToBoxAdapter(
      child: Padding(
        padding: EdgeInsets.fromLTRB(16.0, 24.0, 16.0, 8.0),
        child: IncomeHistoryHeader(),
      ),
    );
  }

  /// Builds the paginated income list section
  Widget _buildPaginatedIncomeListSection(
    AsyncValue<providers.PaginatedIncomeResult> paginatedIncomeAsync,
  ) {
    return paginatedIncomeAsync.when(
      data: (paginatedResult) =>
          _buildPaginatedIncomeList(context, ref, paginatedResult),
      loading: () => const IncomeUnifiedShimmerLoading(),
      error: (error, stack) =>
          SliverFillRemaining(child: Center(child: Text('Error: $error'))),
    );
  }

  /// Builds the date range selector widget
  Widget _buildDateRangeSelector(
    BuildContext context,
    WidgetRef ref,
    DateTimeRange dateRange,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withAlpha(30),
        borderRadius: BorderRadius.circular(8),
      ),
      child: DateRangeSelectorField(
        dateRange: dateRange,
        onDateRangeSelected: (newRange) async {
          await ref
              .read(globalDateRangeProvider.notifier)
              .setDateRange(newRange);
        },
      ),
    );
  }

  /// Builds the summary cards for income, mileage, and trends
  Widget _buildSummaryCards(providers.IncomeSummary summary) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 1. Income summary card
        IncomeSummary(
          totalIncome: summary.totalNetIncome,
          recordCount: summary.recordCount,
          totalRecordCount: summary.totalRecordCount,
          highestIncome: summary.highestIncome,
        ),
        const SizedBox(height: 16),

        // 2. Mileage summary card
        MileageSummary(
          totalMileage: summary.totalMileage,
          recordCount: summary.recordCount,
        ),
        const SizedBox(height: 16),

        // 3. Income trends card
        Consumer(
          builder: (context, ref, child) {
            final filteredIncomeAsync = ref.watch(
              providers.filteredIncomeListProvider,
            );
            return filteredIncomeAsync.when(
              data: (incomeRecords) =>
                  IncomeTrendsCard(incomeRecords: incomeRecords),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => _buildErrorContainer(error),
            );
          },
        ),
      ],
    );
  }

  /// Builds the paginated income list or empty state
  Widget _buildPaginatedIncomeList(
    BuildContext context,
    WidgetRef ref,
    providers.PaginatedIncomeResult paginatedResult,
  ) {
    final incomeList = paginatedResult.incomeList;
    final hasMorePages = paginatedResult.hasMorePages;

    if (incomeList.isEmpty) {
      return SliverList(
        delegate: SliverChildListDelegate([_buildEmptyState(context)]),
      );
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          // If we're at the last item and there are more pages, show a loading indicator
          if (index == incomeList.length) {
            return hasMorePages
                ? const Padding(
                    padding: EdgeInsets.symmetric(vertical: 16.0),
                    child: Center(child: CircularProgressIndicator()),
                  )
                : const SizedBox(
                    height: 40,
                  ); // Extra space at the end of the list
          }

          final income = incomeList[index];
          return IncomeItemCard(
            income: income,
            onTap: () => _showIncomeDetails(context, income),
            onLongPress: () => _showActionsBottomSheet(context, income),
          );
        },
        // Add +1 for the loading indicator or extra space
        childCount: incomeList.length + 1,
      ),
    );
  }

  /// Builds the empty state widget when no income records are found
  Widget _buildEmptyState(BuildContext context) {
    return SizedBox(
      height: MediaQuery.of(context).size.height * 0.4,
      child: Center(
        child: Card(
          margin: const EdgeInsets.symmetric(horizontal: 32),
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: BorderSide(color: Colors.grey.withAlpha(30), width: 1),
          ),
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.account_balance_wallet_outlined,
                  size: 48,
                  color: AppColors.success,
                ),
                const SizedBox(height: 20),
                Text(
                  'No Income Records Found',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  'Add your first income record by tapping the Add button',
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),
                ElevatedButton.icon(
                  onPressed: () => _navigateToIncomeForm(context),
                  icon: const Icon(Icons.add, size: 16),
                  label: const Text('Add Income Record'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 10,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.refresh,
                      size: 14,
                      color: AppColors.textSecondary,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Pull down to refresh',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Navigates to the income form screen
  void _navigateToIncomeForm(BuildContext context, {Income? income}) {
    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (context) => IncomeFormScreen(income: income),
          ),
        )
        .then((_) {
          // Refresh both lists when returning from the form screen
          ref.invalidate(providers.incomeListProvider);
          ref.read(providers.paginatedIncomeListProvider.notifier).refresh();
        });
  }

  /// Shows the actions bottom sheet for an income record
  void _showActionsBottomSheet(BuildContext context, Income income) {
    ItemActionsBottomSheet.show(
      context: context,
      title: 'Income Record',
      subtitle: 'Date: ${DateHelper.formatForDisplay(income.date)}',
      onEdit: () => _navigateToIncomeForm(context, income: income),
      onDelete: () => _showDeleteConfirmation(context, income),
      itemIcon: Icons.account_balance_wallet,
    );
  }

  /// Shows a confirmation dialog before deleting an income record
  void _showDeleteConfirmation(BuildContext context, Income income) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Income Record'),
        content: Text(
          'Are you sure you want to delete the income record for ${DateHelper.formatForDisplay(income.date)}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _deleteIncome(income);
            },
            child: const Text('DELETE', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  /// Deletes an income record
  void _deleteIncome(Income income) async {
    if (income.id == null) return;

    // Show loading indicator
    SnackbarUtils.showLoading(message: 'Deleting...');

    // Use the provider to delete the income record
    final success = await ref
        .read(providers.incomeListProvider.notifier)
        .deleteIncome(income.id!);

    if (mounted) {
      if (success) {
        SnackbarUtils.showSuccess('Income record deleted successfully');

        // Refresh the paginated list
        await ref
            .read(providers.paginatedIncomeListProvider.notifier)
            .refresh();
      } else {
        SnackbarUtils.showError('Failed to delete income record');
      }
    }
  }

  /// Shows the income details bottom sheet
  void _showIncomeDetails(BuildContext context, Income income) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => IncomeDetailsSheet(income: income),
    );
  }
}

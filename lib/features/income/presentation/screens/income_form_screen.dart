import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../core/utils/snackbar_utils.dart';
import '../../../../core/widgets/base_form_screen.dart';
import '../../../../core/widgets/currency_input_field.dart';
import '../../../../core/widgets/mileage_input.dart';
import '../../domain/entities/income.dart';
import '../providers/income_providers.dart';

class IncomeFormScreen extends BaseFormScreen<Income> {
  const IncomeFormScreen({super.key, Income? income}) : super(entity: income);

  @override
  ConsumerState<IncomeFormScreen> createState() => _IncomeFormScreenState();
}

class _IncomeFormScreenState
    extends BaseFormScreenState<Income, IncomeFormScreen> {
  // Mileage controllers
  final _initialMileageController = TextEditingController();
  final _finalMileageController = TextEditingController();

  // Initial balance controllers
  final _initialGopayController = TextEditingController();
  final _initialBcaController = TextEditingController();
  final _initialCashController = TextEditingController();
  final _initialOvoController = TextEditingController();
  final _initialBriController = TextEditingController();
  final _initialRekponController = TextEditingController();

  // Final balance controllers
  final _finalGopayController = TextEditingController();
  final _finalBcaController = TextEditingController();
  final _finalCashController = TextEditingController();
  final _finalOvoController = TextEditingController();
  final _finalBriController = TextEditingController();
  final _finalRekponController = TextEditingController();

  @override
  DateTime getInitialDate() {
    return widget.entity?.date ?? DateTime.now();
  }

  @override
  void initializeControllers() {
    if (isEditing) {
      // Populate form with existing data
      // For mileage fields, don't show 0 values
      if (widget.entity!.initialMileage > 0) {
        _initialMileageController.text = widget.entity!.initialMileage
            .toString();
      }
      if (widget.entity!.finalMileage > 0) {
        _finalMileageController.text = widget.entity!.finalMileage.toString();
      }

      // For currency fields, don't show 0.0 values
      if (widget.entity!.initialGopay != 0.0) {
        _initialGopayController.text = widget.entity!.initialGopay.toString();
      }
      if (widget.entity!.initialBca != 0.0) {
        _initialBcaController.text = widget.entity!.initialBca.toString();
      }
      if (widget.entity!.initialCash != 0.0) {
        _initialCashController.text = widget.entity!.initialCash.toString();
      }
      if (widget.entity!.initialOvo != 0.0) {
        _initialOvoController.text = widget.entity!.initialOvo.toString();
      }
      if (widget.entity!.initialBri != 0.0) {
        _initialBriController.text = widget.entity!.initialBri.toString();
      }
      if (widget.entity!.initialRekpon != 0.0) {
        _initialRekponController.text = widget.entity!.initialRekpon.toString();
      }

      if (widget.entity!.finalGopay != 0.0) {
        _finalGopayController.text = widget.entity!.finalGopay.toString();
      }
      if (widget.entity!.finalBca != 0.0) {
        _finalBcaController.text = widget.entity!.finalBca.toString();
      }
      if (widget.entity!.finalCash != 0.0) {
        _finalCashController.text = widget.entity!.finalCash.toString();
      }
      if (widget.entity!.finalOvo != 0.0) {
        _finalOvoController.text = widget.entity!.finalOvo.toString();
      }
      if (widget.entity!.finalBri != 0.0) {
        _finalBriController.text = widget.entity!.finalBri.toString();
      }
      if (widget.entity!.finalRekpon != 0.0) {
        _finalRekponController.text = widget.entity!.finalRekpon.toString();
      }
    }
  }

  @override
  void disposeControllers() {
    _initialMileageController.dispose();
    _finalMileageController.dispose();

    _initialGopayController.dispose();
    _initialBcaController.dispose();
    _initialCashController.dispose();
    _initialOvoController.dispose();
    _initialBriController.dispose();
    _initialRekponController.dispose();

    _finalGopayController.dispose();
    _finalBcaController.dispose();
    _finalCashController.dispose();
    _finalOvoController.dispose();
    _finalBriController.dispose();
    _finalRekponController.dispose();
  }

  @override
  String getFormTitle() {
    return isEditing ? 'Edit Income Record' : 'Add Income Record';
  }

  @override
  Future<bool> checkDateExists() async {
    final repository = ref.read(incomeRepositoryProvider);
    final dateExistsResult = await repository.checkDateExists(
      selectedDate,
      excludeId: isEditing ? widget.entity!.id : null,
    );

    return dateExistsResult.fold((failure) {
      // If there was an error checking the date, show an error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error checking date: ${failure.toString()}'),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return false; // Assume date doesn't exist if there was an error
    }, (exists) => exists);
  }

  @override
  Future<void> submitForm() async {
    // Parse initial balance values, defaulting to 0 if empty
    final initialGopay = _initialGopayController.text.isEmpty
        ? 0.0
        : double.parse(_initialGopayController.text);
    final initialBca = _initialBcaController.text.isEmpty
        ? 0.0
        : double.parse(_initialBcaController.text);
    final initialCash = _initialCashController.text.isEmpty
        ? 0.0
        : double.parse(_initialCashController.text);
    final initialOvo = _initialOvoController.text.isEmpty
        ? 0.0
        : double.parse(_initialOvoController.text);
    final initialBri = _initialBriController.text.isEmpty
        ? 0.0
        : double.parse(_initialBriController.text);
    final initialRekpon = _initialRekponController.text.isEmpty
        ? 0.0
        : double.parse(_initialRekponController.text);

    // Parse initial mileage, defaulting to 0 if empty
    final initialMileage = _initialMileageController.text.isEmpty
        ? 0
        : int.parse(_initialMileageController.text);

    // When adding a new record, set final mileage to 0 instead of copying initial mileage
    // This makes it easier when editing later as the field won't be pre-filled with initial value
    final finalMileage = isEditing && _finalMileageController.text.isNotEmpty
        ? int.parse(_finalMileageController.text)
        : 0;

    final income = Income(
      id: widget.entity?.id,
      date: selectedDate,
      initialMileage: initialMileage,
      finalMileage: finalMileage,
      initialGopay: initialGopay,
      initialBca: initialBca,
      initialCash: initialCash,
      initialOvo: initialOvo,
      initialBri: initialBri,
      initialRekpon: initialRekpon,
      // When adding a new record, set final values to 0 instead of copying initial values
      // This makes it easier when editing later as the fields won't be pre-filled with initial values
      finalGopay: isEditing
          ? (_finalGopayController.text.isEmpty
                ? 0.0
                : double.parse(_finalGopayController.text))
          : 0.0,
      finalBca: isEditing
          ? (_finalBcaController.text.isEmpty
                ? 0.0
                : double.parse(_finalBcaController.text))
          : 0.0,
      finalCash: isEditing
          ? (_finalCashController.text.isEmpty
                ? 0.0
                : double.parse(_finalCashController.text))
          : 0.0,
      finalOvo: isEditing
          ? (_finalOvoController.text.isEmpty
                ? 0.0
                : double.parse(_finalOvoController.text))
          : 0.0,
      finalBri: isEditing
          ? (_finalBriController.text.isEmpty
                ? 0.0
                : double.parse(_finalBriController.text))
          : 0.0,
      finalRekpon: isEditing
          ? (_finalRekponController.text.isEmpty
                ? 0.0
                : double.parse(_finalRekponController.text))
          : 0.0,
    );

    bool success;

    if (isEditing) {
      success = await ref
          .read(incomeListProvider.notifier)
          .updateIncome(income);
    } else {
      success = await ref.read(incomeListProvider.notifier).addIncome(income);
    }

    // Show success or error message
    if (mounted) {
      if (success) {
        SnackbarUtils.showSuccess(
          '${isEditing ? 'Updated' : 'Added'} income record successfully',
        );
      } else {
        SnackbarUtils.showError(
          'Failed to ${isEditing ? 'update' : 'add'} income record',
        );
      }
    }
  }

  @override
  List<Widget> buildFormFields() {
    return [
      buildSectionTitle('Mileage Information', icon: Icons.speed),
      MileageInput(
        label: 'Initial Mileage',
        controller: _initialMileageController,
        isRequired: true,
      ),
      // Only show Final Mileage when editing
      if (isEditing)
        MileageInput(
          label: 'Final Mileage',
          controller: _finalMileageController,
          isRequired: false,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter Final Mileage';
            }

            final initialMileage =
                int.tryParse(_initialMileageController.text) ?? 0;
            final finalMileage = int.tryParse(value) ?? 0;

            if (finalMileage < initialMileage) {
              return 'Final mileage cannot be less than initial mileage';
            }

            return null;
          },
        ),

      const SizedBox(height: 24),
      buildSectionTitle('Initial Balance', icon: Icons.account_balance_wallet),
      CurrencyInputField(
        label: 'Initial Gopay',
        controller: _initialGopayController,
        allowNegative: true,
      ),
      CurrencyInputField(
        label: 'Initial BCA',
        controller: _initialBcaController,
        allowNegative: true,
      ),
      CurrencyInputField(
        label: 'Initial Cash',
        controller: _initialCashController,
        allowNegative: true,
      ),
      CurrencyInputField(
        label: 'Initial OVO',
        controller: _initialOvoController,
        allowNegative: true,
      ),
      CurrencyInputField(
        label: 'Initial BRI',
        controller: _initialBriController,
        allowNegative: true,
      ),
      CurrencyInputField(
        label: 'Initial Rekpon',
        controller: _initialRekponController,
        allowNegative: true,
      ),

      // Only show Final Balance section when editing
      if (isEditing) ...[
        const SizedBox(height: 24),
        buildSectionTitle('Final Balance', icon: Icons.savings),
        CurrencyInputField(
          label: 'Final Gopay',
          controller: _finalGopayController,
          allowNegative: true,
        ),
        CurrencyInputField(
          label: 'Final BCA',
          controller: _finalBcaController,
          allowNegative: true,
        ),
        CurrencyInputField(
          label: 'Final Cash',
          controller: _finalCashController,
          allowNegative: true,
        ),
        CurrencyInputField(
          label: 'Final OVO',
          controller: _finalOvoController,
          allowNegative: true,
        ),
        CurrencyInputField(
          label: 'Final BRI',
          controller: _finalBriController,
          allowNegative: true,
        ),
        CurrencyInputField(
          label: 'Final Rekpon',
          controller: _finalRekponController,
          allowNegative: true,
        ),
      ],
    ];
  }
}

import 'package:dartz/dartz.dart';
import 'package:drift/drift.dart' as db;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';

import '../../../../core/datasources/app_database.dart' as db;
import '../../../../core/errors/exceptions.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/providers/app_settings_provider.dart';
import '../../../../core/repositories/base_repository.dart';
import '../../../../core/services/calculation/income_calculation_service.dart';
import '../../../../core/services/calculation/providers/calculation_providers.dart';
import '../../../../core/services/sync/sync_operations.dart';
import '../../../../core/services/sync/sync_service.dart';
import '../../domain/entities/income.dart';
import '../../domain/repositories/income_repository.dart';

// Provider for Income Repository
final incomeRepositoryProvider = Provider<IncomeRepository>((ref) {
  final database = ref.watch(databaseProvider);
  final syncService = ref.watch(syncServiceProvider);
  final calculationService = ref.watch(incomeCalculationServiceProvider);

  return IncomeRepositoryImpl(
    database: database,
    calculationService: calculationService,
    syncService: syncService,
  );
});

// Interface for Income Repository
abstract class DataIncomeRepository extends SyncableRepository<db.IncomeData> {}

class IncomeRepositoryImpl implements IncomeRepository {
  final db.AppDatabase database;
  final SyncService syncService;
  final IncomeCalculationService calculationService;

  IncomeRepositoryImpl({
    required this.database,
    required this.calculationService,
    required this.syncService,
  });

  // Helper method to trigger sync
  void _triggerSync() {
    syncService.syncData(SyncOperation.upload);
  }

  // Implement IRepository methods
  @override
  Future<Either<Failure, List<Income>>> getAll() async {
    try {
      final incomeList = await database.getAllIncome(); // List<db.IncomeData>

      return Right(incomeList.map((data) => _mapFromData(data)).toList());
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Income>> getById(int id) async {
    try {
      final query = database.select(database.income)
        ..where((tbl) => tbl.id.equals(id));

      final incomeData = await query.getSingleOrNull();

      if (incomeData == null) {
        return const Left(Failure.notFound(message: 'Income not found'));
      }

      return Right(_mapFromData(incomeData));
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Income>> save(Income income) async {
    try {
      // Calculate all derived fields using the calculation service
      final calculatedIncomeResult = calculationService.calculateIncome(income);

      return calculatedIncomeResult.fold(
        (failure) => Left(failure),
        (calculatedIncome) async {
          final id = await database.insertIncome(_mapToCompanion(calculatedIncome)); // For new records, no need to pass uuid or originalCreatedAt

          // Update spare parts mileage with the latest mileage
          await database.updateSparePartsMileage(calculatedIncome.finalMileage);

          // Trigger sync after adding a record
          _triggerSync();

          return Right(calculatedIncome.copyWith(id: id));
        },
      );
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Income>> update(Income income) async {
    try {
      if (income.id == null) {
        return const Left(Failure.invalidInput(message: 'Income ID cannot be null for update'));
      }

      // Get the existing record to preserve the UUID
      final existingRecordResult = await getById(income.id!);

      return existingRecordResult.fold(
        (failure) => Left(failure),
        (existingIncome) async {
          // Calculate all derived fields using the calculation service
          final calculatedIncomeResult = calculationService.calculateIncome(income);

          return calculatedIncomeResult.fold(
            (failure) => Left(failure),
            (calculatedIncome) async {
              // Get the raw database record to get UUID and createdAt
              final dbQuery = database.select(database.income)
                ..where((tbl) => tbl.id.equals(income.id!));
              final existingRecord = await dbQuery.getSingleOrNull();

              if (existingRecord == null) {
                return const Left(Failure.notFound(message: 'Income record not found in database'));
              }

              // Map to data with the existing UUID and original createdAt
              final incomeData = _mapToDataWithUuid(
                calculatedIncome,
                existingRecord.uuid,
                originalCreatedAt: existingRecord.createdAt
              );
              final success = await database.updateIncome(incomeData);

              if (!success) {
                return const Left(Failure.notFound(message: 'Income not found'));
              }

              // Update spare parts mileage with the latest mileage
              await database.updateSparePartsMileage(calculatedIncome.finalMileage);

              // Trigger sync after updating a record
              _triggerSync();

              return Right(calculatedIncome);
            },
          );
        },
      );
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> delete(int id) async {
    try {
      // Use soft delete instead of hard delete
      final success = await database.softDeleteIncome(id);

      if (!success) {
        return const Left(Failure.notFound(message: 'Income not found'));
      }

      // Trigger sync after deleting a record
      _triggerSync();

      return const Right(true);
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  // Implement IDateRangeRepository methods
  @override
  Future<Either<Failure, List<Income>>> getForDateRange(
    DateTime start,
    DateTime end,
  ) async {
    try {
      final query = database.select(database.income)
        ..where((tbl) => tbl.date.isBetweenValues(start, end))
        ..where((tbl) => tbl.deletedAt.isNull())
        ..orderBy([(tbl) => db.OrderingTerm.desc(tbl.date)]);

      final incomeList = await query.get();

      return Right(incomeList.map((data) => _mapFromData(data)).toList());
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Income>>> getPaginated({
    required int page,
    required int pageSize,
    DateTime? start,
    DateTime? end,
  }) async {
    try {
      // Create a query for income records
      final query = database.select(database.income)
        ..where((tbl) => tbl.deletedAt.isNull())
        ..orderBy([(tbl) => db.OrderingTerm.desc(tbl.date)]);

      // Add date range filter if provided
      if (start != null && end != null) {
        query.where((tbl) => tbl.date.isBetweenValues(start, end));
      }

      // Calculate offset based on page number (1-based) and page size
      final offset = (page - 1) * pageSize;

      // Apply pagination
      query.limit(pageSize, offset: offset);

      // Execute the query
      final incomeList = await query.get();

      return Right(incomeList.map((data) => _mapFromData(data)).toList());
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Income?>> getLatestIncome() async {
    try {
      final query = database.select(database.income)
        ..where((tbl) => tbl.deletedAt.isNull())
        ..orderBy([(tbl) => db.OrderingTerm.desc(tbl.date)])
        ..limit(1);

      final incomeData = await query.getSingleOrNull();

      if (incomeData == null) {
        return const Right(null);
      }

      return Right(_mapFromData(incomeData));
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, int>> getHighestMileage() async {
    try {
      // Use a custom query to get the maximum final_mileage value from non-deleted records
      final result = await database.customSelect(
        'SELECT MAX(final_mileage) as maxMileage FROM income WHERE deleted_at IS NULL',
      ).getSingleOrNull();

      if (result == null || result.data['maxMileage'] == null) {
        return const Right(0); // Default to 0 if no records found
      }

      final maxMileage = result.data['maxMileage'] as int;
      return Right(maxMileage);
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> checkDateExists(DateTime date, {int? excludeId}) async {
    try {
      // Create a query to find records with the same date
      final query = database.select(database.income)
        ..where((tbl) => tbl.date.equals(date))
        ..where((tbl) => tbl.deletedAt.isNull());

      // If excludeId is provided, exclude that record from the check
      // This is useful when updating an existing record
      if (excludeId != null) {
        query.where((tbl) => tbl.id.isNotValue(excludeId));
      }

      // Get the count of records with the same date
      final count = await query.get().then((records) => records.length);

      // Return true if any records were found with the same date
      return Right(count > 0);
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  // Helper methods to map between entity and database models
  Income _mapFromData(db.IncomeData data) {
    return Income(
      id: data.id,
      date: data.date,
      initialMileage: data.initialMileage,
      finalMileage: data.finalMileage,
      initialGopay: data.initialGopay,
      initialBca: data.initialBca,
      initialCash: data.initialCash,
      initialOvo: data.initialOvo,
      initialBri: data.initialBri,
      initialRekpon: data.initialRekpon,
      finalGopay: data.finalGopay,
      finalBca: data.finalBca,
      finalCash: data.finalCash,
      finalOvo: data.finalOvo,
      finalBri: data.finalBri,
      finalRekpon: data.finalRekpon,
      initialCapital: data.initialCapital,
      finalResult: data.finalResult,
      mileage: data.mileage,
      netIncome: data.netIncome,
    );
  }



  // Map Income entity to IncomeData with a specific UUID (for updates)
  db.IncomeData _mapToDataWithUuid(Income income, String uuid, {DateTime? originalCreatedAt}) {
    return db.IncomeData(
      id: income.id!,
      uuid: uuid,
      date: income.date,
      initialMileage: income.initialMileage,
      finalMileage: income.finalMileage,
      initialGopay: income.initialGopay,
      initialBca: income.initialBca,
      initialCash: income.initialCash,
      initialOvo: income.initialOvo,
      initialBri: income.initialBri,
      initialRekpon: income.initialRekpon,
      finalGopay: income.finalGopay,
      finalBca: income.finalBca,
      finalCash: income.finalCash,
      finalOvo: income.finalOvo,
      finalBri: income.finalBri,
      finalRekpon: income.finalRekpon,
      initialCapital: income.initialCapital,
      finalResult: income.finalResult,
      mileage: income.mileage,
      netIncome: income.netIncome,
      createdAt: originalCreatedAt ?? DateTime.now(), // Preserve original createdAt if provided
      updatedAt: DateTime.now(), // Always use UTC for timestamps
      syncStatus: db.SyncStatus.pendingUpload,
    );
  }

  db.IncomeCompanion _mapToCompanion(Income income, {String? uuid, DateTime? originalCreatedAt}) {
    return db.IncomeCompanion(
      id: income.id == null ? const db.Value.absent() : db.Value(income.id!),
      uuid: db.Value(uuid ?? const Uuid().v4()),
      date: db.Value(income.date),
      initialMileage: db.Value(income.initialMileage),
      finalMileage: db.Value(income.finalMileage),
      initialGopay: db.Value(income.initialGopay),
      initialBca: db.Value(income.initialBca),
      initialCash: db.Value(income.initialCash),
      initialOvo: db.Value(income.initialOvo),
      initialBri: db.Value(income.initialBri),
      initialRekpon: db.Value(income.initialRekpon),
      finalGopay: db.Value(income.finalGopay),
      finalBca: db.Value(income.finalBca),
      finalCash: db.Value(income.finalCash),
      finalOvo: db.Value(income.finalOvo),
      finalBri: db.Value(income.finalBri),
      finalRekpon: db.Value(income.finalRekpon),
      initialCapital: income.initialCapital == null
          ? const db.Value.absent()
          : db.Value(income.initialCapital!),
      finalResult: income.finalResult == null
          ? const db.Value.absent()
          : db.Value(income.finalResult!),
      mileage: income.mileage == null
          ? const db.Value.absent()
          : db.Value(income.mileage!),
      netIncome: income.netIncome == null
          ? const db.Value.absent()
          : db.Value(income.netIncome!),
      createdAt: db.Value(originalCreatedAt ?? DateTime.now()), // Preserve original createdAt if provided
      updatedAt: db.Value(DateTime.now()),
      syncStatus: const db.Value(db.SyncStatus.pendingUpload),
    );
  }

  // Note: This method was removed as it's not used in the current implementation

  // Implement ISyncableRepository methods
  @override
  Future<Either<Failure, List<Income>>> getUnsyncedEntities() async {
    try {
      final unsyncedRecords = await database.getUnsyncedIncome();
      return Right(unsyncedRecords.map((data) => _mapFromData(data)).toList());
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> markAsSynced(String uuid) async {
    try {
      await database.markIncomeAsSynced(uuid);
      return const Right(true);
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> syncEntities() async {
    try {
      await syncService.syncNow(SyncOperation.upload);
      return const Right(true);
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  // Implement ISoftDeleteRepository methods
  @override
  Future<Either<Failure, bool>> softDelete(int id) async {
    // We already implement soft delete in delete method
    try {
      // Use soft delete instead of hard delete
      final success = await database.softDeleteIncome(id);

      if (!success) {
        return const Left(Failure.notFound(message: 'Income not found'));
      }

      // Trigger sync after deleting a record
      _triggerSync();

      return const Right(true);
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Income>>> getAllIncludingDeleted() async {
    try {
      final query = database.select(database.income)
        ..orderBy([(t) => db.OrderingTerm.desc(t.date)]);

      final incomeList = await query.get();
      return Right(incomeList.map((data) => _mapFromData(data)).toList());
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> restore(int id) async {
    try {
      final updateStatement = database.update(database.income);
      updateStatement.where((tbl) => tbl.id.equals(id));
      final rowsAffected = await updateStatement.write(const db.IncomeCompanion(
        deletedAt: db.Value(null),
        syncStatus: db.Value(db.SyncStatus.pendingUpload),
      ));

      if (rowsAffected == 0) {
        return const Left(Failure.notFound(message: 'Income not found'));
      }

      _triggerSync();
      return const Right(true);
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  // This is the end of the class
}

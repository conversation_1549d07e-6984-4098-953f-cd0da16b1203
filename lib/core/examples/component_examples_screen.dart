import 'package:flutter/material.dart';
import '../components/app_components.dart';
import '../theme/design_tokens.dart';

/// A screen that showcases all the components in the application
///
/// This screen is useful for developers to see all available components
/// and how they look with different parameters.
class ComponentExamplesScreen extends StatelessWidget {
  const ComponentExamplesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Component Examples')),
      body: ListView(
        padding: EdgeInsets.all(DesignTokens.spacingLg),
        children: [
          // Section Header
          const AppSectionHeader(
            title: 'Buttons',
            subtitle: 'Different button types and sizes',
            showDivider: true,
          ),

          // Button Examples
          _buildButtonExamples(context),

          const SizedBox(height: 24),

          // Section Header
          const AppSectionHeader(
            title: 'Cards',
            subtitle: 'Different card types',
            showDivider: true,
          ),

          // Card Examples
          _buildCardExamples(context),

          const SizedBox(height: 24),

          // Section Header
          const AppSectionHeader(
            title: 'Text Fields',
            subtitle: 'Input components',
            showDivider: true,
          ),

          // Text Field Examples
          _buildTextFieldExamples(context),

          const SizedBox(height: 24),

          // Section Header
          const AppSectionHeader(
            title: 'Feedback',
            subtitle: 'Loading, empty states, and error states',
            showDivider: true,
          ),

          // Feedback Examples
          _buildFeedbackExamples(context),

          const SizedBox(height: 24),

          // Section Header
          const AppSectionHeader(
            title: 'Lists',
            subtitle: 'List items and dividers',
            showDivider: true,
          ),

          // List Examples
          _buildListExamples(context),

          const SizedBox(height: 24),

          // Section Header
          const AppSectionHeader(
            title: 'Badges',
            subtitle: 'Different badge types and sizes',
            showDivider: true,
          ),

          // Badge Examples
          _buildBadgeExamples(context),
        ],
      ),
    );
  }

  /// Builds examples of different button types and sizes
  Widget _buildButtonExamples(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),

        // Primary Buttons
        const Text('Primary Buttons'),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            AppButton(
              text: 'Small',
              onPressed: () {},
              type: AppButtonType.primary,
              size: AppButtonSize.small,
            ),
            AppButton(
              text: 'Medium',
              onPressed: () {},
              type: AppButtonType.primary,
              size: AppButtonSize.medium,
            ),
            AppButton(
              text: 'Large',
              onPressed: () {},
              type: AppButtonType.primary,
              size: AppButtonSize.large,
            ),
            AppButton(
              text: 'With Icon',
              onPressed: () {},
              type: AppButtonType.primary,
              icon: Icons.add,
            ),
            AppButton(
              text: 'Loading',
              onPressed: () {},
              type: AppButtonType.primary,
              isLoading: true,
            ),
            const AppButton(
              text: 'Disabled',
              onPressed: null,
              type: AppButtonType.primary,
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Secondary Buttons
        const Text('Secondary Buttons'),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            AppButton(
              text: 'Secondary',
              onPressed: () {},
              type: AppButtonType.secondary,
            ),
            AppButton(
              text: 'With Icon',
              onPressed: () {},
              type: AppButtonType.secondary,
              icon: Icons.add,
            ),
            const AppButton(
              text: 'Disabled',
              onPressed: null,
              type: AppButtonType.secondary,
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Outline Buttons
        const Text('Outline Buttons'),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            AppButton(
              text: 'Outline',
              onPressed: () {},
              type: AppButtonType.outline,
            ),
            AppButton(
              text: 'With Icon',
              onPressed: () {},
              type: AppButtonType.outline,
              icon: Icons.add,
            ),
            const AppButton(
              text: 'Disabled',
              onPressed: null,
              type: AppButtonType.outline,
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Text Buttons
        const Text('Text Buttons'),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            AppButton(text: 'Text', onPressed: () {}, type: AppButtonType.text),
            AppButton(
              text: 'With Icon',
              onPressed: () {},
              type: AppButtonType.text,
              icon: Icons.add,
            ),
            const AppButton(
              text: 'Disabled',
              onPressed: null,
              type: AppButtonType.text,
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Danger Buttons
        const Text('Danger Buttons'),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            AppButton(
              text: 'Danger',
              onPressed: () {},
              type: AppButtonType.danger,
            ),
            AppButton(
              text: 'With Icon',
              onPressed: () {},
              type: AppButtonType.danger,
              icon: Icons.delete,
            ),
            const AppButton(
              text: 'Disabled',
              onPressed: null,
              type: AppButtonType.danger,
            ),
          ],
        ),
      ],
    );
  }

  /// Builds examples of different card types
  Widget _buildCardExamples(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),

        // Standard Card
        const AppCard(
          type: AppCardType.standard,
          margin: EdgeInsets.only(bottom: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Standard Card'),
              SizedBox(height: 8),
              Text('This is a standard card with default styling.'),
            ],
          ),
        ),

        // Elevated Card
        const AppCard(
          type: AppCardType.elevated,
          margin: EdgeInsets.only(bottom: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Elevated Card'),
              SizedBox(height: 8),
              Text('This card has elevation for a shadow effect.'),
            ],
          ),
        ),

        // Outlined Card
        const AppCard(
          type: AppCardType.outlined,
          margin: EdgeInsets.only(bottom: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Outlined Card'),
              SizedBox(height: 8),
              Text('This card has a border instead of elevation.'),
            ],
          ),
        ),

        // Filled Card
        const AppCard(
          type: AppCardType.filled,
          margin: EdgeInsets.only(bottom: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Filled Card'),
              SizedBox(height: 8),
              Text('This card has a background color.'),
            ],
          ),
        ),

        // Card with Header
        AppCard(
          type: AppCardType.standard,
          margin: const EdgeInsets.only(bottom: 16),
          header: Container(
            padding: EdgeInsets.all(DesignTokens.spacingMd),
            decoration: BoxDecoration(
              color: DesignTokens.colorPrimaryLight.withAlpha(26),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(DesignTokens.radiusCard),
                topRight: Radius.circular(DesignTokens.radiusCard),
              ),
            ),
            child: const Row(
              children: [
                Icon(Icons.info),
                SizedBox(width: 8),
                Text('Card with Header'),
              ],
            ),
          ),
          child: const Text('This card has a custom header.'),
        ),

        // Card with Footer
        AppCard(
          type: AppCardType.standard,
          margin: const EdgeInsets.only(bottom: 16),
          footer: Container(
            padding: EdgeInsets.all(DesignTokens.spacingMd),
            decoration: BoxDecoration(
              color: DesignTokens.colorPrimaryLight.withAlpha(26),
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(DesignTokens.radiusCard),
                bottomRight: Radius.circular(DesignTokens.radiusCard),
              ),
            ),
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [Text('Footer')],
            ),
          ),
          child: const Text('This card has a custom footer.'),
        ),
      ],
    );
  }

  /// Builds examples of different text field types
  Widget _buildTextFieldExamples(BuildContext context) {
    // Controllers for the text fields
    final TextEditingController standardController = TextEditingController();
    final TextEditingController labelController = TextEditingController();
    final TextEditingController iconController = TextEditingController();
    final TextEditingController errorController = TextEditingController();
    final TextEditingController disabledController = TextEditingController();
    final TextEditingController passwordController = TextEditingController();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),

        // Standard Text Field
        AppTextField(
          controller: standardController,
          labelText: 'Standard Text Field',
          hintText: 'Enter text',
        ),

        const SizedBox(height: 16),

        // Text Field with Helper Text
        AppTextField(
          controller: labelController,
          labelText: 'Text Field with Helper Text',
          hintText: 'Enter text',
          helperText: 'This is helper text',
        ),

        const SizedBox(height: 16),

        // Text Field with Icon
        AppTextField(
          controller: iconController,
          labelText: 'Text Field with Icon',
          hintText: 'Enter text',
          prefixIcon: Icons.person,
        ),

        const SizedBox(height: 16),

        // Text Field with Error
        AppTextField(
          controller: errorController,
          labelText: 'Text Field with Error',
          hintText: 'Enter text',
          errorText: 'This field is required',
        ),

        const SizedBox(height: 16),

        // Disabled Text Field
        AppTextField(
          controller: disabledController,
          labelText: 'Disabled Text Field',
          hintText: 'Enter text',
          enabled: false,
        ),

        const SizedBox(height: 16),

        // Password Text Field
        AppTextField(
          controller: passwordController,
          labelText: 'Password Text Field',
          hintText: 'Enter password',
          obscureText: true,
          prefixIcon: Icons.lock,
        ),
      ],
    );
  }

  /// Builds examples of different feedback components
  Widget _buildFeedbackExamples(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),

        // Loading Indicators
        const Text('Loading Indicators'),
        const SizedBox(height: 8),
        const Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            AppLoadingIndicator(
              type: AppLoadingIndicatorType.circular,
              size: AppLoadingIndicatorSize.small,
            ),
            AppLoadingIndicator(
              type: AppLoadingIndicatorType.circular,
              size: AppLoadingIndicatorSize.medium,
            ),
            AppLoadingIndicator(
              type: AppLoadingIndicatorType.circular,
              size: AppLoadingIndicatorSize.large,
            ),
            AppLoadingIndicator(
              type: AppLoadingIndicatorType.circular,
              size: AppLoadingIndicatorSize.medium,
              message: 'Loading...',
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Linear Loading Indicator
        const AppLoadingIndicator(type: AppLoadingIndicatorType.linear),

        const SizedBox(height: 16),

        // Empty State
        AppCard(
          child: AppEmptyState(
            title: 'No Items Found',
            message: 'Add some items to get started',
            actionButtonText: 'Add Item',
            onActionButtonPressed: () {},
            compact: true,
          ),
        ),

        const SizedBox(height: 16),

        // Error State
        AppCard(
          child: AppErrorState(
            title: 'Something Went Wrong',
            message: 'There was an error loading the data',
            retryButtonText: 'Retry',
            onRetry: () {},
            compact: true,
          ),
        ),
      ],
    );
  }

  /// Builds examples of different list components
  Widget _buildListExamples(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),

        // Standard List Item
        const AppListItem(
          title: 'Standard List Item',
          subtitle: 'This is a standard list item',
          leadingIcon: Icons.star,
          trailingIcon: Icons.chevron_right,
          showDivider: true,
        ),

        // List Item with Badge
        const AppListItem(
          title: 'List Item with Badge',
          subtitle: 'This list item has a badge',
          leadingIcon: Icons.notifications,
          badgeText: 'New',
          badgeColor: Colors.red,
          showDivider: true,
        ),

        // List Item with Additional Info
        const AppListItem(
          title: 'List Item with Additional Info',
          subtitle: 'This list item has additional information',
          additionalInfo: 'Last updated: Today',
          leadingIcon: Icons.info,
          showDivider: true,
        ),

        // Compact List Item
        const AppListItem(
          title: 'Compact List Item',
          subtitle: 'This is a compact list item',
          leadingIcon: Icons.check_circle,
          compact: true,
          showDivider: true,
        ),

        const SizedBox(height: 16),

        // Dividers
        const Text('Dividers'),
        const SizedBox(height: 8),
        const AppDivider(),
        const AppDivider(label: 'Labeled Divider'),
        const AppDivider(thickness: 2.0, color: Colors.blue),

        const SizedBox(height: 16),

        // Section Headers
        const Text('Section Headers'),
        const SizedBox(height: 8),
        const AppSectionHeader(
          title: 'Standard Section Header',
          showDivider: true,
        ),
        const AppSectionHeader(
          title: 'Section Header with Subtitle',
          subtitle: 'This is a subtitle',
          showDivider: true,
        ),
        AppSectionHeader(
          title: 'Section Header with Action',
          actionText: 'See All',
          onAction: () {},
          showDivider: true,
        ),
        const AppSectionHeader(
          title: 'Compact Section Header',
          subtitle: 'This is a compact section header',
          compact: true,
          showDivider: true,
        ),
      ],
    );
  }

  /// Builds examples of different badge types and sizes
  Widget _buildBadgeExamples(BuildContext context) {
    return const Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 16),

        // Badge Types
        Text('Badge Types'),
        SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            AppBadge(text: 'Primary', type: AppBadgeType.primary),
            AppBadge(text: 'Secondary', type: AppBadgeType.secondary),
            AppBadge(text: 'Success', type: AppBadgeType.success),
            AppBadge(text: 'Warning', type: AppBadgeType.warning),
            AppBadge(text: 'Error', type: AppBadgeType.error),
            AppBadge(text: 'Info', type: AppBadgeType.info),
            AppBadge(
              text: 'Custom',
              type: AppBadgeType.custom,
              backgroundColor: Colors.purple,
            ),
          ],
        ),

        SizedBox(height: 16),

        // Badge Sizes
        Text('Badge Sizes'),
        SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            AppBadge(text: 'Small', size: AppBadgeSize.small),
            AppBadge(text: 'Medium', size: AppBadgeSize.medium),
            AppBadge(text: 'Large', size: AppBadgeSize.large),
          ],
        ),

        SizedBox(height: 16),

        // Outlined Badges
        Text('Outlined Badges'),
        SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            AppBadge(
              text: 'Primary',
              type: AppBadgeType.primary,
              filled: false,
            ),
            AppBadge(
              text: 'Secondary',
              type: AppBadgeType.secondary,
              filled: false,
            ),
            AppBadge(
              text: 'Success',
              type: AppBadgeType.success,
              filled: false,
            ),
            AppBadge(
              text: 'Warning',
              type: AppBadgeType.warning,
              filled: false,
            ),
            AppBadge(text: 'Error', type: AppBadgeType.error, filled: false),
          ],
        ),

        SizedBox(height: 16),

        // Badges with Icons
        Text('Badges with Icons'),
        SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            AppBadge(text: 'New', icon: Icons.star, type: AppBadgeType.primary),
            AppBadge(
              text: 'Warning',
              icon: Icons.warning,
              type: AppBadgeType.warning,
            ),
            AppBadge(
              text: 'Error',
              icon: Icons.error,
              type: AppBadgeType.error,
            ),
          ],
        ),
      ],
    );
  }
}

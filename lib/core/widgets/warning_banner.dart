import 'package:flutter/material.dart';
import '../theme/app_colors.dart';

enum WarningLevel { info, warning, critical }

/// A banner widget for displaying spare parts warnings and alerts
class WarningBanner extends StatelessWidget {
  final String message;
  final WarningLevel level;
  final VoidCallback? onTap;

  const WarningBanner({
    super.key,
    required this.message,
    required this.level,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
        decoration: BoxDecoration(
          color: _getBackgroundColor(),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(_getIcon(), color: _getIconColor(), size: 24),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: _getTextColor(),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            if (onTap != null)
              Icon(Icons.arrow_forward_ios, color: _getIconColor(), size: 16),
          ],
        ),
      ),
    );
  }

  Color _getBackgroundColor() {
    switch (level) {
      case WarningLevel.info:
        return AppColors.info.withAlpha(25);
      case WarningLevel.warning:
        return AppColors.warning.withAlpha(25);
      case WarningLevel.critical:
        return AppColors.error.withAlpha(25);
    }
  }

  Color _getIconColor() {
    switch (level) {
      case WarningLevel.info:
        return AppColors.info;
      case WarningLevel.warning:
        return AppColors.warning;
      case WarningLevel.critical:
        return AppColors.error;
    }
  }

  Color _getTextColor() {
    switch (level) {
      case WarningLevel.info:
        return AppColors.textPrimary;
      case WarningLevel.warning:
        return AppColors.textPrimary;
      case WarningLevel.critical:
        return AppColors.error;
    }
  }

  IconData _getIcon() {
    switch (level) {
      case WarningLevel.info:
        return Icons.info_outline;
      case WarningLevel.warning:
        return Icons.warning_amber_outlined;
      case WarningLevel.critical:
        return Icons.error_outline;
    }
  }
}

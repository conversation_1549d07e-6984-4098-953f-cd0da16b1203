import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

/// Core shimmer colors and constants
class ShimmerConstants {
  // Base color for shimmer effect (light gray)
  static final Color baseColor = Colors.grey[300]!;

  // Highlight color for shimmer effect (lighter gray)
  static final Color highlightColor = Colors.grey[100]!;

  // Base color for shimmer effect on colored backgrounds (semi-transparent white)
  static final Color lightBaseColor = Colors.white.withAlpha(
    77,
  ); // 0.3 * 255 = 77

  // Highlight color for shimmer effect on colored backgrounds (more opaque white)
  static final Color lightHighlightColor = Colors.white.withAlpha(
    128,
  ); // 0.5 * 255 = 128

  // Standard border radius for shimmer containers
  static const double borderRadius = 4.0;

  // Standard border radius for shimmer cards
  static const double cardBorderRadius = 12.0;

  // Private constructor to prevent instantiation
  ShimmerConstants._();
}

/// Base shimmer widget that wraps content with shimmer effect
class ShimmerWrapper extends StatelessWidget {
  final Widget child;
  final bool useLightColors;

  const ShimmerWrapper({
    super.key,
    required this.child,
    this.useLightColors = false,
  });

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: useLightColors
          ? ShimmerConstants.lightBaseColor
          : ShimmerConstants.baseColor,
      highlightColor: useLightColors
          ? ShimmerConstants.lightHighlightColor
          : ShimmerConstants.highlightColor,
      child: child,
    );
  }
}

/// Basic shimmer container with customizable dimensions and shape
class ShimmerContainer extends StatelessWidget {
  final double width;
  final double height;
  final BorderRadius? borderRadius;
  final BoxShape shape;
  final EdgeInsets? margin;

  const ShimmerContainer({
    super.key,
    required this.width,
    required this.height,
    this.borderRadius,
    this.shape = BoxShape.rectangle,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      margin: margin,
      decoration: BoxDecoration(
        color: Colors.white,
        shape: shape,
        borderRadius: shape == BoxShape.rectangle
            ? (borderRadius ??
                  BorderRadius.circular(ShimmerConstants.borderRadius))
            : null,
      ),
    );
  }
}

/// Shimmer text placeholder with customizable dimensions
class ShimmerText extends StatelessWidget {
  final double width;
  final double height;
  final EdgeInsets? margin;

  const ShimmerText({
    super.key,
    required this.width,
    this.height = 16.0,
    this.margin,
  });

  /// Factory constructor for title-sized shimmer text
  factory ShimmerText.title({EdgeInsets? margin}) {
    return ShimmerText(width: 150, height: 20, margin: margin);
  }

  /// Factory constructor for subtitle-sized shimmer text
  factory ShimmerText.subtitle({EdgeInsets? margin}) {
    return ShimmerText(width: 250, height: 14, margin: margin);
  }

  /// Factory constructor for small-sized shimmer text
  factory ShimmerText.small({EdgeInsets? margin}) {
    return ShimmerText(width: 80, height: 12, margin: margin);
  }

  @override
  Widget build(BuildContext context) {
    return ShimmerContainer(width: width, height: height, margin: margin);
  }
}

/// Shimmer card with customizable dimensions
class ShimmerCard extends StatelessWidget {
  final double? width;
  final double? height;
  final EdgeInsets? margin;
  final EdgeInsets? padding;
  final Widget? child;
  final Color? borderColor;
  final BoxConstraints? constraints;

  const ShimmerCard({
    super.key,
    this.width,
    this.height,
    this.margin,
    this.padding,
    this.child,
    this.borderColor,
    this.constraints,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width ?? double.infinity,
      height: height,
      margin: margin,
      padding: padding,
      constraints: constraints,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(ShimmerConstants.cardBorderRadius),
        border: borderColor != null ? Border.all(color: borderColor!) : null,
      ),
      child: child,
    );
  }
}

/// Shimmer circle with customizable dimensions
class ShimmerCircle extends StatelessWidget {
  final double size;
  final EdgeInsets? margin;

  const ShimmerCircle({super.key, required this.size, this.margin});

  @override
  Widget build(BuildContext context) {
    return ShimmerContainer(
      width: size,
      height: size,
      shape: BoxShape.circle,
      margin: margin,
    );
  }
}

/// Shimmer divider with customizable dimensions
class ShimmerDivider extends StatelessWidget {
  final double width;
  final double height;
  final EdgeInsets? margin;

  const ShimmerDivider({
    super.key,
    this.width = double.infinity,
    this.height = 1.0,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return ShimmerContainer(width: width, height: height, margin: margin);
  }
}

/// Shimmer date range selector
class ShimmerDateRangeSelector extends StatelessWidget {
  const ShimmerDateRangeSelector({super.key});

  @override
  Widget build(BuildContext context) {
    return ShimmerWrapper(
      useLightColors: true,
      child: Container(
        height: 36,
        decoration: BoxDecoration(
          color: Colors.white.withAlpha(77),
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}

/// Shimmer log level selector
class ShimmerLogLevelSelector extends StatelessWidget {
  const ShimmerLogLevelSelector({super.key});

  @override
  Widget build(BuildContext context) {
    return ShimmerWrapper(
      useLightColors: true,
      child: Container(
        height: 36,
        decoration: BoxDecoration(
          color: Colors.white.withAlpha(77),
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}

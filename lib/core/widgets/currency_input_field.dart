import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// A reusable input field for currency values with Rupiah formatting
class CurrencyInputField extends StatelessWidget {
  /// Controller for the text field
  final TextEditingController controller;

  /// Label text to display
  final String label;

  /// Whether the field is required
  final bool isRequired;

  /// Whether to allow decimal values
  final bool allowDecimal;

  /// Whether to allow negative values
  final bool allowNegative;

  /// Text input action (e.g., next, done)
  final TextInputAction textInputAction;

  /// Focus node for this field
  final FocusNode? focusNode;

  /// Focus node to move to when done
  final FocusNode? nextFocusNode;

  /// Callback when value changes
  final Function(String)? onChanged;

  /// Validator function
  final String? Function(String?)? validator;

  /// Hint text to display when empty
  final String? hintText;

  /// Helper text to display below the field
  final String? helperText;

  const CurrencyInputField({
    super.key,
    required this.controller,
    required this.label,
    this.isRequired = false,
    this.allowDecimal = false,
    this.allowNegative = true,
    this.textInputAction = TextInputAction.next,
    this.focusNode,
    this.nextFocusNode,
    this.onChanged,
    this.validator,
    this.hintText,
    this.helperText,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: TextFormField(
        controller: controller,
        focusNode: focusNode,
        keyboardType: TextInputType.numberWithOptions(
          decimal: allowDecimal,
          signed: allowNegative,
        ),
        textInputAction: textInputAction,
        inputFormatters: [
          if (allowDecimal && allowNegative)
            FilteringTextInputFormatter.allow(RegExp(r'^-?\d*\.?\d{0,2}'))
          else if (allowDecimal)
            FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}'))
          else if (allowNegative)
            FilteringTextInputFormatter.allow(RegExp(r'^-?\d*'))
          else
            FilteringTextInputFormatter.digitsOnly,
        ],
        decoration: InputDecoration(
          labelText: '$label${isRequired ? ' *' : ''}',
          prefixText: 'Rp ',
          hintText: hintText,
          helperText: helperText,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10.0),
            borderSide: BorderSide(
              color: Theme.of(context).primaryColor.withAlpha(77),
              width: 1.0,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10.0),
            borderSide: BorderSide(
              color: Theme.of(context).primaryColor.withAlpha(77),
              width: 1.0,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10.0),
            borderSide: BorderSide(
              color: Theme.of(context).primaryColor,
              width: 1.5,
            ),
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 14,
          ),
          floatingLabelStyle: TextStyle(
            color: Theme.of(context).primaryColor,
            fontWeight: FontWeight.w500,
          ),
          prefixStyle: TextStyle(
            fontWeight: FontWeight.w500,
            color: Theme.of(context).textTheme.bodyLarge?.color,
          ),
        ),
        style: Theme.of(
          context,
        ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
        validator: (value) {
          if (isRequired && (value == null || value.isEmpty)) {
            return 'Please enter $label';
          }
          if (validator != null) {
            return validator!(value);
          }
          return null;
        },
        onChanged: onChanged,
        onFieldSubmitted: (value) {
          if (nextFocusNode != null) {
            FocusScope.of(context).requestFocus(nextFocusNode);
          }
        },
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../utils/snackbar_utils.dart';
import '../widgets/date_picker_field.dart';

/// A base form screen that provides common functionality for all form screens
abstract class BaseFormScreen<T> extends ConsumerStatefulWidget {
  /// The entity being edited, or null if creating a new entity
  final T? entity;

  const BaseFormScreen({super.key, this.entity});
}

/// Base state class for form screens
abstract class BaseFormScreenState<E, W extends BaseFormScreen<E>>
    extends ConsumerState<W> {
  /// Form key for validation
  final formKey = GlobalKey<FormState>();

  /// Selected date for the form
  late DateTime selectedDate;

  /// Whether we're editing an existing entity
  bool get isEditing => widget.entity != null;

  @override
  void initState() {
    super.initState();
    selectedDate = getInitialDate();
    initializeControllers();
  }

  @override
  void dispose() {
    disposeControllers();
    super.dispose();
  }

  /// Get the initial date for the form
  DateTime getInitialDate();

  /// Initialize controllers with data from entity if editing
  void initializeControllers();

  /// Dispose all controllers
  void disposeControllers();

  /// Get the title for the form
  String getFormTitle();

  /// Build the form fields
  List<Widget> buildFormFields();

  /// Submit the form
  Future<void> submitForm();

  /// Check if a record with the same date already exists
  Future<bool> checkDateExists();

  /// Build a section title with an icon
  Widget buildSectionTitle(String title, {IconData? icon}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon ?? Icons.info_outline,
                size: 18,
                color: Theme.of(context).primaryColor,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Divider(color: Colors.grey.withAlpha(30), thickness: 1),
        ],
      ),
    );
  }

  /// Build a read-only field for display purposes
  Widget buildReadOnlyField({
    required String label,
    required String value,
    IconData? icon,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: TextFormField(
        initialValue: value,
        readOnly: true,
        style: TextStyle(
          fontWeight: FontWeight.w500,
          color: Theme.of(context).textTheme.bodyLarge?.color,
        ),
        decoration: InputDecoration(
          labelText: label,
          labelStyle: TextStyle(
            color: Theme.of(context).primaryColor.withAlpha(204),
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8.0),
            borderSide: BorderSide(
              color: Colors.grey.withAlpha(77),
              width: 1.0,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8.0),
            borderSide: BorderSide(
              color: Colors.grey.withAlpha(77),
              width: 1.0,
            ),
          ),
          filled: true,
          fillColor: Colors.grey.withAlpha(10),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 14,
          ),
          prefixIcon: Icon(
            icon ?? Icons.info_outline,
            color: Theme.of(context).primaryColor,
            size: 18,
          ),
        ),
      ),
    );
  }

  /// Handle form submission with date validation
  Future<void> handleFormSubmission() async {
    if (formKey.currentState!.validate()) {
      // Show loading indicator
      if (mounted) {
        SnackbarUtils.showLoading(message: 'Saving...');
      }

      // Check if a record with the same date already exists
      final dateExists = await checkDateExists();

      // Check if the widget is still mounted before using BuildContext
      if (!mounted) return;

      // If a record with the same date already exists, show an error message and don't save
      if (dateExists) {
        SnackbarUtils.showError(
          'A record for this date already exists. Please choose a different date.',
        );
        return;
      }

      // Submit the form and wait for it to complete
      await submitForm();

      // Check if the widget is still mounted before using BuildContext
      if (mounted) {
        Navigator.of(context).pop();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Theme.of(context).primaryColor,
        title: Text(getFormTitle()),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: () async {
              await handleFormSubmission();
            },
            tooltip: isEditing ? 'Update' : 'Save',
          ),
        ],
      ),
      body: Form(
        key: formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              DatePickerField(
                selectedDate: selectedDate,
                onDateSelected: (date) {
                  setState(() {
                    selectedDate = date;
                  });
                },
                labelText: 'Date',
                isRequired: true,
              ),
              const SizedBox(height: 24),
              ...buildFormFields(),
              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

import '../services/sync/sync_operations.dart';
import '../theme/app_colors.dart';
import '../utils/date_helper.dart';

/// A widget that displays detailed sync status information
class SyncStatusIndicatorWidget extends ConsumerWidget {
  /// Whether to show a compact version of the widget
  final bool compact;

  /// Whether to show the next scheduled sync time
  final bool showNextSync;

  /// Whether to show a detailed status message
  final bool showDetailedStatus;

  /// Constructor
  const SyncStatusIndicatorWidget({
    super.key,
    this.compact = false,
    this.showNextSync = true,
    this.showDetailedStatus = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final syncState = ref.watch(syncStateProvider);
    final lastSyncTime = ref.watch(lastSyncTimeProvider);
    final nextScheduledSync = ref.watch(nextScheduledSyncProvider);

    return Card(
      elevation: compact ? 0 : 1,
      margin: EdgeInsets.zero,
      child: Padding(
        padding: EdgeInsets.all(compact ? 8.0 : 16.0),
        child: syncState.when(
          data: (status) => _buildSyncStatusContent(
            context,
            status,
            lastSyncTime,
            nextScheduledSync,
          ),
          loading: () => _buildSyncingContent(context),
          error: (error, _) => _buildErrorContent(context, error.toString()),
        ),
      ),
    );
  }

  Widget _buildSyncStatusContent(
    BuildContext context,
    String status,
    DateTime? lastSyncTime,
    DateTime? nextScheduledSync,
  ) {
    if (compact) {
      return _buildCompactStatusContent(context, status, lastSyncTime);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          children: [
            _getSyncStatusIcon(status),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Sync Status',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (showDetailedStatus) ...[
                    const SizedBox(height: 4),
                    _getSyncStatusText(status),
                  ],
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        const Divider(height: 1),
        const SizedBox(height: 12),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Last synchronized:',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
            ),
            Text(
              lastSyncTime != null ? _formatDateTime(lastSyncTime) : 'Never',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        if (showNextSync && nextScheduledSync != null) ...[
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Next scheduled sync:',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
              ),
              Text(
                _formatDateTime(nextScheduledSync),
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildCompactStatusContent(
    BuildContext context,
    String status,
    DateTime? lastSyncTime,
  ) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _getSyncStatusIcon(status, small: true),
        const SizedBox(width: 8),
        Flexible(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                _getSyncStatusLabel(status),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: _getSyncStatusColor(status),
                ),
              ),
              if (lastSyncTime != null) ...[
                Text(
                  'Last: ${_formatRelativeTime(lastSyncTime)}',
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSyncingContent(BuildContext context) {
    if (compact) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            'Syncing...',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppColors.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      );
    }

    return Row(
      children: [
        const SizedBox(
          width: 24,
          height: 24,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            color: AppColors.primary,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Syncing Data',
                style: Theme.of(
                  context,
                ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              Text(
                'Please wait while your data is being synchronized...',
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildErrorContent(BuildContext context, String error) {
    if (compact) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(Icons.error_outline, color: AppColors.error, size: 16),
          const SizedBox(width: 8),
          Flexible(
            child: Text(
              'Sync Error',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.error,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(Icons.error_outline, color: AppColors.error, size: 24),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                'Sync Error',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.error,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Text(
          error,
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: AppColors.error),
        ),
      ],
    );
  }

  Widget _getSyncStatusIcon(String status, {bool small = false}) {
    final double size = small ? 16.0 : 24.0;

    switch (status) {
      case 'synced':
        return Icon(Icons.cloud_done, color: AppColors.success, size: size);
      case 'synced with errors':
        return Icon(Icons.cloud_done, color: AppColors.warning, size: size);
      case 'pendingUpload':
        return Icon(Icons.cloud_upload, color: AppColors.warning, size: size);
      case 'conflict':
        return Icon(Icons.cloud_off, color: AppColors.error, size: size);
      default:
        return Icon(Icons.cloud_queue, color: Colors.grey, size: size);
    }
  }

  Widget _getSyncStatusText(String status) {
    switch (status) {
      case 'synced':
        return const Text(
          'All data is synced',
          style: TextStyle(color: AppColors.success),
        );
      case 'synced with errors':
        return const Text(
          'Synced with some errors',
          style: TextStyle(color: AppColors.warning),
        );
      case 'pendingUpload':
        return const Text(
          'Pending changes to upload',
          style: TextStyle(color: AppColors.warning),
        );
      case 'conflict':
        return const Text(
          'Sync conflicts detected',
          style: TextStyle(color: AppColors.error),
        );
      default:
        return const Text(
          'Unknown sync status',
          style: TextStyle(color: Colors.grey),
        );
    }
  }

  String _getSyncStatusLabel(String status) {
    switch (status) {
      case 'synced':
        return 'Synced';
      case 'synced with errors':
        return 'Partial Sync';
      case 'pendingUpload':
        return 'Pending Upload';
      case 'conflict':
        return 'Conflict';
      default:
        return 'Unknown';
    }
  }

  Color _getSyncStatusColor(String status) {
    switch (status) {
      case 'synced':
        return AppColors.success;
      case 'synced with errors':
      case 'pendingUpload':
        return AppColors.warning;
      case 'conflict':
        return AppColors.error;
      default:
        return Colors.grey;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return DateHelper.formatDateTimeForDisplay(dateTime.toLocal());
  }

  String _formatRelativeTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      final minutes = difference.inMinutes;
      return '$minutes min ago';
    } else if (difference.inDays < 1) {
      final hours = difference.inHours;
      return '$hours hr ago';
    } else if (difference.inDays < 7) {
      final days = difference.inDays;
      return '$days day${days > 1 ? 's' : ''} ago';
    } else {
      return DateFormat('MMM d').format(time);
    }
  }
}

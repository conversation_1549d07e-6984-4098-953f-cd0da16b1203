import 'package:flutter/material.dart';
import '../services/sync/sync_logger.dart';
import '../theme/app_colors.dart';

/// A reusable widget for selecting a log level filter
class LogLevelSelectorField extends StatelessWidget {
  /// The currently selected log level
  final LogLevel logLevel;

  /// Callback when a new log level is selected
  final Function(LogLevel) onLogLevelSelected;

  /// Label text to display
  final String? labelText;

  /// Icon to display
  final IconData icon;

  /// Border color
  final Color? borderColor;

  const LogLevelSelectorField({
    super.key,
    required this.logLevel,
    required this.onLogLevelSelected,
    this.labelText,
    this.icon = Icons.filter_list,
    this.borderColor,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => _selectLogLevel(context),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color:
                borderColor ??
                AppColors.primary.withAlpha(77), // 0.3 * 255 = ~77
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            if (labelText != null) ...[
              Text(labelText!, style: Theme.of(context).textTheme.bodySmall),
              const SizedBox(width: 8),
            ],
            Expanded(
              child: Text(
                'Show ${logLevel.label}+',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: _getLogLevelColor(logLevel),
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Icon(icon),
          ],
        ),
      ),
    );
  }

  Future<void> _selectLogLevel(BuildContext context) async {
    final selectedLevel = await showDialog<LogLevel>(
      context: context,
      builder: (BuildContext context) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(
              context,
            ).colorScheme.copyWith(primary: AppColors.primary),
          ),
          child: AlertDialog(
            title: const Text('Select Log Level'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: LogLevel.values.map((level) {
                return ListTile(
                  title: Text(
                    'Show ${level.label}+',
                    style: TextStyle(
                      color: _getLogLevelColor(level),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  onTap: () {
                    Navigator.of(context).pop(level);
                  },
                  selected: level == logLevel,
                  selectedTileColor: AppColors.primary.withAlpha(25),
                );
              }).toList(),
            ),
          ),
        );
      },
    );

    if (selectedLevel != null) {
      onLogLevelSelected(selectedLevel);
    }
  }

  Color _getLogLevelColor(LogLevel level) {
    switch (level) {
      case LogLevel.debug:
        return Colors.grey;
      case LogLevel.info:
        return Colors.blue;
      case LogLevel.warning:
        return Colors.orange;
      case LogLevel.error:
        return Colors.red;
    }
  }
}

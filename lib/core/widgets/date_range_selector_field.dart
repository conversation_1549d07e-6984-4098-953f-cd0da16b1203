import 'package:flutter/material.dart';
import '../theme/app_colors.dart';
import '../utils/date_helper.dart';

/// A reusable widget for selecting a date range
class DateRangeSelectorField extends StatelessWidget {
  /// The currently selected date range
  final DateTimeRange dateRange;

  /// Callback when a new date range is selected
  final Function(DateTimeRange) onDateRangeSelected;

  /// The earliest date that can be selected
  final DateTime? firstDate;

  /// The latest date that can be selected
  final DateTime? lastDate;

  /// Label text to display
  final String? labelText;

  /// Icon to display
  final IconData icon;

  /// Border color
  final Color? borderColor;

  const DateRangeSelectorField({
    super.key,
    required this.dateRange,
    required this.onDateRangeSelected,
    this.firstDate,
    this.lastDate,
    this.labelText,
    this.icon = Icons.calendar_month,
    this.borderColor,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => _selectDateRange(context),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color:
                borderColor ??
                AppColors.primary.withAlpha(77), // 0.3 * 255 = ~77
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            if (labelText != null) ...[
              Text(labelText!, style: Theme.of(context).textTheme.bodySmall),
              const SizedBox(width: 8),
            ],
            Expanded(
              child: Text(
                DateHelper.formatDateRange(dateRange.start, dateRange.end),
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Icon(icon),
          ],
        ),
      ),
    );
  }

  Future<void> _selectDateRange(BuildContext context) async {
    // Ensure we're using local dates for the picker
    final localStart = DateHelper.ensureLocal(dateRange.start);
    final localEnd = DateHelper.ensureLocal(dateRange.end);
    final localDateRange = DateTimeRange(start: localStart, end: localEnd);

    final newDateRange = await showDateRangePicker(
      context: context,
      firstDate: firstDate ?? DateTime(2020),
      lastDate: lastDate ?? DateTime(2030),
      initialDateRange: localDateRange,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(
              context,
            ).colorScheme.copyWith(primary: AppColors.primary),
          ),
          child: child!,
        );
      },
    );

    if (newDateRange != null) {
      // Preserve the exact dates selected by the user without timezone shifts
      // We'll normalize these in the parent widget to ensure consistent behavior
      onDateRangeSelected(newDateRange);
    }
  }
}

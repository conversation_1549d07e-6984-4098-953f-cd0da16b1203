import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// A reusable input field for integer values
class IntegerInputField extends StatelessWidget {
  /// Controller for the text field
  final TextEditingController controller;

  /// Label text to display
  final String label;

  /// Whether the field is required
  final bool isRequired;

  /// Whether to allow negative values
  final bool allowNegative;

  /// Text input action (e.g., next, done)
  final TextInputAction textInputAction;

  /// Focus node for this field
  final FocusNode? focusNode;

  /// Focus node to move to when done
  final FocusNode? nextFocusNode;

  /// Callback when value changes
  final Function(String)? onChanged;

  /// Validator function
  final String? Function(String?)? validator;

  /// Hint text to display when empty
  final String? hintText;

  /// Helper text to display below the field
  final String? helperText;

  /// Suffix text to display after the input
  final String? suffixText;

  /// Prefix icon to display before the input
  final IconData? prefixIcon;

  const IntegerInputField({
    super.key,
    required this.controller,
    required this.label,
    this.isRequired = false,
    this.allowNegative = false,
    this.textInputAction = TextInputAction.next,
    this.focusNode,
    this.nextFocusNode,
    this.onChanged,
    this.validator,
    this.hintText,
    this.helperText,
    this.suffixText,
    this.prefixIcon,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: TextFormField(
        controller: controller,
        focusNode: focusNode,
        keyboardType: TextInputType.numberWithOptions(
          decimal: false,
          signed: allowNegative,
        ),
        textInputAction: textInputAction,
        inputFormatters: [
          allowNegative
              ? FilteringTextInputFormatter.allow(RegExp(r'^-?\d*'))
              : FilteringTextInputFormatter.digitsOnly,
        ],
        decoration: InputDecoration(
          labelText: '$label${isRequired ? ' *' : ''}',
          hintText: hintText,
          helperText: helperText,
          suffixText: suffixText,
          prefixIcon: prefixIcon != null
              ? Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  child: Icon(
                    prefixIcon,
                    color: Theme.of(context).primaryColor.withAlpha(204),
                    size: 20,
                  ),
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10.0),
            borderSide: BorderSide(
              color: Theme.of(context).primaryColor.withAlpha(77),
              width: 1.0,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10.0),
            borderSide: BorderSide(
              color: Theme.of(context).primaryColor.withAlpha(77),
              width: 1.0,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10.0),
            borderSide: BorderSide(
              color: Theme.of(context).primaryColor,
              width: 1.5,
            ),
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 14,
          ),
          floatingLabelStyle: TextStyle(
            color: Theme.of(context).primaryColor,
            fontWeight: FontWeight.w500,
          ),
          suffixStyle: TextStyle(
            fontWeight: FontWeight.w500,
            color: Theme.of(context).textTheme.bodyLarge?.color,
          ),
        ),
        style: Theme.of(
          context,
        ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
        validator: (value) {
          if (isRequired && (value == null || value.isEmpty)) {
            return 'Please enter $label';
          }
          if (validator != null) {
            return validator!(value);
          }
          return null;
        },
        onChanged: onChanged,
        onFieldSubmitted: (value) {
          if (nextFocusNode != null) {
            FocusScope.of(context).requestFocus(nextFocusNode);
          }
        },
      ),
    );
  }
}

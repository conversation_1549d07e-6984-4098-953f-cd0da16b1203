import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../initialization/service_initializer.dart';
import '../services/app_lifecycle_service.dart';

/// A widget that handles app lifecycle events
///
/// This widget initializes the app lifecycle service and service initializer,
/// and handles app lifecycle events such as resumed, paused, etc.
class LifecycleHandler extends ConsumerStatefulWidget {
  /// The child widget
  final Widget child;

  /// Constructor
  const LifecycleHandler({super.key, required this.child});

  @override
  ConsumerState<LifecycleHandler> createState() => _LifecycleHandlerState();
}

class _LifecycleHandlerState extends ConsumerState<LifecycleHandler> {
  @override
  void initState() {
    super.initState();

    // Initialize services
    ref.read(serviceInitializerProvider).initializeWithPostFrameCallback();

    // Initialize app lifecycle service
    ref.read(appLifecycleServiceProvider).initialize();
  }

  @override
  void dispose() {
    // Dispose app lifecycle service
    ref.read(appLifecycleServiceProvider).dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}

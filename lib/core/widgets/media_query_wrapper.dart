import 'package:flutter/material.dart';
import '../theme/app_dimensions.dart';

/// A widget that wraps its child with a MediaQuery to control text scaling
///
/// This widget initializes AppDimensions with the current context and
/// applies text scaling limits to prevent layout issues.
class MediaQueryWrapper extends StatelessWidget {
  /// The child widget to wrap
  final Widget child;

  /// The minimum text scaling factor
  final double minScaleFactor;

  /// The maximum text scaling factor
  final double maxScaleFactor;

  /// Constructor
  const MediaQueryWrapper({
    super.key,
    required this.child,
    this.minScaleFactor = 0.85,
    this.maxScaleFactor = 1.1,
  });

  @override
  Widget build(BuildContext context) {
    // Initialize AppDimensions with the current context
    AppDimensions.init(context);

    // Apply text scaling limits
    return MediaQuery(
      // Limit text scaling to prevent layout issues
      data: MediaQuery.of(context).copyWith(
        textScaler: MediaQuery.textScalerOf(
          context,
        ).clamp(minScaleFactor: minScaleFactor, maxScaleFactor: maxScaleFactor),
      ),
      child: child,
    );
  }
}

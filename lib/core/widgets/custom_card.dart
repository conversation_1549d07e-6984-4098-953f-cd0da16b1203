import 'package:flutter/material.dart';

import '../theme/app_colors.dart';
import '../theme/app_dimensions.dart';

/// A custom card widget with consistent styling that adapts to screen size
class CustomCard extends StatelessWidget {
  /// The child widget to display inside the card
  final Widget child;

  /// Optional elevation for the card
  final double elevation;

  /// Optional margin for the card
  final EdgeInsetsGeometry? margin;

  /// Optional padding for the card
  final EdgeInsetsGeometry? padding;

  /// Optional border radius for the card
  final BorderRadius? borderRadius;

  /// Optional background color for the card
  final Color? backgroundColor;

  /// Optional border color for the card
  final Color? borderColor;

  const CustomCard({
    super.key,
    required this.child,
    this.elevation = 1.0,
    this.margin,
    this.padding,
    this.borderRadius,
    this.backgroundColor,
    this.borderColor,
  });

  @override
  Widget build(BuildContext context) {
    // Initialize dimensions
    AppDimensions.init(context);

    // Get responsive values
    final defaultRadius = AppDimensions.cardRadius;
    final defaultMargin = EdgeInsets.symmetric(
      vertical: AppDimensions.spacing8,
      horizontal: AppDimensions.spacing4,
    );
    final defaultPadding = EdgeInsets.all(AppDimensions.spacing16);

    return Card(
      elevation: elevation,
      margin: margin ?? defaultMargin,
      shape: RoundedRectangleBorder(
        borderRadius: borderRadius ?? BorderRadius.circular(defaultRadius),
        side: borderColor != null
            ? BorderSide(color: borderColor!, width: AppDimensions.borderWidth)
            : BorderSide.none,
      ),
      color: backgroundColor ?? Theme.of(context).cardColor,
      shadowColor: AppColors.shadow,
      child: padding != null
          ? Padding(padding: padding!, child: child)
          : Padding(padding: defaultPadding, child: child),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../theme/app_colors.dart';
import '../utils/date_helper.dart';

/// A reusable date picker field that displays a date and opens a date picker dialog when tapped
class DatePickerField extends StatelessWidget {
  /// The currently selected date
  final DateTime selectedDate;

  /// Callback when a new date is selected
  final Function(DateTime) onDateSelected;

  /// The earliest date that can be selected
  final DateTime? firstDate;

  /// The latest date that can be selected
  final DateTime? lastDate;

  /// Label text to display
  final String labelText;

  /// Whether the field is required
  final bool isRequired;

  /// Format to display the date (defaults to app's display format)
  final String? dateFormat;

  const DatePickerField({
    super.key,
    required this.selectedDate,
    required this.onDateSelected,
    this.firstDate,
    this.lastDate,
    this.labelText = 'Date',
    this.isRequired = false,
    this.dateFormat,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _selectDate(context),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.primary),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            const Icon(Icons.calendar_today, color: AppColors.primary),
            const SizedBox(width: 12),
            Text(
              '$labelText${isRequired ? ' *' : ''}',
              style: Theme.of(context).textTheme.labelMedium,
            ),
            const Spacer(),
            Text(
              dateFormat != null
                  ? DateFormat(dateFormat).format(selectedDate)
                  : DateHelper.formatForDisplay(selectedDate),
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    final pickedDate = await showDatePicker(
      context: context,
      initialDate: selectedDate,
      firstDate: firstDate ?? DateTime(2020),
      lastDate: lastDate ?? DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(
              context,
            ).colorScheme.copyWith(primary: AppColors.primary),
          ),
          child: child!,
        );
      },
    );

    if (pickedDate != null && pickedDate != selectedDate) {
      // Ensure the selected date is in UTC for consistent storage
      final pickedDateUtc = DateHelper.ensureUtc(pickedDate);
      onDateSelected(pickedDateUtc);
    }
  }
}

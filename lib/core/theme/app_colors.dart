import 'package:flutter/material.dart';

/// Design system color palette for the BidTrakr application
///
/// This class defines all colors used throughout the application,
/// ensuring consistency across the UI. Colors are organized by
/// their purpose and usage context.
class AppColors {
  // Primary colors - Brand identity
  /// Main brand color, used for primary actions, app bars, and key UI elements
  /// A calming blue-teal shade that provides a modern, professional look while being easier on the eyes
  static const Color primary = Color(0xFF3A87AD);

  /// Lighter variant of primary, used for hover states, backgrounds, and secondary elements
  /// A softer blue-teal that creates a pleasant visual hierarchy
  static const Color primaryLight = Color(0xFF5BA7C9);

  /// Darker variant of primary, used for pressed states and emphasis
  /// A deeper blue-teal for emphasis and interactive states
  static const Color primaryDark = Color(0xFF286A8A);

  // Primary color with opacity
  /// Returns the primary color with the specified opacity (0.0 to 1.0)
  static Color primaryWithOpacity(double opacity) =>
      primary.withAlpha((opacity * 255).round());

  // Secondary colors - Complementary palette
  /// Used for secondary actions and complementary UI elements
  /// A warm accent color that complements the primary blue-teal
  static const Color secondary = Color(0xFFD68C45);

  /// Lighter variant of secondary, used for hover states and backgrounds
  /// A softer warm tone for backgrounds and hover states
  static const Color secondaryLight = Color(0xFFE6A96A);

  /// Darker variant of secondary, used for pressed states and emphasis
  /// A deeper warm tone for emphasis and interactive states
  static const Color secondaryDark = Color(0xFFB57035);

  // Secondary color with opacity
  /// Returns the secondary color with the specified opacity (0.0 to 1.0)
  static Color secondaryWithOpacity(double opacity) =>
      secondary.withAlpha((opacity * 255).round());

  // Text colors - Typography hierarchy
  /// Main text color for body text and headings in light theme
  static const Color textPrimary = Color(0xFF1A1A1A);

  /// Secondary text color for less important information in light theme
  static const Color textSecondary = Color(0xFF616161);

  /// Text color for use on dark backgrounds
  static const Color textLight = Color(0xFFFFFFFF);

  /// Text color for disabled elements
  static const Color textDisabled = Color(0xFFBDBDBD);

  // Background colors - Surface hierarchy
  /// Main application background in light theme
  static const Color background = Color(0xFFF8F9FA);

  /// Surface color for cards and elevated elements in light theme
  static const Color surface = Color(0xFFFFFFFF);

  /// Background color for cards in light theme
  static const Color cardBackground = Color(0xFFFFFFFF);

  /// Color for dividers and separators in light theme
  static const Color divider = Color(0xFFE0E0E0);

  /// Color for borders in light theme
  static const Color border = Color(0xFFE0E0E0);

  /// Color for error states and messages
  /// A muted red that clearly indicates errors while being less jarring
  static const Color error = Color(0xFFE05D65);

  // Status colors - Feedback and state indicators
  /// Indicates successful actions or positive status
  /// A calming green that's easy on the eyes while clearly indicating success
  static const Color success = Color(0xFF4CAF7C);

  /// Indicates warnings or caution
  /// A warm amber that clearly indicates caution without being too harsh
  static const Color warning = Color(0xFFEDAB4A);

  /// Indicates informational messages
  /// A soft blue that aligns with the new primary color family
  static const Color info = Color(0xFF4A9CC9);

  // Subtle background variants for status indicators
  /// Light background for success states
  /// A very soft green background that complements the success color
  static const Color successLight = Color(0xFFE5F5EE);

  /// Light background for warning states
  /// A very soft amber background that complements the warning color
  static const Color warningLight = Color(0xFFF9F0E2);

  /// Light background for info states
  /// A very soft blue background that complements the info color
  static const Color infoLight = Color(0xFFE5F0F5);

  /// Light background for error states
  /// A very soft pink background that complements the error color
  static const Color errorLight = Color(0xFFF9E5E7);

  // Shadow colors - Elevation indicators
  /// Standard shadow color (10% opacity)
  static const Color shadow = Color(0x1A000000);

  /// Darker shadow color (15% opacity)
  static const Color shadowDark = Color(0x26000000);

  // Dark theme colors - Dark mode palette
  /// Main application background in dark theme
  static const Color darkBackground = Color(0xFF121212);

  /// Surface color for cards and elevated elements in dark theme
  static const Color darkSurface = Color(0xFF1E1E1E);

  /// Background color for cards in dark theme
  static const Color darkCardBackground = Color(0xFF2C2C2C);

  /// Main text color for body text and headings in dark theme
  static const Color darkTextPrimary = Color(0xFFF5F5F5);

  /// Secondary text color for less important information in dark theme
  static const Color darkTextSecondary = Color(0xFFB0B0B0);

  /// Color for dividers and separators in dark theme
  static const Color darkDivider = Color(0xFF424242);

  // Level colors - Driver achievement system
  /// Primary color for basic level UI elements
  static const Color basicLevelPrimary = Color(0xFF0DA802);

  /// Secondary color for basic level UI elements
  static const Color basicLevelSecondary = Color(0xFF078000);

  /// Primary color for silver level UI elements
  static const Color silverLevelPrimary = Color(0xFFA3A5A4);

  /// Secondary color for silver level UI elements
  static const Color silverLevelSecondary = Color(0xFF7D7F7E);

  /// Primary color for gold level UI elements
  static const Color goldLevelPrimary = Color(0xFFFFB31F);

  /// Secondary color for gold level UI elements
  static const Color goldLevelSecondary = Color(0xFFE09200);

  /// Primary color for platinum level UI elements
  static const Color platinumLevelPrimary = Color(0xFF5A6875);

  /// Secondary color for platinum level UI elements
  static const Color platinumLevelSecondary = Color(0xFF28323B);

  // Level gradient lists for backgrounds and cards
  /// Gradient colors for basic level backgrounds and cards
  static const List<Color> basicLevelGradient = [
    basicLevelPrimary,
    basicLevelSecondary,
  ];

  /// Gradient colors for silver level backgrounds and cards
  static const List<Color> silverLevelGradient = [
    silverLevelPrimary,
    silverLevelSecondary,
  ];

  /// Gradient colors for gold level backgrounds and cards
  static const List<Color> goldLevelGradient = [
    goldLevelPrimary,
    goldLevelSecondary,
  ];

  /// Gradient colors for platinum level backgrounds and cards
  static const List<Color> platinumLevelGradient = [
    platinumLevelPrimary,
    platinumLevelSecondary,
  ];

  // Level badge colors for achievement indicators
  /// Badge color for basic level achievements
  static const Color basicLevelBadge = Color(0xFF0DA802);

  /// Badge color for silver level achievements
  static const Color silverLevelBadge = Color(0xFFA3A5A4);

  /// Badge color for gold level achievements
  static const Color goldLevelBadge = Color(0xFFFFB31F);

  /// Badge color for platinum level achievements
  static const Color platinumLevelBadge = Color(0xFF28323B);

  // Helper methods for color manipulation
  /// Returns the specified color with the given opacity (0.0 to 1.0)
  static Color withOpacity(Color color, double opacity) =>
      color.withAlpha((opacity * 255).round());

  /// Lightens a color by the specified amount (0.0 to 1.0)
  ///
  /// Example: `AppColors.lighten(AppColors.primary, 0.2)` creates a 20% lighter version of the primary color
  static Color lighten(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    final lightness = (hsl.lightness + amount).clamp(0.0, 1.0);
    return hsl.withLightness(lightness).toColor();
  }

  /// Darkens a color by the specified amount (0.0 to 1.0)
  ///
  /// Example: `AppColors.darken(AppColors.primary, 0.2)` creates a 20% darker version of the primary color
  static Color darken(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    final lightness = (hsl.lightness - amount).clamp(0.0, 1.0);
    return hsl.withLightness(lightness).toColor();
  }

  /// Creates a subtle background color variant for any color
  ///
  /// Useful for creating background colors for status indicators or emphasis
  static Color createBackgroundVariant(Color color) {
    final hsl = HSLColor.fromColor(color);
    return hsl
        .withLightness((hsl.lightness + 0.45).clamp(0.0, 0.95))
        .withSaturation(0.1)
        .toColor();
  }
}

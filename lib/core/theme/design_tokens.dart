import 'package:flutter/material.dart';
import 'app_colors.dart';
import 'app_dimensions.dart';

/// Design tokens for the BidTrakr application
///
/// This class provides a centralized place for all design tokens used in the application.
/// Design tokens are the visual design atoms of the design system — specifically,
/// they are named entities that store visual design attributes.
class DesignTokens {
  // Private constructor to prevent instantiation
  DesignTokens._();

  /// Initialize design tokens based on screen size
  ///
  /// This method should be called before accessing any tokens,
  /// typically in the build method of the root widget.
  static void init(BuildContext context) {
    AppDimensions.init(context);
  }

  // COLOR TOKENS
  // These tokens define the color palette for the application

  /// Primary brand color
  static Color get colorPrimary => AppColors.primary;

  /// Light variant of primary color
  static Color get colorPrimaryLight => AppColors.primaryLight;

  /// Dark variant of primary color
  static Color get colorPrimaryDark => AppColors.primaryDark;

  /// Secondary brand color
  static Color get colorSecondary => AppColors.secondary;

  /// Light variant of secondary color
  static Color get colorSecondaryLight => AppColors.secondaryLight;

  /// Dark variant of secondary color
  static Color get colorSecondaryDark => AppColors.secondaryDark;

  /// Main text color
  static Color get colorTextPrimary => AppColors.textPrimary;

  /// Secondary text color
  static Color get colorTextSecondary => AppColors.textSecondary;

  /// Text color for use on dark backgrounds
  static Color get colorTextLight => AppColors.textLight;

  /// Text color for disabled elements
  static Color get colorTextDisabled => AppColors.textDisabled;

  /// Main application background color
  static Color get colorBackground => AppColors.background;

  /// Surface color for cards and elevated elements
  static Color get colorSurface => AppColors.surface;

  /// Background color for cards
  static Color get colorCardBackground => AppColors.cardBackground;

  /// Color for dividers and separators
  static Color get colorDivider => AppColors.divider;

  /// Color for borders
  static Color get colorBorder => AppColors.border;

  /// Color for error states and messages
  static Color get colorError => AppColors.error;

  /// Color for success states and messages
  static Color get colorSuccess => AppColors.success;

  /// Color for warning states and messages
  static Color get colorWarning => AppColors.warning;

  /// Color for informational states and messages
  static Color get colorInfo => AppColors.info;

  /// Light background for success states
  static Color get colorSuccessLight => AppColors.successLight;

  /// Light background for warning states
  static Color get colorWarningLight => AppColors.warningLight;

  /// Light background for info states
  static Color get colorInfoLight => AppColors.infoLight;

  /// Light background for error states
  static Color get colorErrorLight => AppColors.errorLight;

  /// Shadow color for elevation effects
  static Color get colorShadow => AppColors.shadow;

  // SPACING TOKENS
  // These tokens define the spacing system for the application

  /// Minimal spacing (2px)
  static double get spacingXxs => AppDimensions.spacing2;

  /// Extra small spacing (4px)
  static double get spacingXs => AppDimensions.spacing4;

  /// Small spacing (8px)
  static double get spacingSm => AppDimensions.spacing8;

  /// Medium-small spacing (12px)
  static double get spacingMd => AppDimensions.spacing12;

  /// Medium spacing (16px)
  static double get spacingLg => AppDimensions.spacing16;

  /// Medium-large spacing (20px)
  static double get spacingXl => AppDimensions.spacing20;

  /// Large spacing (24px)
  static double get spacingXxl => AppDimensions.spacing24;

  /// Extra large spacing (32px)
  static double get spacingXxxl => AppDimensions.spacing32;

  // COMPONENT TOKENS
  // These tokens define the dimensions for UI components

  /// Standard card corner radius
  static double get radiusCard => AppDimensions.cardRadius;

  /// Standard card elevation
  static double get elevationCard => AppDimensions.cardElevation;

  /// Standard button corner radius
  static double get radiusButton => AppDimensions.buttonRadius;

  /// Standard button height
  static double get heightButton => AppDimensions.buttonHeight;

  /// Small icon size
  static double get sizeIconSmall => AppDimensions.iconSizeSmall;

  /// Medium icon size
  static double get sizeIconMedium => AppDimensions.iconSizeMedium;

  /// Large icon size
  static double get sizeIconLarge => AppDimensions.iconSizeLarge;

  /// Standard border width
  static double get widthBorder => AppDimensions.borderWidth;

  /// Border width for focused elements
  static double get widthBorderFocus => AppDimensions.borderWidthFocus;

  /// Standard progress indicator height
  static double get heightProgressIndicator =>
      AppDimensions.progressIndicatorHeight;

  // PADDING TOKENS
  // These tokens define the standard paddings for different UI contexts

  /// Standard screen padding
  static EdgeInsets get paddingScreen => AppDimensions.screenPadding;

  /// Standard card content padding
  static EdgeInsets get paddingCard => AppDimensions.cardPadding;

  /// Standard list item padding
  static EdgeInsets get paddingListItem => AppDimensions.listItemPadding;

  /// Standard button padding
  static EdgeInsets get paddingButton => AppDimensions.buttonPadding;

  // TYPOGRAPHY TOKENS
  // These tokens define the text styles for the application
  // Note: These should be used with the TextStyleHelper class

  /// Font size for heading 1
  static double get fontSizeH1 => 20;

  /// Font size for heading 2
  static double get fontSizeH2 => 17;

  /// Font size for heading 3
  static double get fontSizeH3 => 15;

  /// Font size for body large
  static double get fontSizeBodyLarge => 14;

  /// Font size for body medium
  static double get fontSizeBodyMedium => 12;

  /// Font size for body small
  static double get fontSizeBodySmall => 10;

  /// Font size for button text
  static double get fontSizeButton => 12;

  /// Font size for label text
  static double get fontSizeLabel => 12;

  /// Font size for caption text
  static double get fontSizeCaption => 10;

  /// Font size for tab label
  static double get fontSizeTabLabel => 12;

  /// Font size for detail item
  static double get fontSizeDetailItem => 11;

  /// Font weight for regular text
  static FontWeight get fontWeightRegular => FontWeight.normal;

  /// Font weight for medium text
  static FontWeight get fontWeightMedium => FontWeight.w500;

  /// Font weight for bold text
  static FontWeight get fontWeightBold => FontWeight.bold;

  /// Line height for headings
  static double get lineHeightHeading => 1.2;

  /// Line height for body text
  static double get lineHeightBody => 1.4;

  /// Letter spacing for normal text
  static double get letterSpacingNormal => 0.0;

  /// Letter spacing for caption text
  static double get letterSpacingCaption => 0.3;
}

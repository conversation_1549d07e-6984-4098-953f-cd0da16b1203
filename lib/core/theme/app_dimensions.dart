import 'package:flutter/material.dart';

/// Design system spacing and dimensions for the BidTrakr application
///
/// This class provides a comprehensive set of responsive dimensions based on the device's screen size.
/// It defines a consistent spacing system, component dimensions, and helper methods for responsive layouts.
///
/// The dimensions are optimized for 1080 x 2408 pixels, 20:9 ratio, ~400 ppi density,
/// but will scale appropriately for different screen sizes.
class AppDimensions {
  // Private constructor to prevent instantiation
  AppDimensions._();

  // Device metrics with safe default values
  /// Current screen width in logical pixels
  static double screenWidth = 360;

  /// Current screen height in logical pixels
  static double screenHeight = 640;

  /// Horizontal block size (1% of screen width)
  static double blockSizeHorizontal = 3.6;

  /// Vertical block size (1% of screen height)
  static double blockSizeVertical = 6.4;

  /// Device pixel ratio (physical pixels per logical pixel)
  static double pixelRatio = 2.0;

  /// Initialize dimensions based on screen size
  ///
  /// This method should be called before accessing any dimensions,
  /// typically in the build method of the root widget.
  static void init(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    screenWidth = mediaQuery.size.width;
    screenHeight = mediaQuery.size.height;
    blockSizeHorizontal = screenWidth / 100;
    blockSizeVertical = screenHeight / 100;
    pixelRatio = mediaQuery.devicePixelRatio;
  }

  // SPACING SYSTEM
  // The spacing system is based on a 4-point grid, with each spacing unit
  // representing a multiple of 4 logical pixels. This ensures consistent
  // spacing throughout the application.

  /// Minimal spacing (2px) - For very tight spacing needs
  static double get spacing2 => blockSizeHorizontal * 0.5;

  /// Extra small spacing (4px) - For very close elements
  static double get spacing4 => blockSizeHorizontal * 1.0;

  /// Small spacing (8px) - For related elements
  static double get spacing8 => blockSizeHorizontal * 2.0;

  /// Medium-small spacing (12px) - For grouped elements
  static double get spacing12 => blockSizeHorizontal * 3.0;

  /// Medium spacing (16px) - Standard spacing for most elements
  static double get spacing16 => blockSizeHorizontal * 4.0;

  /// Medium-large spacing (20px) - For separating related sections
  static double get spacing20 => blockSizeHorizontal * 5.0;

  /// Large spacing (24px) - For separating distinct sections
  static double get spacing24 => blockSizeHorizontal * 6.0;

  /// Extra large spacing (32px) - For major section divisions
  static double get spacing32 => blockSizeHorizontal * 8.0;

  // COMPONENT DIMENSIONS
  // These dimensions define the standard sizes for UI components
  // to ensure consistency across the application.

  /// Standard card corner radius (12px)
  static double get cardRadius => 12.0;

  /// Standard card elevation (1px)
  static double get cardElevation => 1.0;

  /// Standard button corner radius (8px)
  static double get buttonRadius => 8.0;

  /// Standard button height (responsive to screen size)
  static double get buttonHeight => blockSizeVertical * 6.0;

  /// Small icon size (16px) - For compact UI elements
  static double get iconSizeSmall => 16.0;

  /// Medium icon size (20px) - For standard UI elements
  static double get iconSizeMedium => 20.0;

  /// Large icon size (24px) - For prominent UI elements
  static double get iconSizeLarge => 24.0;

  /// Standard border width (1px)
  static double get borderWidth => 1.0;

  /// Border width for focused elements (2px)
  static double get borderWidthFocus => 2.0;

  /// Standard progress indicator height (6px)
  static double get progressIndicatorHeight => 6.0;

  // STANDARD PADDINGS
  // These padding values define the standard spacing for different UI contexts

  /// Standard screen padding (16px on all sides)
  static EdgeInsets get screenPadding => EdgeInsets.all(spacing16);

  /// Standard card content padding (16px on all sides)
  static EdgeInsets get cardPadding => EdgeInsets.all(spacing16);

  /// Standard list item padding (16px horizontal, 12px vertical)
  static EdgeInsets get listItemPadding =>
      EdgeInsets.symmetric(horizontal: spacing16, vertical: spacing12);

  /// Standard button padding (16px horizontal, 12px vertical)
  static EdgeInsets get buttonPadding =>
      EdgeInsets.symmetric(horizontal: spacing16, vertical: spacing12);

  // RESPONSIVE LAYOUT HELPERS
  // These methods help create responsive layouts that adapt to different screen sizes

  /// Returns a width value that is a percentage of the screen width
  ///
  /// Example: `getResponsiveWidth(50)` returns 50% of the screen width
  static double getResponsiveWidth(double percentage) {
    return screenWidth * percentage / 100;
  }

  /// Returns a height value that is a percentage of the screen height
  ///
  /// Example: `getResponsiveHeight(30)` returns 30% of the screen height
  static double getResponsiveHeight(double percentage) {
    return screenHeight * percentage / 100;
  }

  /// Returns a font size that scales based on screen size and pixel density
  ///
  /// This ensures text is readable across different devices while maintaining
  /// the intended visual hierarchy.
  ///
  /// Example: `getResponsiveFontSize(14)` returns a scaled version of 14px
  static double getResponsiveFontSize(double size) {
    // Adjust font size based on screen width and pixel ratio with safety checks
    const double baseWidth = 1080;
    const double basePixelRatio = 400;

    // Ensure we don't divide by zero and have reasonable limits
    final double widthFactor = (screenWidth / baseWidth).clamp(0.5, 2.0);
    final double pixelFactor =
        (basePixelRatio / (pixelRatio > 0 ? pixelRatio : 2.0)).clamp(0.5, 2.0);

    final double scaleFactor = widthFactor * pixelFactor * 0.95;
    return size * scaleFactor;
  }

  /// Returns a responsive inset based on screen size
  ///
  /// Useful for creating responsive margins and paddings
  static EdgeInsets getResponsiveInsets({
    double left = 0,
    double top = 0,
    double right = 0,
    double bottom = 0,
  }) {
    return EdgeInsets.only(
      left: left * blockSizeHorizontal,
      top: top * blockSizeVertical,
      right: right * blockSizeHorizontal,
      bottom: bottom * blockSizeVertical,
    );
  }
}

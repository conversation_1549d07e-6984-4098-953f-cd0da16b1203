import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// A singleton class for managing Supabase configuration and initialization
class SupabaseConfig {
  // Get Supabase URL from .env file or use fallback
  static String get supabaseUrl =>
      dotenv.env['SUPABASE_URL'] ?? 'https://oztwzpgsdvrzczmrvfvx.supabase.co';

  // Get Supabase anon key from .env file or use fallback
  static String get supabaseAnonKey =>
      dotenv.env['SUPABASE_ANON_KEY'] ??
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im96dHd6cGdzZHZyemN6bXJ2ZnZ4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMyMjIzNTEsImV4cCI6MjA2ODc5ODM1MX0.D9X0m7wAkONolSvwj5mmdINiWETnDfd-wVkABdan9Ok';

  // Singleton instance
  static final SupabaseConfig _instance = SupabaseConfig._internal();

  // Factory constructor to return the same instance
  factory SupabaseConfig() {
    return _instance;
  }

  // Private constructor
  SupabaseConfig._internal();

  // Flag to track initialization status
  bool _initialized = false;

  // Property to check if Supabase is initialized
  bool get isInitialized => _initialized;

  // Getter for Supabase client
  SupabaseClient get client => Supabase.instance.client;

  // Initialize Supabase
  Future<void> initialize() async {
    if (_initialized) {
      debugPrint('Supabase already initialized');
      return;
    }

    try {
      // Get the URL and key, and extract the domain for testing
      final url = supabaseUrl;
      final key = supabaseAnonKey;

      // Extract the domain from the URL (remove https:// if present)
      final String domain = url
          .replaceAll('https://', '')
          .replaceAll('http://', '');

      debugPrint('Initializing Supabase with URL: $url');

      // Log initialization details for debugging
      debugPrint('Build mode: Release=$kReleaseMode, Debug=$kDebugMode');
      debugPrint('Supabase URL: $url');
      debugPrint('Supabase domain for testing: $domain');
      debugPrint('Supabase Key: ${key.substring(0, 10)}...');

      // Try to initialize Supabase with a timeout
      bool initSuccess = false;

      // First attempt with standard configuration
      try {
        // Add a small delay to ensure network is ready
        await Future.delayed(const Duration(milliseconds: 500));

        // Test network connectivity to Supabase domain before initializing
        final httpClient = HttpClient();
        httpClient.connectionTimeout = const Duration(seconds: 10);

        try {
          debugPrint('Testing connection to Supabase domain: $domain');
          final request = await httpClient.getUrl(Uri.parse('https://$domain'));
          final response = await request.close();
          debugPrint('Connection test result: ${response.statusCode}');
          await response.drain();
        } catch (connectionError) {
          debugPrint('Connection test failed: $connectionError');
          // Continue anyway, as the actual Supabase initialization might still work
        } finally {
          httpClient.close();
        }

        // Initialize Supabase with standard configuration
        await Supabase.initialize(
          url: url, // Use the original URL
          anonKey: key,
          debug: kDebugMode,
        );
        initSuccess = true;
        debugPrint('First initialization attempt succeeded');
      } catch (initError) {
        debugPrint('First initialization attempt failed: $initError');

        // If first attempt fails, try again with a different configuration
        if (!initSuccess) {
          try {
            // Wait a bit before retrying
            await Future.delayed(const Duration(seconds: 2));

            // Try with a different configuration
            await Supabase.initialize(
              url: url, // Use the original URL
              anonKey: key,
              debug: false,
            );
            initSuccess = true;
            debugPrint('Second initialization attempt succeeded');
          } catch (retryError) {
            debugPrint('Second initialization attempt failed: $retryError');

            // Last attempt with minimal configuration
            try {
              await Future.delayed(const Duration(seconds: 3));
              await Supabase.initialize(url: url, anonKey: key, debug: false);
              initSuccess = true;
              debugPrint('Third initialization attempt succeeded');
            } catch (lastError) {
              debugPrint('All initialization attempts failed: $lastError');
              rethrow;
            }
          }
        }
      }

      _initialized = true;
      debugPrint('Supabase initialized successfully');
    } catch (e) {
      debugPrint('Error initializing Supabase: $e');
      rethrow;
    }
  }

  // Reset session (for logout)
  Future<void> resetSession() async {
    if (!_initialized) {
      debugPrint('Supabase not initialized');
      return;
    }

    await client.auth.signOut();
    debugPrint('Supabase session reset');
  }
}

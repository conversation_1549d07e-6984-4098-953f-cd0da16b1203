import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:drift/drift.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';

import '../config/supabase_config.dart';

/// A class responsible for initializing the app
///
/// This class handles all initialization tasks that need to be performed
/// before the app starts, such as loading environment variables, setting up
/// the database, checking connectivity, and initializing Supabase.
class AppInitializer {
  /// Initialize the app
  ///
  /// This method performs all necessary initialization tasks and returns
  /// the database path that will be used by the app.
  static Future<String> initialize() async {
    // Ensure Flutter binding is initialized
    WidgetsFlutterBinding.ensureInitialized();

    // Load environment variables
    await _loadEnvironmentVariables();

    // Configure database options
    _configureDatabaseOptions();

    // Get database path
    final dbPath = await _setupDatabase();

    // Check connectivity and initialize Supabase if connected
    await _initializeSupabase();

    return dbPath;
  }

  /// Load environment variables from .env file
  static Future<void> _loadEnvironmentVariables() async {
    try {
      // Try to load environment variables with multiple attempts
      bool loaded = false;

      // First attempt - standard loading
      try {
        await dotenv.load(fileName: '.env');
        loaded = true;
        debugPrint(
          'Environment variables loaded successfully on first attempt',
        );
      } catch (e) {
        debugPrint('First attempt to load environment variables failed: $e');
      }

      // Second attempt - try with optional flag
      if (!loaded) {
        try {
          await dotenv.load(fileName: '.env', isOptional: true);
          loaded = true;
          debugPrint(
            'Environment variables loaded successfully on second attempt',
          );
        } catch (e) {
          debugPrint('Second attempt to load environment variables failed: $e');
        }
      }

      // Log the loaded variables for debugging (only in debug mode)
      if (loaded) {
        debugPrint('SUPABASE_URL: ${dotenv.env['SUPABASE_URL'] ?? 'not set'}');
        debugPrint(
          'SUPABASE_ANON_KEY: ${dotenv.env['SUPABASE_ANON_KEY']?.substring(0, 10) ?? 'not set'}...',
        );
      } else {
        debugPrint(
          'Failed to load environment variables, will use fallback values',
        );
      }
    } catch (e) {
      debugPrint('Failed to load environment variables: $e');
      // Continue without environment variables
    }
  }

  /// Configure database options
  static void _configureDatabaseOptions() {
    // Suppress the multiple database instances warning
    driftRuntimeOptions.dontWarnAboutMultipleDatabases = true;
    debugPrint('Database options configured');
  }

  /// Set up the database and return the database path
  static Future<String> _setupDatabase() async {
    final appDir = await getApplicationDocumentsDirectory();
    final dbPath = join(appDir.path, 'BidTrakr.db');
    debugPrint('Database path: $dbPath');
    return dbPath;
  }

  /// Check connectivity and initialize Supabase if connected
  static Future<bool> _initializeSupabase() async {
    try {
      // Check connectivity
      final connectivity = Connectivity();
      final connectivityResults = await connectivity.checkConnectivity();
      final hasInternet =
          connectivityResults.isNotEmpty &&
          !connectivityResults.contains(ConnectivityResult.none);

      if (hasInternet) {
        await SupabaseConfig().initialize();
        debugPrint('Supabase initialized successfully');
        return true;
      } else {
        debugPrint('No internet connection, skipping Supabase initialization');
        return false;
      }
    } catch (e) {
      debugPrint('Failed to initialize Supabase: $e');
      return false;
    }
  }
}

import '../version/version_info.dart';

/// Application-wide constants
class AppConstants {
  // App information
  static const String appName = 'bidtrakr';
  static String get appVersion => VersionInfo.version;
  static String get appVersionDisplay => VersionInfo.versionDisplay;
  static const String appDescription =
      'Drivers Performance, Income Tracking and Spare Parts Monitoring App';

  // Page titles
  static const String incomeTitle = 'Income Tracking';
  static const String ordersTitle = 'Order Management';
  static const String performanceTitle = 'Performance Metrics';
  static const String levelTitle = 'Driver Level';
  static const String sparePartsTitle = 'Spare Parts';

  // Driver levels
  static const String levelBasic = 'Basic';
  static const String levelSilver = 'Silver';
  static const String levelGold = 'Gold';
  static const String levelPlatinum = 'Platinum';

  // Warning thresholds
  static const double mileageWarningThreshold = 0.9; // 90% of mileage limit

  // Date formats
  static const String dateFormatDisplay = 'MMM dd, yyyy';
  static const String dateFormatInput = 'yyyy-MM-dd';

  // Default values
  static const int defaultActiveDays = 14; // For performance calculations
}

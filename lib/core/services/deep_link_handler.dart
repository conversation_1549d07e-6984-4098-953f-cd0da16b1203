import 'dart:async';
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Service to handle deep links for authentication callbacks
class DeepLinkHandler {
  static StreamSubscription<AuthState>? _authSubscription;

  /// Initialize deep link handling
  static Future<void> initialize() async {
    debugPrint('Initializing deep link handler');

    // Listen for auth state changes to handle email confirmations
    _authSubscription = Supabase.instance.client.auth.onAuthStateChange.listen(
      (AuthState data) {
        debugPrint('Auth state changed: ${data.event}');
        _handleAuthStateChange(data);
      },
      onError: (err) {
        debugPrint('Auth state change error: $err');
      },
    );
  }

  /// Handle auth state changes
  static void _handleAuthStateChange(AuthState data) {
    switch (data.event) {
      case AuthChangeEvent.initialSession:
        debugPrint('Initial session loaded: ${data.session?.user.email}');
        break;
      case AuthChangeEvent.signedIn:
        debugPrint('User signed in: ${data.session?.user.email}');
        break;
      case AuthChangeEvent.signedOut:
        debugPrint('User signed out');
        break;
      case AuthChangeEvent.tokenRefreshed:
        debugPrint('Token refreshed for user: ${data.session?.user.email}');
        break;
      case AuthChangeEvent.userUpdated:
        debugPrint('User updated: ${data.session?.user.email}');
        break;
      case AuthChangeEvent.passwordRecovery:
        debugPrint('Password recovery initiated');
        break;
      case AuthChangeEvent.mfaChallengeVerified:
        debugPrint('MFA challenge verified');
        break;
      // ignore: deprecated_member_use
      case AuthChangeEvent.userDeleted:
        debugPrint('User deleted');
        break;
    }
  }

  /// Dispose of deep link handling
  static void dispose() {
    _authSubscription?.cancel();
    _authSubscription = null;
  }

  /// Process deep link URL for auth callback
  static Future<void> processAuthCallback(String url) async {
    try {
      debugPrint('Processing auth callback URL: $url');
      final uri = Uri.parse(url);

      // Let Supabase handle the auth callback
      await Supabase.instance.client.auth.getSessionFromUrl(uri);
      debugPrint('Auth callback processed successfully');
    } catch (e) {
      debugPrint('Error processing auth callback: $e');
    }
  }
}

import 'package:dartz/dartz.dart';
import '../../errors/failures.dart';

/// Generic calculation service that handles different types of calculations
/// across the application in a consistent way.
///
/// This service uses the Strategy pattern to allow for different calculation
/// strategies for different entity types.
class CalculationService {
  final Map<Type, CalculationStrategy> _strategies = {};

  /// Register a calculation strategy for a specific entity type
  void registerStrategy<T>(CalculationStrategy<T> strategy) {
    _strategies[T] = strategy;
  }

  /// Get the calculation strategy for a specific entity type
  CalculationStrategy<T>? getStrategy<T>() {
    final strategy = _strategies[T];
    return strategy != null ? strategy as CalculationStrategy<T> : null;
  }

  /// Calculate derived values for an entity using the appropriate strategy
  ///
  /// Returns Either a Failure or the updated entity with calculated values
  Either<Failure, T> calculate<T>(T entity, {Map<String, dynamic>? params}) {
    try {
      final strategy = getStrategy<T>();
      if (strategy == null) {
        return Left(
          Failure.businessLogic(
            message:
                'No calculation strategy registered for type ${T.toString()}',
          ),
        );
      }
      return strategy.execute(entity, params: params);
    } catch (e) {
      return Left(
        Failure.businessLogic(message: 'Error during calculation: $e'),
      );
    }
  }
}

/// Interface for calculation strategies
///
/// Implementations of this interface define how to calculate derived values
/// for specific entity types.
abstract class CalculationStrategy<T> {
  /// Execute the calculation strategy on the given entity
  ///
  /// Returns Either a Failure or the updated entity with calculated values
  Either<Failure, T> execute(T entity, {Map<String, dynamic>? params});
}

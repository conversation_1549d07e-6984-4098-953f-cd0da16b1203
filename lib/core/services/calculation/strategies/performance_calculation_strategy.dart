import 'package:dartz/dartz.dart';

import '../../../../features/performance/domain/entities/performance.dart';
import '../../../errors/failures.dart';
import '../../../utils/calculations.dart';
import '../calculation_service.dart';

/// Calculation strategy for Performance entities
///
/// This strategy calculates:
/// - Average completed orders per day
/// - Average online hours per day
/// - Retention rate
class PerformanceCalculationStrategy implements CalculationStrategy<Performance> {
  @override
  Either<Failure, Performance> execute(Performance entity, {Map<String, dynamic>? params}) {
    try {
      // Extract parameters
      final int? totalCompletedOrders = params?['totalCompletedOrders'];

      // Calculate average completed orders per day
      final avgCompleted = totalCompletedOrders != null
          ? Calculations.calculateAvgCompleted(
              sumCompleted: totalCompletedOrders,
              activeDays: entity.activeDays,
            )
          : 0.0;

      // Calculate average online hours per day
      final avgOnline = Calculations.calculateAvgOnline(
        onlineHours: entity.onlineHours,
        activeDays: entity.activeDays,
      );

      // Calculate order retention
      final retention = Calculations.calculateRetention(
        avgOnline: avgOnline,
        avgCompleted: avgCompleted,
      );

      // Return updated performance with calculated values
      return Right(
        entity.copyWith(
          avgCompleted: avgCompleted,
          avgOnline: avgOnline,
          retention: retention,
        ),
      );
    } catch (e) {
      return Left(Failure.businessLogic(message: 'Error calculating performance metrics: $e'));
    }
  }
}

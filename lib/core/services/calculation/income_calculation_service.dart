import 'package:dartz/dartz.dart';

import '../../../features/income/domain/entities/income.dart';
import '../../errors/failures.dart';
import '../../utils/calculations.dart';
import 'calculation_service.dart';

/// Calculation strategy for Income entities
///
/// This strategy calculates:
/// - initialCapital
/// - finalResult
/// - mileage
/// - netIncome
class IncomeCalculationStrategy implements CalculationStrategy<Income> {
  @override
  Either<Failure, Income> execute(Income entity, {Map<String, dynamic>? params}) {
    try {
      // Calculate initial capital
      final initialCapital = Calculations.calculateInitialCapital(
        initialGopay: entity.initialGopay,
        initialBca: entity.initialBca,
        initialCash: entity.initialCash,
        initialOvo: entity.initialOvo,
        initialBri: entity.initialBri,
        initialRekpon: entity.initialRekpon,
      );
      
      // Calculate final result
      final finalResult = Calculations.calculateFinalResult(
        finalGopay: entity.finalGopay,
        finalBca: entity.finalBca,
        finalCash: entity.finalCash,
        finalOvo: entity.finalOvo,
        finalBri: entity.finalBri,
        finalRekpon: entity.finalRekpon,
      );
      
      // Calculate mileage
      final mileage = Calculations.calculateMileage(
        finalMileage: entity.finalMileage,
        initialMileage: entity.initialMileage,
      );
      
      // Calculate net income
      final netIncome = Calculations.calculateNetIncome(
        finalResult: finalResult,
        initialCapital: initialCapital,
      );
      
      // Return updated income with calculated values
      return Right(
        entity.copyWith(
          initialCapital: initialCapital,
          finalResult: finalResult,
          mileage: mileage,
          netIncome: netIncome,
        ),
      );
    } catch (e) {
      return Left(Failure.businessLogic(message: 'Error calculating income values: $e'));
    }
  }
}

/// Calculation strategy for Order entities
///
/// This strategy calculates:
/// - incomingOrder
/// - orderReceived
/// - bidAcceptance
/// - tripCompletion
/// - income
class OrderCalculationStrategy implements CalculationStrategy<dynamic> {
  @override
  Either<Failure, dynamic> execute(dynamic entity, {Map<String, dynamic>? params}) {
    try {
      // Calculate incoming order
      final incomingOrder = Calculations.calculateIncomingOrder(
        orderCompleted: entity.orderCompleted,
        orderMissed: entity.orderMissed,
        orderCanceled: entity.orderCanceled,
        cbsOrder: entity.cbsOrder,
      );
      
      // Calculate order received
      final orderReceived = Calculations.calculateOrderReceived(
        orderCompleted: entity.orderCompleted,
        orderCanceled: entity.orderCanceled,
      );
      
      // Calculate bid acceptance
      final bidAcceptance = Calculations.calculateBidAcceptance(
        orderReceived: orderReceived,
        cbsOrder: entity.cbsOrder,
        incomingOrder: incomingOrder,
      );
      
      // Calculate trip completion
      final tripCompletion = Calculations.calculateTripCompletion(
        orderCompleted: entity.orderCompleted,
        orderReceived: orderReceived,
      );
      
      // Calculate total income
      final income = Calculations.calculateTotalIncome(
        trip: entity.trip,
        bonus: entity.bonus,
        tips: entity.tips,
      );
      
      // Return updated order with calculated values
      return Right(
        entity.copyWith(
          incomingOrder: incomingOrder,
          orderReceived: orderReceived,
          bidAcceptance: bidAcceptance,
          tripCompletion: tripCompletion,
          income: income,
        ),
      );
    } catch (e) {
      return Left(Failure.businessLogic(message: 'Error calculating order values: $e'));
    }
  }
}

/// Enhanced calculation service for income-related calculations
class IncomeCalculationService {
  final CalculationService _calculationService;
  
  /// Constructor
  IncomeCalculationService(this._calculationService) {
    // Register strategies
    _calculationService.registerStrategy<Income>(IncomeCalculationStrategy());
  }
  
  /// Calculate derived values for an income entity
  Either<Failure, Income> calculateIncome(Income income) {
    return _calculationService.calculate<Income>(income);
  }
  
  /// Calculate net income from raw values
  Either<Failure, double> calculateNetIncome({
    required double initialGopay,
    required double initialBca,
    required double initialCash,
    required double initialOvo,
    required double initialBri,
    required double initialRekpon,
    required double finalGopay,
    required double finalBca,
    required double finalCash,
    required double finalOvo,
    required double finalBri,
    required double finalRekpon,
  }) {
    try {
      final initialCapital = Calculations.calculateInitialCapital(
        initialGopay: initialGopay,
        initialBca: initialBca,
        initialCash: initialCash,
        initialOvo: initialOvo,
        initialBri: initialBri,
        initialRekpon: initialRekpon,
      );
      
      final finalResult = Calculations.calculateFinalResult(
        finalGopay: finalGopay,
        finalBca: finalBca,
        finalCash: finalCash,
        finalOvo: finalOvo,
        finalBri: finalBri,
        finalRekpon: finalRekpon,
      );
      
      final netIncome = Calculations.calculateNetIncome(
        finalResult: finalResult,
        initialCapital: initialCapital,
      );
      
      return Right(netIncome);
    } catch (e) {
      return Left(Failure.businessLogic(message: 'Error calculating net income: $e'));
    }
  }
  
  /// Calculate mileage from initial and final mileage
  Either<Failure, int> calculateMileage({
    required int initialMileage,
    required int finalMileage,
  }) {
    try {
      final mileage = Calculations.calculateMileage(
        initialMileage: initialMileage,
        finalMileage: finalMileage,
      );
      
      return Right(mileage);
    } catch (e) {
      return Left(Failure.businessLogic(message: 'Error calculating mileage: $e'));
    }
  }
}

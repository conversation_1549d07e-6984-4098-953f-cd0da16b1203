import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as rp;
import 'package:supabase_flutter/supabase_flutter.dart' hide AuthState;

import 'auth_state.dart';

/// Manager for handling authentication state
class AuthStateManager {
  final rp.Ref _ref;
  final rp.StateProvider<AuthState> _authStateProvider;

  /// Constructor
  AuthStateManager(this._ref, this._authStateProvider);

  /// Get the current auth state
  AuthState get currentState => _ref.read(_authStateProvider);

  /// Set loading state
  void setLoading(bool isLoading) {
    final currentState = _ref.read(_authStateProvider);
    _ref.read(_authStateProvider.notifier).state = currentState.copyWith(
      isLoading: isLoading,
    );
  }

  /// Set error message
  void setError(String errorMessage) {
    final currentState = _ref.read(_authStateProvider);
    _ref.read(_authStateProvider.notifier).state = currentState.copyWith(
      errorMessage: errorMessage,
      isLoading: false,
    );
  }

  /// Clear error message
  void clearError() {
    final currentState = _ref.read(_authStateProvider);
    _ref.read(_authStateProvider.notifier).state = currentState.copyWith(
      errorMessage: null,
    );
  }

  /// Update auth state based on auth change event
  void updateAuthState(AuthChangeEvent event, User? user) {
    final currentState = _ref.read(_authStateProvider);

    switch (event) {
      case AuthChangeEvent.signedIn:
        _ref.read(_authStateProvider.notifier).state = AuthState(
          isAuthenticated: true,
          user: user,
          isLoading: false,
          isAuthOptional:
              currentState.isAuthOptional, // Preserve optional auth setting
        );
        break;
      case AuthChangeEvent.signedOut:
        _ref.read(_authStateProvider.notifier).state = AuthState(
          isAuthenticated: false,
          user: null,
          isLoading: false,
          isAuthOptional:
              currentState.isAuthOptional, // Preserve optional auth setting
        );
        break;
      default:
        // For other events, just update the user
        _ref.read(_authStateProvider.notifier).state = currentState.copyWith(
          user: user,
          isLoading: false,
        );
    }
  }

  /// Execute an authentication operation with proper state management
  Future<T> executeAuthOperation<T>(
    Future<T> Function() operation, {
    String? errorPrefix,
  }) async {
    try {
      // Set loading state
      setLoading(true);
      clearError();

      // Execute the operation
      final result = await operation();

      // Set loading state to false
      setLoading(false);

      return result;
    } catch (e) {
      // Set error state
      debugPrint('${errorPrefix ?? 'Auth error'}: $e');
      setError(e.toString());
      rethrow;
    }
  }
}

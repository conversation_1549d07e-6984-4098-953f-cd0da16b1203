import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';

import 'sync_logger.dart';
import 'sync_operations.dart';

/// Class responsible for monitoring connectivity and triggering sync operations
class ConnectivityMonitor {
  final Connectivity _connectivity;
  final SyncLogger _logger;

  /// Stream subscription for connectivity changes
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;

  /// Constructor
  ConnectivityMonitor(this._connectivity, this._logger);

  /// Initialize connectivity monitoring
  void setSyncOperations(SyncOperations syncOperations) {
    // We no longer need to store the syncOperations instance
    // since we don't trigger sync on connectivity changes
    _initConnectivityMonitoring();
  }

  /// Initialize connectivity monitoring
  void _initConnectivityMonitoring() {
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen((
      List<ConnectivityResult> results,
    ) {
      if (results.isNotEmpty && !results.contains(ConnectivityResult.none)) {
        _logger.addLogEntry(
          'Internet connection detected: ${results.toString()}',
        );
      } else {
        _logger.addLogEntry('Internet connection lost');
      }
    });
  }

  /// Check if connected to the internet
  Future<bool> isConnected() async {
    final results = await _connectivity.checkConnectivity();
    return results.isNotEmpty && !results.contains(ConnectivityResult.none);
  }

  /// Dispose resources
  void dispose() {
    _connectivitySubscription?.cancel();
  }
}

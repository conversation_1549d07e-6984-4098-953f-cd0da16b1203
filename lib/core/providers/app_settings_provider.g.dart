// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_settings_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$persistedDateRangeHash() =>
    r'a0c55879edab68a035d7f834cc593076129b6d5a';

/// Provider for the date range from app settings
///
/// Copied from [persistedDateRange].
@ProviderFor(persistedDateRange)
final persistedDateRangeProvider =
    AutoDisposeFutureProvider<DateTimeRange>.internal(
      persistedDateRange,
      name: r'persistedDateRangeProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$persistedDateRangeHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef PersistedDateRangeRef = AutoDisposeFutureProviderRef<DateTimeRange>;
String _$databaseHash() => r'4c149cb51d66171e4564eedabfdbda7362520928';

/// Provider for database instance
///
/// Copied from [database].
@ProviderFor(database)
final databaseProvider = Provider<AppDatabase>.internal(
  database,
  name: r'databaseProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$databaseHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef DatabaseRef = ProviderRef<AppDatabase>;
String _$databasePathHash() => r'a83bf371e59c7475a37d8b4b35aec70fa16db7b7';

/// Provider for database path
///
/// Copied from [databasePath].
@ProviderFor(databasePath)
final databasePathProvider = Provider<String>.internal(
  databasePath,
  name: r'databasePathProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$databasePathHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef DatabasePathRef = ProviderRef<String>;
String _$appSettingsNotifierHash() =>
    r'cbb9a6e1c1c95e0472d839b993e9a68634e77608';

/// Provider for app settings
///
/// Copied from [AppSettingsNotifier].
@ProviderFor(AppSettingsNotifier)
final appSettingsNotifierProvider =
    AutoDisposeAsyncNotifierProvider<AppSettingsNotifier, AppSetting>.internal(
      AppSettingsNotifier.new,
      name: r'appSettingsNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$appSettingsNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$AppSettingsNotifier = AutoDisposeAsyncNotifier<AppSetting>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'global_date_range_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$globalDateRangeHash() => r'c1af13ee16a6f661a9cf8a550c09562401021d8f';

/// Global provider for date range selection that can be used across the app
///
/// Copied from [GlobalDateRange].
@ProviderFor(GlobalDateRange)
final globalDateRangeProvider =
    AutoDisposeAsyncNotifierProvider<GlobalDateRange, DateTimeRange>.internal(
      GlobalDateRange.new,
      name: r'globalDateRangeProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$globalDateRangeHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$GlobalDateRange = AutoDisposeAsyncNotifier<DateTimeRange>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../theme/app_theme.dart';

/// Provider for light theme with context
final lightThemeProvider = Provider.family<ThemeData, BuildContext>((
  ref,
  context,
) {
  return AppTheme.lightTheme(context);
});

/// Provider for dark theme with context
final darkThemeProvider = Provider.family<ThemeData, BuildContext>((
  ref,
  context,
) {
  return AppTheme.darkTheme(context);
});

/// Theme builder that creates responsive themes
class ThemeBuilder extends ConsumerWidget {
  final Widget child;

  const ThemeBuilder({super.key, required this.child});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // This will be used by MaterialApp's builder to create the theme
    return child;
  }
}

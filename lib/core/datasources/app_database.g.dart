// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_database.dart';

// ignore_for_file: type=lint
class $IncomeTable extends Income with TableInfo<$IncomeTable, IncomeData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $IncomeTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _uuidMeta = const VerificationMeta('uuid');
  @override
  late final GeneratedColumn<String> uuid = GeneratedColumn<String>(
    'uuid',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: false,
    clientDefault: () => const Uuid().v4(),
  );
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
    'id',
    aliasedName,
    false,
    hasAutoIncrement: true,
    type: DriftSqlType.int,
    requiredDuringInsert: false,
    defaultConstraints: GeneratedColumn.constraintIsAlways(
      'PRIMARY KEY AUTOINCREMENT',
    ),
  );
  @override
  late final GeneratedColumnWithTypeConverter<DateTime, DateTime> date =
      GeneratedColumn<DateTime>(
        'date',
        aliasedName,
        false,
        type: DriftSqlType.dateTime,
        requiredDuringInsert: true,
      ).withConverter<DateTime>($IncomeTable.$converterdate);
  static const VerificationMeta _initialMileageMeta = const VerificationMeta(
    'initialMileage',
  );
  @override
  late final GeneratedColumn<int> initialMileage = GeneratedColumn<int>(
    'initial_mileage',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _finalMileageMeta = const VerificationMeta(
    'finalMileage',
  );
  @override
  late final GeneratedColumn<int> finalMileage = GeneratedColumn<int>(
    'final_mileage',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _initialGopayMeta = const VerificationMeta(
    'initialGopay',
  );
  @override
  late final GeneratedColumn<double> initialGopay = GeneratedColumn<double>(
    'initial_gopay',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _initialBcaMeta = const VerificationMeta(
    'initialBca',
  );
  @override
  late final GeneratedColumn<double> initialBca = GeneratedColumn<double>(
    'initial_bca',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _initialCashMeta = const VerificationMeta(
    'initialCash',
  );
  @override
  late final GeneratedColumn<double> initialCash = GeneratedColumn<double>(
    'initial_cash',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _initialOvoMeta = const VerificationMeta(
    'initialOvo',
  );
  @override
  late final GeneratedColumn<double> initialOvo = GeneratedColumn<double>(
    'initial_ovo',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _initialBriMeta = const VerificationMeta(
    'initialBri',
  );
  @override
  late final GeneratedColumn<double> initialBri = GeneratedColumn<double>(
    'initial_bri',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _initialRekponMeta = const VerificationMeta(
    'initialRekpon',
  );
  @override
  late final GeneratedColumn<double> initialRekpon = GeneratedColumn<double>(
    'initial_rekpon',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _finalGopayMeta = const VerificationMeta(
    'finalGopay',
  );
  @override
  late final GeneratedColumn<double> finalGopay = GeneratedColumn<double>(
    'final_gopay',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _finalBcaMeta = const VerificationMeta(
    'finalBca',
  );
  @override
  late final GeneratedColumn<double> finalBca = GeneratedColumn<double>(
    'final_bca',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _finalCashMeta = const VerificationMeta(
    'finalCash',
  );
  @override
  late final GeneratedColumn<double> finalCash = GeneratedColumn<double>(
    'final_cash',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _finalOvoMeta = const VerificationMeta(
    'finalOvo',
  );
  @override
  late final GeneratedColumn<double> finalOvo = GeneratedColumn<double>(
    'final_ovo',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _finalBriMeta = const VerificationMeta(
    'finalBri',
  );
  @override
  late final GeneratedColumn<double> finalBri = GeneratedColumn<double>(
    'final_bri',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _finalRekponMeta = const VerificationMeta(
    'finalRekpon',
  );
  @override
  late final GeneratedColumn<double> finalRekpon = GeneratedColumn<double>(
    'final_rekpon',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _initialCapitalMeta = const VerificationMeta(
    'initialCapital',
  );
  @override
  late final GeneratedColumn<double> initialCapital = GeneratedColumn<double>(
    'initial_capital',
    aliasedName,
    true,
    type: DriftSqlType.double,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _finalResultMeta = const VerificationMeta(
    'finalResult',
  );
  @override
  late final GeneratedColumn<double> finalResult = GeneratedColumn<double>(
    'final_result',
    aliasedName,
    true,
    type: DriftSqlType.double,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _mileageMeta = const VerificationMeta(
    'mileage',
  );
  @override
  late final GeneratedColumn<int> mileage = GeneratedColumn<int>(
    'mileage',
    aliasedName,
    true,
    type: DriftSqlType.int,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _netIncomeMeta = const VerificationMeta(
    'netIncome',
  );
  @override
  late final GeneratedColumn<double> netIncome = GeneratedColumn<double>(
    'net_income',
    aliasedName,
    true,
    type: DriftSqlType.double,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _createdAtMeta = const VerificationMeta(
    'createdAt',
  );
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
    'created_at',
    aliasedName,
    false,
    type: DriftSqlType.dateTime,
    requiredDuringInsert: false,
    clientDefault: () => DateTime.now().toUtc(),
  );
  static const VerificationMeta _updatedAtMeta = const VerificationMeta(
    'updatedAt',
  );
  @override
  late final GeneratedColumn<DateTime> updatedAt = GeneratedColumn<DateTime>(
    'updated_at',
    aliasedName,
    false,
    type: DriftSqlType.dateTime,
    requiredDuringInsert: false,
    clientDefault: () => DateTime.now().toUtc(),
  );
  static const VerificationMeta _deletedAtMeta = const VerificationMeta(
    'deletedAt',
  );
  @override
  late final GeneratedColumn<DateTime> deletedAt = GeneratedColumn<DateTime>(
    'deleted_at',
    aliasedName,
    true,
    type: DriftSqlType.dateTime,
    requiredDuringInsert: false,
  );
  @override
  late final GeneratedColumnWithTypeConverter<SyncStatus, String> syncStatus =
      GeneratedColumn<String>(
        'sync_status',
        aliasedName,
        false,
        type: DriftSqlType.string,
        requiredDuringInsert: false,
        defaultValue: const Constant('pendingUpload'),
      ).withConverter<SyncStatus>($IncomeTable.$convertersyncStatus);
  @override
  List<GeneratedColumn> get $columns => [
    uuid,
    id,
    date,
    initialMileage,
    finalMileage,
    initialGopay,
    initialBca,
    initialCash,
    initialOvo,
    initialBri,
    initialRekpon,
    finalGopay,
    finalBca,
    finalCash,
    finalOvo,
    finalBri,
    finalRekpon,
    initialCapital,
    finalResult,
    mileage,
    netIncome,
    createdAt,
    updatedAt,
    deletedAt,
    syncStatus,
  ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'income';
  @override
  VerificationContext validateIntegrity(
    Insertable<IncomeData> instance, {
    bool isInserting = false,
  }) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('uuid')) {
      context.handle(
        _uuidMeta,
        uuid.isAcceptableOrUnknown(data['uuid']!, _uuidMeta),
      );
    }
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('initial_mileage')) {
      context.handle(
        _initialMileageMeta,
        initialMileage.isAcceptableOrUnknown(
          data['initial_mileage']!,
          _initialMileageMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_initialMileageMeta);
    }
    if (data.containsKey('final_mileage')) {
      context.handle(
        _finalMileageMeta,
        finalMileage.isAcceptableOrUnknown(
          data['final_mileage']!,
          _finalMileageMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_finalMileageMeta);
    }
    if (data.containsKey('initial_gopay')) {
      context.handle(
        _initialGopayMeta,
        initialGopay.isAcceptableOrUnknown(
          data['initial_gopay']!,
          _initialGopayMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_initialGopayMeta);
    }
    if (data.containsKey('initial_bca')) {
      context.handle(
        _initialBcaMeta,
        initialBca.isAcceptableOrUnknown(data['initial_bca']!, _initialBcaMeta),
      );
    } else if (isInserting) {
      context.missing(_initialBcaMeta);
    }
    if (data.containsKey('initial_cash')) {
      context.handle(
        _initialCashMeta,
        initialCash.isAcceptableOrUnknown(
          data['initial_cash']!,
          _initialCashMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_initialCashMeta);
    }
    if (data.containsKey('initial_ovo')) {
      context.handle(
        _initialOvoMeta,
        initialOvo.isAcceptableOrUnknown(data['initial_ovo']!, _initialOvoMeta),
      );
    } else if (isInserting) {
      context.missing(_initialOvoMeta);
    }
    if (data.containsKey('initial_bri')) {
      context.handle(
        _initialBriMeta,
        initialBri.isAcceptableOrUnknown(data['initial_bri']!, _initialBriMeta),
      );
    } else if (isInserting) {
      context.missing(_initialBriMeta);
    }
    if (data.containsKey('initial_rekpon')) {
      context.handle(
        _initialRekponMeta,
        initialRekpon.isAcceptableOrUnknown(
          data['initial_rekpon']!,
          _initialRekponMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_initialRekponMeta);
    }
    if (data.containsKey('final_gopay')) {
      context.handle(
        _finalGopayMeta,
        finalGopay.isAcceptableOrUnknown(data['final_gopay']!, _finalGopayMeta),
      );
    } else if (isInserting) {
      context.missing(_finalGopayMeta);
    }
    if (data.containsKey('final_bca')) {
      context.handle(
        _finalBcaMeta,
        finalBca.isAcceptableOrUnknown(data['final_bca']!, _finalBcaMeta),
      );
    } else if (isInserting) {
      context.missing(_finalBcaMeta);
    }
    if (data.containsKey('final_cash')) {
      context.handle(
        _finalCashMeta,
        finalCash.isAcceptableOrUnknown(data['final_cash']!, _finalCashMeta),
      );
    } else if (isInserting) {
      context.missing(_finalCashMeta);
    }
    if (data.containsKey('final_ovo')) {
      context.handle(
        _finalOvoMeta,
        finalOvo.isAcceptableOrUnknown(data['final_ovo']!, _finalOvoMeta),
      );
    } else if (isInserting) {
      context.missing(_finalOvoMeta);
    }
    if (data.containsKey('final_bri')) {
      context.handle(
        _finalBriMeta,
        finalBri.isAcceptableOrUnknown(data['final_bri']!, _finalBriMeta),
      );
    } else if (isInserting) {
      context.missing(_finalBriMeta);
    }
    if (data.containsKey('final_rekpon')) {
      context.handle(
        _finalRekponMeta,
        finalRekpon.isAcceptableOrUnknown(
          data['final_rekpon']!,
          _finalRekponMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_finalRekponMeta);
    }
    if (data.containsKey('initial_capital')) {
      context.handle(
        _initialCapitalMeta,
        initialCapital.isAcceptableOrUnknown(
          data['initial_capital']!,
          _initialCapitalMeta,
        ),
      );
    }
    if (data.containsKey('final_result')) {
      context.handle(
        _finalResultMeta,
        finalResult.isAcceptableOrUnknown(
          data['final_result']!,
          _finalResultMeta,
        ),
      );
    }
    if (data.containsKey('mileage')) {
      context.handle(
        _mileageMeta,
        mileage.isAcceptableOrUnknown(data['mileage']!, _mileageMeta),
      );
    }
    if (data.containsKey('net_income')) {
      context.handle(
        _netIncomeMeta,
        netIncome.isAcceptableOrUnknown(data['net_income']!, _netIncomeMeta),
      );
    }
    if (data.containsKey('created_at')) {
      context.handle(
        _createdAtMeta,
        createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta),
      );
    }
    if (data.containsKey('updated_at')) {
      context.handle(
        _updatedAtMeta,
        updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta),
      );
    }
    if (data.containsKey('deleted_at')) {
      context.handle(
        _deletedAtMeta,
        deletedAt.isAcceptableOrUnknown(data['deleted_at']!, _deletedAtMeta),
      );
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  IncomeData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return IncomeData(
      uuid: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}uuid'],
      )!,
      id: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}id'],
      )!,
      date: $IncomeTable.$converterdate.fromSql(
        attachedDatabase.typeMapping.read(
          DriftSqlType.dateTime,
          data['${effectivePrefix}date'],
        )!,
      ),
      initialMileage: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}initial_mileage'],
      )!,
      finalMileage: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}final_mileage'],
      )!,
      initialGopay: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}initial_gopay'],
      )!,
      initialBca: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}initial_bca'],
      )!,
      initialCash: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}initial_cash'],
      )!,
      initialOvo: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}initial_ovo'],
      )!,
      initialBri: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}initial_bri'],
      )!,
      initialRekpon: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}initial_rekpon'],
      )!,
      finalGopay: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}final_gopay'],
      )!,
      finalBca: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}final_bca'],
      )!,
      finalCash: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}final_cash'],
      )!,
      finalOvo: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}final_ovo'],
      )!,
      finalBri: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}final_bri'],
      )!,
      finalRekpon: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}final_rekpon'],
      )!,
      initialCapital: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}initial_capital'],
      ),
      finalResult: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}final_result'],
      ),
      mileage: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}mileage'],
      ),
      netIncome: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}net_income'],
      ),
      createdAt: attachedDatabase.typeMapping.read(
        DriftSqlType.dateTime,
        data['${effectivePrefix}created_at'],
      )!,
      updatedAt: attachedDatabase.typeMapping.read(
        DriftSqlType.dateTime,
        data['${effectivePrefix}updated_at'],
      )!,
      deletedAt: attachedDatabase.typeMapping.read(
        DriftSqlType.dateTime,
        data['${effectivePrefix}deleted_at'],
      ),
      syncStatus: $IncomeTable.$convertersyncStatus.fromSql(
        attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}sync_status'],
        )!,
      ),
    );
  }

  @override
  $IncomeTable createAlias(String alias) {
    return $IncomeTable(attachedDatabase, alias);
  }

  static TypeConverter<DateTime, DateTime> $converterdate =
      const UtcDateTimeConverter();
  static TypeConverter<SyncStatus, String> $convertersyncStatus =
      const SyncStatusConverter();
}

class IncomeData extends DataClass implements Insertable<IncomeData> {
  final String uuid;
  final int id;
  final DateTime date;
  final int initialMileage;
  final int finalMileage;
  final double initialGopay;
  final double initialBca;
  final double initialCash;
  final double initialOvo;
  final double initialBri;
  final double initialRekpon;
  final double finalGopay;
  final double finalBca;
  final double finalCash;
  final double finalOvo;
  final double finalBri;
  final double finalRekpon;
  final double? initialCapital;
  final double? finalResult;
  final int? mileage;
  final double? netIncome;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;
  final SyncStatus syncStatus;
  const IncomeData({
    required this.uuid,
    required this.id,
    required this.date,
    required this.initialMileage,
    required this.finalMileage,
    required this.initialGopay,
    required this.initialBca,
    required this.initialCash,
    required this.initialOvo,
    required this.initialBri,
    required this.initialRekpon,
    required this.finalGopay,
    required this.finalBca,
    required this.finalCash,
    required this.finalOvo,
    required this.finalBri,
    required this.finalRekpon,
    this.initialCapital,
    this.finalResult,
    this.mileage,
    this.netIncome,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    required this.syncStatus,
  });
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['uuid'] = Variable<String>(uuid);
    map['id'] = Variable<int>(id);
    {
      map['date'] = Variable<DateTime>($IncomeTable.$converterdate.toSql(date));
    }
    map['initial_mileage'] = Variable<int>(initialMileage);
    map['final_mileage'] = Variable<int>(finalMileage);
    map['initial_gopay'] = Variable<double>(initialGopay);
    map['initial_bca'] = Variable<double>(initialBca);
    map['initial_cash'] = Variable<double>(initialCash);
    map['initial_ovo'] = Variable<double>(initialOvo);
    map['initial_bri'] = Variable<double>(initialBri);
    map['initial_rekpon'] = Variable<double>(initialRekpon);
    map['final_gopay'] = Variable<double>(finalGopay);
    map['final_bca'] = Variable<double>(finalBca);
    map['final_cash'] = Variable<double>(finalCash);
    map['final_ovo'] = Variable<double>(finalOvo);
    map['final_bri'] = Variable<double>(finalBri);
    map['final_rekpon'] = Variable<double>(finalRekpon);
    if (!nullToAbsent || initialCapital != null) {
      map['initial_capital'] = Variable<double>(initialCapital);
    }
    if (!nullToAbsent || finalResult != null) {
      map['final_result'] = Variable<double>(finalResult);
    }
    if (!nullToAbsent || mileage != null) {
      map['mileage'] = Variable<int>(mileage);
    }
    if (!nullToAbsent || netIncome != null) {
      map['net_income'] = Variable<double>(netIncome);
    }
    map['created_at'] = Variable<DateTime>(createdAt);
    map['updated_at'] = Variable<DateTime>(updatedAt);
    if (!nullToAbsent || deletedAt != null) {
      map['deleted_at'] = Variable<DateTime>(deletedAt);
    }
    {
      map['sync_status'] = Variable<String>(
        $IncomeTable.$convertersyncStatus.toSql(syncStatus),
      );
    }
    return map;
  }

  IncomeCompanion toCompanion(bool nullToAbsent) {
    return IncomeCompanion(
      uuid: Value(uuid),
      id: Value(id),
      date: Value(date),
      initialMileage: Value(initialMileage),
      finalMileage: Value(finalMileage),
      initialGopay: Value(initialGopay),
      initialBca: Value(initialBca),
      initialCash: Value(initialCash),
      initialOvo: Value(initialOvo),
      initialBri: Value(initialBri),
      initialRekpon: Value(initialRekpon),
      finalGopay: Value(finalGopay),
      finalBca: Value(finalBca),
      finalCash: Value(finalCash),
      finalOvo: Value(finalOvo),
      finalBri: Value(finalBri),
      finalRekpon: Value(finalRekpon),
      initialCapital: initialCapital == null && nullToAbsent
          ? const Value.absent()
          : Value(initialCapital),
      finalResult: finalResult == null && nullToAbsent
          ? const Value.absent()
          : Value(finalResult),
      mileage: mileage == null && nullToAbsent
          ? const Value.absent()
          : Value(mileage),
      netIncome: netIncome == null && nullToAbsent
          ? const Value.absent()
          : Value(netIncome),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
      deletedAt: deletedAt == null && nullToAbsent
          ? const Value.absent()
          : Value(deletedAt),
      syncStatus: Value(syncStatus),
    );
  }

  factory IncomeData.fromJson(
    Map<String, dynamic> json, {
    ValueSerializer? serializer,
  }) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return IncomeData(
      uuid: serializer.fromJson<String>(json['uuid']),
      id: serializer.fromJson<int>(json['id']),
      date: serializer.fromJson<DateTime>(json['date']),
      initialMileage: serializer.fromJson<int>(json['initialMileage']),
      finalMileage: serializer.fromJson<int>(json['finalMileage']),
      initialGopay: serializer.fromJson<double>(json['initialGopay']),
      initialBca: serializer.fromJson<double>(json['initialBca']),
      initialCash: serializer.fromJson<double>(json['initialCash']),
      initialOvo: serializer.fromJson<double>(json['initialOvo']),
      initialBri: serializer.fromJson<double>(json['initialBri']),
      initialRekpon: serializer.fromJson<double>(json['initialRekpon']),
      finalGopay: serializer.fromJson<double>(json['finalGopay']),
      finalBca: serializer.fromJson<double>(json['finalBca']),
      finalCash: serializer.fromJson<double>(json['finalCash']),
      finalOvo: serializer.fromJson<double>(json['finalOvo']),
      finalBri: serializer.fromJson<double>(json['finalBri']),
      finalRekpon: serializer.fromJson<double>(json['finalRekpon']),
      initialCapital: serializer.fromJson<double?>(json['initialCapital']),
      finalResult: serializer.fromJson<double?>(json['finalResult']),
      mileage: serializer.fromJson<int?>(json['mileage']),
      netIncome: serializer.fromJson<double?>(json['netIncome']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      updatedAt: serializer.fromJson<DateTime>(json['updatedAt']),
      deletedAt: serializer.fromJson<DateTime?>(json['deletedAt']),
      syncStatus: serializer.fromJson<SyncStatus>(json['syncStatus']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'uuid': serializer.toJson<String>(uuid),
      'id': serializer.toJson<int>(id),
      'date': serializer.toJson<DateTime>(date),
      'initialMileage': serializer.toJson<int>(initialMileage),
      'finalMileage': serializer.toJson<int>(finalMileage),
      'initialGopay': serializer.toJson<double>(initialGopay),
      'initialBca': serializer.toJson<double>(initialBca),
      'initialCash': serializer.toJson<double>(initialCash),
      'initialOvo': serializer.toJson<double>(initialOvo),
      'initialBri': serializer.toJson<double>(initialBri),
      'initialRekpon': serializer.toJson<double>(initialRekpon),
      'finalGopay': serializer.toJson<double>(finalGopay),
      'finalBca': serializer.toJson<double>(finalBca),
      'finalCash': serializer.toJson<double>(finalCash),
      'finalOvo': serializer.toJson<double>(finalOvo),
      'finalBri': serializer.toJson<double>(finalBri),
      'finalRekpon': serializer.toJson<double>(finalRekpon),
      'initialCapital': serializer.toJson<double?>(initialCapital),
      'finalResult': serializer.toJson<double?>(finalResult),
      'mileage': serializer.toJson<int?>(mileage),
      'netIncome': serializer.toJson<double?>(netIncome),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'updatedAt': serializer.toJson<DateTime>(updatedAt),
      'deletedAt': serializer.toJson<DateTime?>(deletedAt),
      'syncStatus': serializer.toJson<SyncStatus>(syncStatus),
    };
  }

  IncomeData copyWith({
    String? uuid,
    int? id,
    DateTime? date,
    int? initialMileage,
    int? finalMileage,
    double? initialGopay,
    double? initialBca,
    double? initialCash,
    double? initialOvo,
    double? initialBri,
    double? initialRekpon,
    double? finalGopay,
    double? finalBca,
    double? finalCash,
    double? finalOvo,
    double? finalBri,
    double? finalRekpon,
    Value<double?> initialCapital = const Value.absent(),
    Value<double?> finalResult = const Value.absent(),
    Value<int?> mileage = const Value.absent(),
    Value<double?> netIncome = const Value.absent(),
    DateTime? createdAt,
    DateTime? updatedAt,
    Value<DateTime?> deletedAt = const Value.absent(),
    SyncStatus? syncStatus,
  }) => IncomeData(
    uuid: uuid ?? this.uuid,
    id: id ?? this.id,
    date: date ?? this.date,
    initialMileage: initialMileage ?? this.initialMileage,
    finalMileage: finalMileage ?? this.finalMileage,
    initialGopay: initialGopay ?? this.initialGopay,
    initialBca: initialBca ?? this.initialBca,
    initialCash: initialCash ?? this.initialCash,
    initialOvo: initialOvo ?? this.initialOvo,
    initialBri: initialBri ?? this.initialBri,
    initialRekpon: initialRekpon ?? this.initialRekpon,
    finalGopay: finalGopay ?? this.finalGopay,
    finalBca: finalBca ?? this.finalBca,
    finalCash: finalCash ?? this.finalCash,
    finalOvo: finalOvo ?? this.finalOvo,
    finalBri: finalBri ?? this.finalBri,
    finalRekpon: finalRekpon ?? this.finalRekpon,
    initialCapital: initialCapital.present
        ? initialCapital.value
        : this.initialCapital,
    finalResult: finalResult.present ? finalResult.value : this.finalResult,
    mileage: mileage.present ? mileage.value : this.mileage,
    netIncome: netIncome.present ? netIncome.value : this.netIncome,
    createdAt: createdAt ?? this.createdAt,
    updatedAt: updatedAt ?? this.updatedAt,
    deletedAt: deletedAt.present ? deletedAt.value : this.deletedAt,
    syncStatus: syncStatus ?? this.syncStatus,
  );
  IncomeData copyWithCompanion(IncomeCompanion data) {
    return IncomeData(
      uuid: data.uuid.present ? data.uuid.value : this.uuid,
      id: data.id.present ? data.id.value : this.id,
      date: data.date.present ? data.date.value : this.date,
      initialMileage: data.initialMileage.present
          ? data.initialMileage.value
          : this.initialMileage,
      finalMileage: data.finalMileage.present
          ? data.finalMileage.value
          : this.finalMileage,
      initialGopay: data.initialGopay.present
          ? data.initialGopay.value
          : this.initialGopay,
      initialBca: data.initialBca.present
          ? data.initialBca.value
          : this.initialBca,
      initialCash: data.initialCash.present
          ? data.initialCash.value
          : this.initialCash,
      initialOvo: data.initialOvo.present
          ? data.initialOvo.value
          : this.initialOvo,
      initialBri: data.initialBri.present
          ? data.initialBri.value
          : this.initialBri,
      initialRekpon: data.initialRekpon.present
          ? data.initialRekpon.value
          : this.initialRekpon,
      finalGopay: data.finalGopay.present
          ? data.finalGopay.value
          : this.finalGopay,
      finalBca: data.finalBca.present ? data.finalBca.value : this.finalBca,
      finalCash: data.finalCash.present ? data.finalCash.value : this.finalCash,
      finalOvo: data.finalOvo.present ? data.finalOvo.value : this.finalOvo,
      finalBri: data.finalBri.present ? data.finalBri.value : this.finalBri,
      finalRekpon: data.finalRekpon.present
          ? data.finalRekpon.value
          : this.finalRekpon,
      initialCapital: data.initialCapital.present
          ? data.initialCapital.value
          : this.initialCapital,
      finalResult: data.finalResult.present
          ? data.finalResult.value
          : this.finalResult,
      mileage: data.mileage.present ? data.mileage.value : this.mileage,
      netIncome: data.netIncome.present ? data.netIncome.value : this.netIncome,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
      deletedAt: data.deletedAt.present ? data.deletedAt.value : this.deletedAt,
      syncStatus: data.syncStatus.present
          ? data.syncStatus.value
          : this.syncStatus,
    );
  }

  @override
  String toString() {
    return (StringBuffer('IncomeData(')
          ..write('uuid: $uuid, ')
          ..write('id: $id, ')
          ..write('date: $date, ')
          ..write('initialMileage: $initialMileage, ')
          ..write('finalMileage: $finalMileage, ')
          ..write('initialGopay: $initialGopay, ')
          ..write('initialBca: $initialBca, ')
          ..write('initialCash: $initialCash, ')
          ..write('initialOvo: $initialOvo, ')
          ..write('initialBri: $initialBri, ')
          ..write('initialRekpon: $initialRekpon, ')
          ..write('finalGopay: $finalGopay, ')
          ..write('finalBca: $finalBca, ')
          ..write('finalCash: $finalCash, ')
          ..write('finalOvo: $finalOvo, ')
          ..write('finalBri: $finalBri, ')
          ..write('finalRekpon: $finalRekpon, ')
          ..write('initialCapital: $initialCapital, ')
          ..write('finalResult: $finalResult, ')
          ..write('mileage: $mileage, ')
          ..write('netIncome: $netIncome, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('deletedAt: $deletedAt, ')
          ..write('syncStatus: $syncStatus')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hashAll([
    uuid,
    id,
    date,
    initialMileage,
    finalMileage,
    initialGopay,
    initialBca,
    initialCash,
    initialOvo,
    initialBri,
    initialRekpon,
    finalGopay,
    finalBca,
    finalCash,
    finalOvo,
    finalBri,
    finalRekpon,
    initialCapital,
    finalResult,
    mileage,
    netIncome,
    createdAt,
    updatedAt,
    deletedAt,
    syncStatus,
  ]);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is IncomeData &&
          other.uuid == this.uuid &&
          other.id == this.id &&
          other.date == this.date &&
          other.initialMileage == this.initialMileage &&
          other.finalMileage == this.finalMileage &&
          other.initialGopay == this.initialGopay &&
          other.initialBca == this.initialBca &&
          other.initialCash == this.initialCash &&
          other.initialOvo == this.initialOvo &&
          other.initialBri == this.initialBri &&
          other.initialRekpon == this.initialRekpon &&
          other.finalGopay == this.finalGopay &&
          other.finalBca == this.finalBca &&
          other.finalCash == this.finalCash &&
          other.finalOvo == this.finalOvo &&
          other.finalBri == this.finalBri &&
          other.finalRekpon == this.finalRekpon &&
          other.initialCapital == this.initialCapital &&
          other.finalResult == this.finalResult &&
          other.mileage == this.mileage &&
          other.netIncome == this.netIncome &&
          other.createdAt == this.createdAt &&
          other.updatedAt == this.updatedAt &&
          other.deletedAt == this.deletedAt &&
          other.syncStatus == this.syncStatus);
}

class IncomeCompanion extends UpdateCompanion<IncomeData> {
  final Value<String> uuid;
  final Value<int> id;
  final Value<DateTime> date;
  final Value<int> initialMileage;
  final Value<int> finalMileage;
  final Value<double> initialGopay;
  final Value<double> initialBca;
  final Value<double> initialCash;
  final Value<double> initialOvo;
  final Value<double> initialBri;
  final Value<double> initialRekpon;
  final Value<double> finalGopay;
  final Value<double> finalBca;
  final Value<double> finalCash;
  final Value<double> finalOvo;
  final Value<double> finalBri;
  final Value<double> finalRekpon;
  final Value<double?> initialCapital;
  final Value<double?> finalResult;
  final Value<int?> mileage;
  final Value<double?> netIncome;
  final Value<DateTime> createdAt;
  final Value<DateTime> updatedAt;
  final Value<DateTime?> deletedAt;
  final Value<SyncStatus> syncStatus;
  const IncomeCompanion({
    this.uuid = const Value.absent(),
    this.id = const Value.absent(),
    this.date = const Value.absent(),
    this.initialMileage = const Value.absent(),
    this.finalMileage = const Value.absent(),
    this.initialGopay = const Value.absent(),
    this.initialBca = const Value.absent(),
    this.initialCash = const Value.absent(),
    this.initialOvo = const Value.absent(),
    this.initialBri = const Value.absent(),
    this.initialRekpon = const Value.absent(),
    this.finalGopay = const Value.absent(),
    this.finalBca = const Value.absent(),
    this.finalCash = const Value.absent(),
    this.finalOvo = const Value.absent(),
    this.finalBri = const Value.absent(),
    this.finalRekpon = const Value.absent(),
    this.initialCapital = const Value.absent(),
    this.finalResult = const Value.absent(),
    this.mileage = const Value.absent(),
    this.netIncome = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.deletedAt = const Value.absent(),
    this.syncStatus = const Value.absent(),
  });
  IncomeCompanion.insert({
    this.uuid = const Value.absent(),
    this.id = const Value.absent(),
    required DateTime date,
    required int initialMileage,
    required int finalMileage,
    required double initialGopay,
    required double initialBca,
    required double initialCash,
    required double initialOvo,
    required double initialBri,
    required double initialRekpon,
    required double finalGopay,
    required double finalBca,
    required double finalCash,
    required double finalOvo,
    required double finalBri,
    required double finalRekpon,
    this.initialCapital = const Value.absent(),
    this.finalResult = const Value.absent(),
    this.mileage = const Value.absent(),
    this.netIncome = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.deletedAt = const Value.absent(),
    this.syncStatus = const Value.absent(),
  }) : date = Value(date),
       initialMileage = Value(initialMileage),
       finalMileage = Value(finalMileage),
       initialGopay = Value(initialGopay),
       initialBca = Value(initialBca),
       initialCash = Value(initialCash),
       initialOvo = Value(initialOvo),
       initialBri = Value(initialBri),
       initialRekpon = Value(initialRekpon),
       finalGopay = Value(finalGopay),
       finalBca = Value(finalBca),
       finalCash = Value(finalCash),
       finalOvo = Value(finalOvo),
       finalBri = Value(finalBri),
       finalRekpon = Value(finalRekpon);
  static Insertable<IncomeData> custom({
    Expression<String>? uuid,
    Expression<int>? id,
    Expression<DateTime>? date,
    Expression<int>? initialMileage,
    Expression<int>? finalMileage,
    Expression<double>? initialGopay,
    Expression<double>? initialBca,
    Expression<double>? initialCash,
    Expression<double>? initialOvo,
    Expression<double>? initialBri,
    Expression<double>? initialRekpon,
    Expression<double>? finalGopay,
    Expression<double>? finalBca,
    Expression<double>? finalCash,
    Expression<double>? finalOvo,
    Expression<double>? finalBri,
    Expression<double>? finalRekpon,
    Expression<double>? initialCapital,
    Expression<double>? finalResult,
    Expression<int>? mileage,
    Expression<double>? netIncome,
    Expression<DateTime>? createdAt,
    Expression<DateTime>? updatedAt,
    Expression<DateTime>? deletedAt,
    Expression<String>? syncStatus,
  }) {
    return RawValuesInsertable({
      if (uuid != null) 'uuid': uuid,
      if (id != null) 'id': id,
      if (date != null) 'date': date,
      if (initialMileage != null) 'initial_mileage': initialMileage,
      if (finalMileage != null) 'final_mileage': finalMileage,
      if (initialGopay != null) 'initial_gopay': initialGopay,
      if (initialBca != null) 'initial_bca': initialBca,
      if (initialCash != null) 'initial_cash': initialCash,
      if (initialOvo != null) 'initial_ovo': initialOvo,
      if (initialBri != null) 'initial_bri': initialBri,
      if (initialRekpon != null) 'initial_rekpon': initialRekpon,
      if (finalGopay != null) 'final_gopay': finalGopay,
      if (finalBca != null) 'final_bca': finalBca,
      if (finalCash != null) 'final_cash': finalCash,
      if (finalOvo != null) 'final_ovo': finalOvo,
      if (finalBri != null) 'final_bri': finalBri,
      if (finalRekpon != null) 'final_rekpon': finalRekpon,
      if (initialCapital != null) 'initial_capital': initialCapital,
      if (finalResult != null) 'final_result': finalResult,
      if (mileage != null) 'mileage': mileage,
      if (netIncome != null) 'net_income': netIncome,
      if (createdAt != null) 'created_at': createdAt,
      if (updatedAt != null) 'updated_at': updatedAt,
      if (deletedAt != null) 'deleted_at': deletedAt,
      if (syncStatus != null) 'sync_status': syncStatus,
    });
  }

  IncomeCompanion copyWith({
    Value<String>? uuid,
    Value<int>? id,
    Value<DateTime>? date,
    Value<int>? initialMileage,
    Value<int>? finalMileage,
    Value<double>? initialGopay,
    Value<double>? initialBca,
    Value<double>? initialCash,
    Value<double>? initialOvo,
    Value<double>? initialBri,
    Value<double>? initialRekpon,
    Value<double>? finalGopay,
    Value<double>? finalBca,
    Value<double>? finalCash,
    Value<double>? finalOvo,
    Value<double>? finalBri,
    Value<double>? finalRekpon,
    Value<double?>? initialCapital,
    Value<double?>? finalResult,
    Value<int?>? mileage,
    Value<double?>? netIncome,
    Value<DateTime>? createdAt,
    Value<DateTime>? updatedAt,
    Value<DateTime?>? deletedAt,
    Value<SyncStatus>? syncStatus,
  }) {
    return IncomeCompanion(
      uuid: uuid ?? this.uuid,
      id: id ?? this.id,
      date: date ?? this.date,
      initialMileage: initialMileage ?? this.initialMileage,
      finalMileage: finalMileage ?? this.finalMileage,
      initialGopay: initialGopay ?? this.initialGopay,
      initialBca: initialBca ?? this.initialBca,
      initialCash: initialCash ?? this.initialCash,
      initialOvo: initialOvo ?? this.initialOvo,
      initialBri: initialBri ?? this.initialBri,
      initialRekpon: initialRekpon ?? this.initialRekpon,
      finalGopay: finalGopay ?? this.finalGopay,
      finalBca: finalBca ?? this.finalBca,
      finalCash: finalCash ?? this.finalCash,
      finalOvo: finalOvo ?? this.finalOvo,
      finalBri: finalBri ?? this.finalBri,
      finalRekpon: finalRekpon ?? this.finalRekpon,
      initialCapital: initialCapital ?? this.initialCapital,
      finalResult: finalResult ?? this.finalResult,
      mileage: mileage ?? this.mileage,
      netIncome: netIncome ?? this.netIncome,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      syncStatus: syncStatus ?? this.syncStatus,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (uuid.present) {
      map['uuid'] = Variable<String>(uuid.value);
    }
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (date.present) {
      map['date'] = Variable<DateTime>(
        $IncomeTable.$converterdate.toSql(date.value),
      );
    }
    if (initialMileage.present) {
      map['initial_mileage'] = Variable<int>(initialMileage.value);
    }
    if (finalMileage.present) {
      map['final_mileage'] = Variable<int>(finalMileage.value);
    }
    if (initialGopay.present) {
      map['initial_gopay'] = Variable<double>(initialGopay.value);
    }
    if (initialBca.present) {
      map['initial_bca'] = Variable<double>(initialBca.value);
    }
    if (initialCash.present) {
      map['initial_cash'] = Variable<double>(initialCash.value);
    }
    if (initialOvo.present) {
      map['initial_ovo'] = Variable<double>(initialOvo.value);
    }
    if (initialBri.present) {
      map['initial_bri'] = Variable<double>(initialBri.value);
    }
    if (initialRekpon.present) {
      map['initial_rekpon'] = Variable<double>(initialRekpon.value);
    }
    if (finalGopay.present) {
      map['final_gopay'] = Variable<double>(finalGopay.value);
    }
    if (finalBca.present) {
      map['final_bca'] = Variable<double>(finalBca.value);
    }
    if (finalCash.present) {
      map['final_cash'] = Variable<double>(finalCash.value);
    }
    if (finalOvo.present) {
      map['final_ovo'] = Variable<double>(finalOvo.value);
    }
    if (finalBri.present) {
      map['final_bri'] = Variable<double>(finalBri.value);
    }
    if (finalRekpon.present) {
      map['final_rekpon'] = Variable<double>(finalRekpon.value);
    }
    if (initialCapital.present) {
      map['initial_capital'] = Variable<double>(initialCapital.value);
    }
    if (finalResult.present) {
      map['final_result'] = Variable<double>(finalResult.value);
    }
    if (mileage.present) {
      map['mileage'] = Variable<int>(mileage.value);
    }
    if (netIncome.present) {
      map['net_income'] = Variable<double>(netIncome.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<DateTime>(updatedAt.value);
    }
    if (deletedAt.present) {
      map['deleted_at'] = Variable<DateTime>(deletedAt.value);
    }
    if (syncStatus.present) {
      map['sync_status'] = Variable<String>(
        $IncomeTable.$convertersyncStatus.toSql(syncStatus.value),
      );
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('IncomeCompanion(')
          ..write('uuid: $uuid, ')
          ..write('id: $id, ')
          ..write('date: $date, ')
          ..write('initialMileage: $initialMileage, ')
          ..write('finalMileage: $finalMileage, ')
          ..write('initialGopay: $initialGopay, ')
          ..write('initialBca: $initialBca, ')
          ..write('initialCash: $initialCash, ')
          ..write('initialOvo: $initialOvo, ')
          ..write('initialBri: $initialBri, ')
          ..write('initialRekpon: $initialRekpon, ')
          ..write('finalGopay: $finalGopay, ')
          ..write('finalBca: $finalBca, ')
          ..write('finalCash: $finalCash, ')
          ..write('finalOvo: $finalOvo, ')
          ..write('finalBri: $finalBri, ')
          ..write('finalRekpon: $finalRekpon, ')
          ..write('initialCapital: $initialCapital, ')
          ..write('finalResult: $finalResult, ')
          ..write('mileage: $mileage, ')
          ..write('netIncome: $netIncome, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('deletedAt: $deletedAt, ')
          ..write('syncStatus: $syncStatus')
          ..write(')'))
        .toString();
  }
}

class $OrdersTable extends Orders with TableInfo<$OrdersTable, Order> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $OrdersTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _uuidMeta = const VerificationMeta('uuid');
  @override
  late final GeneratedColumn<String> uuid = GeneratedColumn<String>(
    'uuid',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: false,
    clientDefault: () => const Uuid().v4(),
  );
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
    'id',
    aliasedName,
    false,
    hasAutoIncrement: true,
    type: DriftSqlType.int,
    requiredDuringInsert: false,
    defaultConstraints: GeneratedColumn.constraintIsAlways(
      'PRIMARY KEY AUTOINCREMENT',
    ),
  );
  @override
  late final GeneratedColumnWithTypeConverter<DateTime, DateTime> date =
      GeneratedColumn<DateTime>(
        'date',
        aliasedName,
        false,
        type: DriftSqlType.dateTime,
        requiredDuringInsert: true,
      ).withConverter<DateTime>($OrdersTable.$converterdate);
  static const VerificationMeta _orderCompletedMeta = const VerificationMeta(
    'orderCompleted',
  );
  @override
  late final GeneratedColumn<int> orderCompleted = GeneratedColumn<int>(
    'order_completed',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _orderMissedMeta = const VerificationMeta(
    'orderMissed',
  );
  @override
  late final GeneratedColumn<int> orderMissed = GeneratedColumn<int>(
    'order_missed',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _orderCanceledMeta = const VerificationMeta(
    'orderCanceled',
  );
  @override
  late final GeneratedColumn<int> orderCanceled = GeneratedColumn<int>(
    'order_canceled',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _cbsOrderMeta = const VerificationMeta(
    'cbsOrder',
  );
  @override
  late final GeneratedColumn<int> cbsOrder = GeneratedColumn<int>(
    'cbs_order',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _incomingOrderMeta = const VerificationMeta(
    'incomingOrder',
  );
  @override
  late final GeneratedColumn<int> incomingOrder = GeneratedColumn<int>(
    'incoming_order',
    aliasedName,
    true,
    type: DriftSqlType.int,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _orderReceivedMeta = const VerificationMeta(
    'orderReceived',
  );
  @override
  late final GeneratedColumn<int> orderReceived = GeneratedColumn<int>(
    'order_received',
    aliasedName,
    true,
    type: DriftSqlType.int,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _bidAcceptanceMeta = const VerificationMeta(
    'bidAcceptance',
  );
  @override
  late final GeneratedColumn<double> bidAcceptance = GeneratedColumn<double>(
    'bid_acceptance',
    aliasedName,
    true,
    type: DriftSqlType.double,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _tripCompletionMeta = const VerificationMeta(
    'tripCompletion',
  );
  @override
  late final GeneratedColumn<double> tripCompletion = GeneratedColumn<double>(
    'trip_completion',
    aliasedName,
    true,
    type: DriftSqlType.double,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _pointsMeta = const VerificationMeta('points');
  @override
  late final GeneratedColumn<int> points = GeneratedColumn<int>(
    'points',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _tripMeta = const VerificationMeta('trip');
  @override
  late final GeneratedColumn<double> trip = GeneratedColumn<double>(
    'trip',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _bonusMeta = const VerificationMeta('bonus');
  @override
  late final GeneratedColumn<double> bonus = GeneratedColumn<double>(
    'bonus',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _tipsMeta = const VerificationMeta('tips');
  @override
  late final GeneratedColumn<double> tips = GeneratedColumn<double>(
    'tips',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _incomeMeta = const VerificationMeta('income');
  @override
  late final GeneratedColumn<double> income = GeneratedColumn<double>(
    'income',
    aliasedName,
    true,
    type: DriftSqlType.double,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _createdAtMeta = const VerificationMeta(
    'createdAt',
  );
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
    'created_at',
    aliasedName,
    false,
    type: DriftSqlType.dateTime,
    requiredDuringInsert: false,
    clientDefault: () => DateTime.now().toUtc(),
  );
  static const VerificationMeta _updatedAtMeta = const VerificationMeta(
    'updatedAt',
  );
  @override
  late final GeneratedColumn<DateTime> updatedAt = GeneratedColumn<DateTime>(
    'updated_at',
    aliasedName,
    false,
    type: DriftSqlType.dateTime,
    requiredDuringInsert: false,
    clientDefault: () => DateTime.now().toUtc(),
  );
  static const VerificationMeta _deletedAtMeta = const VerificationMeta(
    'deletedAt',
  );
  @override
  late final GeneratedColumn<DateTime> deletedAt = GeneratedColumn<DateTime>(
    'deleted_at',
    aliasedName,
    true,
    type: DriftSqlType.dateTime,
    requiredDuringInsert: false,
  );
  @override
  late final GeneratedColumnWithTypeConverter<SyncStatus, String> syncStatus =
      GeneratedColumn<String>(
        'sync_status',
        aliasedName,
        false,
        type: DriftSqlType.string,
        requiredDuringInsert: false,
        defaultValue: const Constant('pendingUpload'),
      ).withConverter<SyncStatus>($OrdersTable.$convertersyncStatus);
  @override
  List<GeneratedColumn> get $columns => [
    uuid,
    id,
    date,
    orderCompleted,
    orderMissed,
    orderCanceled,
    cbsOrder,
    incomingOrder,
    orderReceived,
    bidAcceptance,
    tripCompletion,
    points,
    trip,
    bonus,
    tips,
    income,
    createdAt,
    updatedAt,
    deletedAt,
    syncStatus,
  ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'orders';
  @override
  VerificationContext validateIntegrity(
    Insertable<Order> instance, {
    bool isInserting = false,
  }) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('uuid')) {
      context.handle(
        _uuidMeta,
        uuid.isAcceptableOrUnknown(data['uuid']!, _uuidMeta),
      );
    }
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('order_completed')) {
      context.handle(
        _orderCompletedMeta,
        orderCompleted.isAcceptableOrUnknown(
          data['order_completed']!,
          _orderCompletedMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_orderCompletedMeta);
    }
    if (data.containsKey('order_missed')) {
      context.handle(
        _orderMissedMeta,
        orderMissed.isAcceptableOrUnknown(
          data['order_missed']!,
          _orderMissedMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_orderMissedMeta);
    }
    if (data.containsKey('order_canceled')) {
      context.handle(
        _orderCanceledMeta,
        orderCanceled.isAcceptableOrUnknown(
          data['order_canceled']!,
          _orderCanceledMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_orderCanceledMeta);
    }
    if (data.containsKey('cbs_order')) {
      context.handle(
        _cbsOrderMeta,
        cbsOrder.isAcceptableOrUnknown(data['cbs_order']!, _cbsOrderMeta),
      );
    } else if (isInserting) {
      context.missing(_cbsOrderMeta);
    }
    if (data.containsKey('incoming_order')) {
      context.handle(
        _incomingOrderMeta,
        incomingOrder.isAcceptableOrUnknown(
          data['incoming_order']!,
          _incomingOrderMeta,
        ),
      );
    }
    if (data.containsKey('order_received')) {
      context.handle(
        _orderReceivedMeta,
        orderReceived.isAcceptableOrUnknown(
          data['order_received']!,
          _orderReceivedMeta,
        ),
      );
    }
    if (data.containsKey('bid_acceptance')) {
      context.handle(
        _bidAcceptanceMeta,
        bidAcceptance.isAcceptableOrUnknown(
          data['bid_acceptance']!,
          _bidAcceptanceMeta,
        ),
      );
    }
    if (data.containsKey('trip_completion')) {
      context.handle(
        _tripCompletionMeta,
        tripCompletion.isAcceptableOrUnknown(
          data['trip_completion']!,
          _tripCompletionMeta,
        ),
      );
    }
    if (data.containsKey('points')) {
      context.handle(
        _pointsMeta,
        points.isAcceptableOrUnknown(data['points']!, _pointsMeta),
      );
    } else if (isInserting) {
      context.missing(_pointsMeta);
    }
    if (data.containsKey('trip')) {
      context.handle(
        _tripMeta,
        trip.isAcceptableOrUnknown(data['trip']!, _tripMeta),
      );
    } else if (isInserting) {
      context.missing(_tripMeta);
    }
    if (data.containsKey('bonus')) {
      context.handle(
        _bonusMeta,
        bonus.isAcceptableOrUnknown(data['bonus']!, _bonusMeta),
      );
    } else if (isInserting) {
      context.missing(_bonusMeta);
    }
    if (data.containsKey('tips')) {
      context.handle(
        _tipsMeta,
        tips.isAcceptableOrUnknown(data['tips']!, _tipsMeta),
      );
    } else if (isInserting) {
      context.missing(_tipsMeta);
    }
    if (data.containsKey('income')) {
      context.handle(
        _incomeMeta,
        income.isAcceptableOrUnknown(data['income']!, _incomeMeta),
      );
    }
    if (data.containsKey('created_at')) {
      context.handle(
        _createdAtMeta,
        createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta),
      );
    }
    if (data.containsKey('updated_at')) {
      context.handle(
        _updatedAtMeta,
        updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta),
      );
    }
    if (data.containsKey('deleted_at')) {
      context.handle(
        _deletedAtMeta,
        deletedAt.isAcceptableOrUnknown(data['deleted_at']!, _deletedAtMeta),
      );
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  Order map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return Order(
      uuid: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}uuid'],
      )!,
      id: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}id'],
      )!,
      date: $OrdersTable.$converterdate.fromSql(
        attachedDatabase.typeMapping.read(
          DriftSqlType.dateTime,
          data['${effectivePrefix}date'],
        )!,
      ),
      orderCompleted: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}order_completed'],
      )!,
      orderMissed: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}order_missed'],
      )!,
      orderCanceled: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}order_canceled'],
      )!,
      cbsOrder: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}cbs_order'],
      )!,
      incomingOrder: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}incoming_order'],
      ),
      orderReceived: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}order_received'],
      ),
      bidAcceptance: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}bid_acceptance'],
      ),
      tripCompletion: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}trip_completion'],
      ),
      points: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}points'],
      )!,
      trip: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}trip'],
      )!,
      bonus: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}bonus'],
      )!,
      tips: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}tips'],
      )!,
      income: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}income'],
      ),
      createdAt: attachedDatabase.typeMapping.read(
        DriftSqlType.dateTime,
        data['${effectivePrefix}created_at'],
      )!,
      updatedAt: attachedDatabase.typeMapping.read(
        DriftSqlType.dateTime,
        data['${effectivePrefix}updated_at'],
      )!,
      deletedAt: attachedDatabase.typeMapping.read(
        DriftSqlType.dateTime,
        data['${effectivePrefix}deleted_at'],
      ),
      syncStatus: $OrdersTable.$convertersyncStatus.fromSql(
        attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}sync_status'],
        )!,
      ),
    );
  }

  @override
  $OrdersTable createAlias(String alias) {
    return $OrdersTable(attachedDatabase, alias);
  }

  static TypeConverter<DateTime, DateTime> $converterdate =
      const UtcDateTimeConverter();
  static TypeConverter<SyncStatus, String> $convertersyncStatus =
      const SyncStatusConverter();
}

class Order extends DataClass implements Insertable<Order> {
  final String uuid;
  final int id;
  final DateTime date;
  final int orderCompleted;
  final int orderMissed;
  final int orderCanceled;
  final int cbsOrder;
  final int? incomingOrder;
  final int? orderReceived;
  final double? bidAcceptance;
  final double? tripCompletion;
  final int points;
  final double trip;
  final double bonus;
  final double tips;
  final double? income;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;
  final SyncStatus syncStatus;
  const Order({
    required this.uuid,
    required this.id,
    required this.date,
    required this.orderCompleted,
    required this.orderMissed,
    required this.orderCanceled,
    required this.cbsOrder,
    this.incomingOrder,
    this.orderReceived,
    this.bidAcceptance,
    this.tripCompletion,
    required this.points,
    required this.trip,
    required this.bonus,
    required this.tips,
    this.income,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    required this.syncStatus,
  });
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['uuid'] = Variable<String>(uuid);
    map['id'] = Variable<int>(id);
    {
      map['date'] = Variable<DateTime>($OrdersTable.$converterdate.toSql(date));
    }
    map['order_completed'] = Variable<int>(orderCompleted);
    map['order_missed'] = Variable<int>(orderMissed);
    map['order_canceled'] = Variable<int>(orderCanceled);
    map['cbs_order'] = Variable<int>(cbsOrder);
    if (!nullToAbsent || incomingOrder != null) {
      map['incoming_order'] = Variable<int>(incomingOrder);
    }
    if (!nullToAbsent || orderReceived != null) {
      map['order_received'] = Variable<int>(orderReceived);
    }
    if (!nullToAbsent || bidAcceptance != null) {
      map['bid_acceptance'] = Variable<double>(bidAcceptance);
    }
    if (!nullToAbsent || tripCompletion != null) {
      map['trip_completion'] = Variable<double>(tripCompletion);
    }
    map['points'] = Variable<int>(points);
    map['trip'] = Variable<double>(trip);
    map['bonus'] = Variable<double>(bonus);
    map['tips'] = Variable<double>(tips);
    if (!nullToAbsent || income != null) {
      map['income'] = Variable<double>(income);
    }
    map['created_at'] = Variable<DateTime>(createdAt);
    map['updated_at'] = Variable<DateTime>(updatedAt);
    if (!nullToAbsent || deletedAt != null) {
      map['deleted_at'] = Variable<DateTime>(deletedAt);
    }
    {
      map['sync_status'] = Variable<String>(
        $OrdersTable.$convertersyncStatus.toSql(syncStatus),
      );
    }
    return map;
  }

  OrdersCompanion toCompanion(bool nullToAbsent) {
    return OrdersCompanion(
      uuid: Value(uuid),
      id: Value(id),
      date: Value(date),
      orderCompleted: Value(orderCompleted),
      orderMissed: Value(orderMissed),
      orderCanceled: Value(orderCanceled),
      cbsOrder: Value(cbsOrder),
      incomingOrder: incomingOrder == null && nullToAbsent
          ? const Value.absent()
          : Value(incomingOrder),
      orderReceived: orderReceived == null && nullToAbsent
          ? const Value.absent()
          : Value(orderReceived),
      bidAcceptance: bidAcceptance == null && nullToAbsent
          ? const Value.absent()
          : Value(bidAcceptance),
      tripCompletion: tripCompletion == null && nullToAbsent
          ? const Value.absent()
          : Value(tripCompletion),
      points: Value(points),
      trip: Value(trip),
      bonus: Value(bonus),
      tips: Value(tips),
      income: income == null && nullToAbsent
          ? const Value.absent()
          : Value(income),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
      deletedAt: deletedAt == null && nullToAbsent
          ? const Value.absent()
          : Value(deletedAt),
      syncStatus: Value(syncStatus),
    );
  }

  factory Order.fromJson(
    Map<String, dynamic> json, {
    ValueSerializer? serializer,
  }) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return Order(
      uuid: serializer.fromJson<String>(json['uuid']),
      id: serializer.fromJson<int>(json['id']),
      date: serializer.fromJson<DateTime>(json['date']),
      orderCompleted: serializer.fromJson<int>(json['orderCompleted']),
      orderMissed: serializer.fromJson<int>(json['orderMissed']),
      orderCanceled: serializer.fromJson<int>(json['orderCanceled']),
      cbsOrder: serializer.fromJson<int>(json['cbsOrder']),
      incomingOrder: serializer.fromJson<int?>(json['incomingOrder']),
      orderReceived: serializer.fromJson<int?>(json['orderReceived']),
      bidAcceptance: serializer.fromJson<double?>(json['bidAcceptance']),
      tripCompletion: serializer.fromJson<double?>(json['tripCompletion']),
      points: serializer.fromJson<int>(json['points']),
      trip: serializer.fromJson<double>(json['trip']),
      bonus: serializer.fromJson<double>(json['bonus']),
      tips: serializer.fromJson<double>(json['tips']),
      income: serializer.fromJson<double?>(json['income']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      updatedAt: serializer.fromJson<DateTime>(json['updatedAt']),
      deletedAt: serializer.fromJson<DateTime?>(json['deletedAt']),
      syncStatus: serializer.fromJson<SyncStatus>(json['syncStatus']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'uuid': serializer.toJson<String>(uuid),
      'id': serializer.toJson<int>(id),
      'date': serializer.toJson<DateTime>(date),
      'orderCompleted': serializer.toJson<int>(orderCompleted),
      'orderMissed': serializer.toJson<int>(orderMissed),
      'orderCanceled': serializer.toJson<int>(orderCanceled),
      'cbsOrder': serializer.toJson<int>(cbsOrder),
      'incomingOrder': serializer.toJson<int?>(incomingOrder),
      'orderReceived': serializer.toJson<int?>(orderReceived),
      'bidAcceptance': serializer.toJson<double?>(bidAcceptance),
      'tripCompletion': serializer.toJson<double?>(tripCompletion),
      'points': serializer.toJson<int>(points),
      'trip': serializer.toJson<double>(trip),
      'bonus': serializer.toJson<double>(bonus),
      'tips': serializer.toJson<double>(tips),
      'income': serializer.toJson<double?>(income),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'updatedAt': serializer.toJson<DateTime>(updatedAt),
      'deletedAt': serializer.toJson<DateTime?>(deletedAt),
      'syncStatus': serializer.toJson<SyncStatus>(syncStatus),
    };
  }

  Order copyWith({
    String? uuid,
    int? id,
    DateTime? date,
    int? orderCompleted,
    int? orderMissed,
    int? orderCanceled,
    int? cbsOrder,
    Value<int?> incomingOrder = const Value.absent(),
    Value<int?> orderReceived = const Value.absent(),
    Value<double?> bidAcceptance = const Value.absent(),
    Value<double?> tripCompletion = const Value.absent(),
    int? points,
    double? trip,
    double? bonus,
    double? tips,
    Value<double?> income = const Value.absent(),
    DateTime? createdAt,
    DateTime? updatedAt,
    Value<DateTime?> deletedAt = const Value.absent(),
    SyncStatus? syncStatus,
  }) => Order(
    uuid: uuid ?? this.uuid,
    id: id ?? this.id,
    date: date ?? this.date,
    orderCompleted: orderCompleted ?? this.orderCompleted,
    orderMissed: orderMissed ?? this.orderMissed,
    orderCanceled: orderCanceled ?? this.orderCanceled,
    cbsOrder: cbsOrder ?? this.cbsOrder,
    incomingOrder: incomingOrder.present
        ? incomingOrder.value
        : this.incomingOrder,
    orderReceived: orderReceived.present
        ? orderReceived.value
        : this.orderReceived,
    bidAcceptance: bidAcceptance.present
        ? bidAcceptance.value
        : this.bidAcceptance,
    tripCompletion: tripCompletion.present
        ? tripCompletion.value
        : this.tripCompletion,
    points: points ?? this.points,
    trip: trip ?? this.trip,
    bonus: bonus ?? this.bonus,
    tips: tips ?? this.tips,
    income: income.present ? income.value : this.income,
    createdAt: createdAt ?? this.createdAt,
    updatedAt: updatedAt ?? this.updatedAt,
    deletedAt: deletedAt.present ? deletedAt.value : this.deletedAt,
    syncStatus: syncStatus ?? this.syncStatus,
  );
  Order copyWithCompanion(OrdersCompanion data) {
    return Order(
      uuid: data.uuid.present ? data.uuid.value : this.uuid,
      id: data.id.present ? data.id.value : this.id,
      date: data.date.present ? data.date.value : this.date,
      orderCompleted: data.orderCompleted.present
          ? data.orderCompleted.value
          : this.orderCompleted,
      orderMissed: data.orderMissed.present
          ? data.orderMissed.value
          : this.orderMissed,
      orderCanceled: data.orderCanceled.present
          ? data.orderCanceled.value
          : this.orderCanceled,
      cbsOrder: data.cbsOrder.present ? data.cbsOrder.value : this.cbsOrder,
      incomingOrder: data.incomingOrder.present
          ? data.incomingOrder.value
          : this.incomingOrder,
      orderReceived: data.orderReceived.present
          ? data.orderReceived.value
          : this.orderReceived,
      bidAcceptance: data.bidAcceptance.present
          ? data.bidAcceptance.value
          : this.bidAcceptance,
      tripCompletion: data.tripCompletion.present
          ? data.tripCompletion.value
          : this.tripCompletion,
      points: data.points.present ? data.points.value : this.points,
      trip: data.trip.present ? data.trip.value : this.trip,
      bonus: data.bonus.present ? data.bonus.value : this.bonus,
      tips: data.tips.present ? data.tips.value : this.tips,
      income: data.income.present ? data.income.value : this.income,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
      deletedAt: data.deletedAt.present ? data.deletedAt.value : this.deletedAt,
      syncStatus: data.syncStatus.present
          ? data.syncStatus.value
          : this.syncStatus,
    );
  }

  @override
  String toString() {
    return (StringBuffer('Order(')
          ..write('uuid: $uuid, ')
          ..write('id: $id, ')
          ..write('date: $date, ')
          ..write('orderCompleted: $orderCompleted, ')
          ..write('orderMissed: $orderMissed, ')
          ..write('orderCanceled: $orderCanceled, ')
          ..write('cbsOrder: $cbsOrder, ')
          ..write('incomingOrder: $incomingOrder, ')
          ..write('orderReceived: $orderReceived, ')
          ..write('bidAcceptance: $bidAcceptance, ')
          ..write('tripCompletion: $tripCompletion, ')
          ..write('points: $points, ')
          ..write('trip: $trip, ')
          ..write('bonus: $bonus, ')
          ..write('tips: $tips, ')
          ..write('income: $income, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('deletedAt: $deletedAt, ')
          ..write('syncStatus: $syncStatus')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
    uuid,
    id,
    date,
    orderCompleted,
    orderMissed,
    orderCanceled,
    cbsOrder,
    incomingOrder,
    orderReceived,
    bidAcceptance,
    tripCompletion,
    points,
    trip,
    bonus,
    tips,
    income,
    createdAt,
    updatedAt,
    deletedAt,
    syncStatus,
  );
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is Order &&
          other.uuid == this.uuid &&
          other.id == this.id &&
          other.date == this.date &&
          other.orderCompleted == this.orderCompleted &&
          other.orderMissed == this.orderMissed &&
          other.orderCanceled == this.orderCanceled &&
          other.cbsOrder == this.cbsOrder &&
          other.incomingOrder == this.incomingOrder &&
          other.orderReceived == this.orderReceived &&
          other.bidAcceptance == this.bidAcceptance &&
          other.tripCompletion == this.tripCompletion &&
          other.points == this.points &&
          other.trip == this.trip &&
          other.bonus == this.bonus &&
          other.tips == this.tips &&
          other.income == this.income &&
          other.createdAt == this.createdAt &&
          other.updatedAt == this.updatedAt &&
          other.deletedAt == this.deletedAt &&
          other.syncStatus == this.syncStatus);
}

class OrdersCompanion extends UpdateCompanion<Order> {
  final Value<String> uuid;
  final Value<int> id;
  final Value<DateTime> date;
  final Value<int> orderCompleted;
  final Value<int> orderMissed;
  final Value<int> orderCanceled;
  final Value<int> cbsOrder;
  final Value<int?> incomingOrder;
  final Value<int?> orderReceived;
  final Value<double?> bidAcceptance;
  final Value<double?> tripCompletion;
  final Value<int> points;
  final Value<double> trip;
  final Value<double> bonus;
  final Value<double> tips;
  final Value<double?> income;
  final Value<DateTime> createdAt;
  final Value<DateTime> updatedAt;
  final Value<DateTime?> deletedAt;
  final Value<SyncStatus> syncStatus;
  const OrdersCompanion({
    this.uuid = const Value.absent(),
    this.id = const Value.absent(),
    this.date = const Value.absent(),
    this.orderCompleted = const Value.absent(),
    this.orderMissed = const Value.absent(),
    this.orderCanceled = const Value.absent(),
    this.cbsOrder = const Value.absent(),
    this.incomingOrder = const Value.absent(),
    this.orderReceived = const Value.absent(),
    this.bidAcceptance = const Value.absent(),
    this.tripCompletion = const Value.absent(),
    this.points = const Value.absent(),
    this.trip = const Value.absent(),
    this.bonus = const Value.absent(),
    this.tips = const Value.absent(),
    this.income = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.deletedAt = const Value.absent(),
    this.syncStatus = const Value.absent(),
  });
  OrdersCompanion.insert({
    this.uuid = const Value.absent(),
    this.id = const Value.absent(),
    required DateTime date,
    required int orderCompleted,
    required int orderMissed,
    required int orderCanceled,
    required int cbsOrder,
    this.incomingOrder = const Value.absent(),
    this.orderReceived = const Value.absent(),
    this.bidAcceptance = const Value.absent(),
    this.tripCompletion = const Value.absent(),
    required int points,
    required double trip,
    required double bonus,
    required double tips,
    this.income = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.deletedAt = const Value.absent(),
    this.syncStatus = const Value.absent(),
  }) : date = Value(date),
       orderCompleted = Value(orderCompleted),
       orderMissed = Value(orderMissed),
       orderCanceled = Value(orderCanceled),
       cbsOrder = Value(cbsOrder),
       points = Value(points),
       trip = Value(trip),
       bonus = Value(bonus),
       tips = Value(tips);
  static Insertable<Order> custom({
    Expression<String>? uuid,
    Expression<int>? id,
    Expression<DateTime>? date,
    Expression<int>? orderCompleted,
    Expression<int>? orderMissed,
    Expression<int>? orderCanceled,
    Expression<int>? cbsOrder,
    Expression<int>? incomingOrder,
    Expression<int>? orderReceived,
    Expression<double>? bidAcceptance,
    Expression<double>? tripCompletion,
    Expression<int>? points,
    Expression<double>? trip,
    Expression<double>? bonus,
    Expression<double>? tips,
    Expression<double>? income,
    Expression<DateTime>? createdAt,
    Expression<DateTime>? updatedAt,
    Expression<DateTime>? deletedAt,
    Expression<String>? syncStatus,
  }) {
    return RawValuesInsertable({
      if (uuid != null) 'uuid': uuid,
      if (id != null) 'id': id,
      if (date != null) 'date': date,
      if (orderCompleted != null) 'order_completed': orderCompleted,
      if (orderMissed != null) 'order_missed': orderMissed,
      if (orderCanceled != null) 'order_canceled': orderCanceled,
      if (cbsOrder != null) 'cbs_order': cbsOrder,
      if (incomingOrder != null) 'incoming_order': incomingOrder,
      if (orderReceived != null) 'order_received': orderReceived,
      if (bidAcceptance != null) 'bid_acceptance': bidAcceptance,
      if (tripCompletion != null) 'trip_completion': tripCompletion,
      if (points != null) 'points': points,
      if (trip != null) 'trip': trip,
      if (bonus != null) 'bonus': bonus,
      if (tips != null) 'tips': tips,
      if (income != null) 'income': income,
      if (createdAt != null) 'created_at': createdAt,
      if (updatedAt != null) 'updated_at': updatedAt,
      if (deletedAt != null) 'deleted_at': deletedAt,
      if (syncStatus != null) 'sync_status': syncStatus,
    });
  }

  OrdersCompanion copyWith({
    Value<String>? uuid,
    Value<int>? id,
    Value<DateTime>? date,
    Value<int>? orderCompleted,
    Value<int>? orderMissed,
    Value<int>? orderCanceled,
    Value<int>? cbsOrder,
    Value<int?>? incomingOrder,
    Value<int?>? orderReceived,
    Value<double?>? bidAcceptance,
    Value<double?>? tripCompletion,
    Value<int>? points,
    Value<double>? trip,
    Value<double>? bonus,
    Value<double>? tips,
    Value<double?>? income,
    Value<DateTime>? createdAt,
    Value<DateTime>? updatedAt,
    Value<DateTime?>? deletedAt,
    Value<SyncStatus>? syncStatus,
  }) {
    return OrdersCompanion(
      uuid: uuid ?? this.uuid,
      id: id ?? this.id,
      date: date ?? this.date,
      orderCompleted: orderCompleted ?? this.orderCompleted,
      orderMissed: orderMissed ?? this.orderMissed,
      orderCanceled: orderCanceled ?? this.orderCanceled,
      cbsOrder: cbsOrder ?? this.cbsOrder,
      incomingOrder: incomingOrder ?? this.incomingOrder,
      orderReceived: orderReceived ?? this.orderReceived,
      bidAcceptance: bidAcceptance ?? this.bidAcceptance,
      tripCompletion: tripCompletion ?? this.tripCompletion,
      points: points ?? this.points,
      trip: trip ?? this.trip,
      bonus: bonus ?? this.bonus,
      tips: tips ?? this.tips,
      income: income ?? this.income,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      syncStatus: syncStatus ?? this.syncStatus,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (uuid.present) {
      map['uuid'] = Variable<String>(uuid.value);
    }
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (date.present) {
      map['date'] = Variable<DateTime>(
        $OrdersTable.$converterdate.toSql(date.value),
      );
    }
    if (orderCompleted.present) {
      map['order_completed'] = Variable<int>(orderCompleted.value);
    }
    if (orderMissed.present) {
      map['order_missed'] = Variable<int>(orderMissed.value);
    }
    if (orderCanceled.present) {
      map['order_canceled'] = Variable<int>(orderCanceled.value);
    }
    if (cbsOrder.present) {
      map['cbs_order'] = Variable<int>(cbsOrder.value);
    }
    if (incomingOrder.present) {
      map['incoming_order'] = Variable<int>(incomingOrder.value);
    }
    if (orderReceived.present) {
      map['order_received'] = Variable<int>(orderReceived.value);
    }
    if (bidAcceptance.present) {
      map['bid_acceptance'] = Variable<double>(bidAcceptance.value);
    }
    if (tripCompletion.present) {
      map['trip_completion'] = Variable<double>(tripCompletion.value);
    }
    if (points.present) {
      map['points'] = Variable<int>(points.value);
    }
    if (trip.present) {
      map['trip'] = Variable<double>(trip.value);
    }
    if (bonus.present) {
      map['bonus'] = Variable<double>(bonus.value);
    }
    if (tips.present) {
      map['tips'] = Variable<double>(tips.value);
    }
    if (income.present) {
      map['income'] = Variable<double>(income.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<DateTime>(updatedAt.value);
    }
    if (deletedAt.present) {
      map['deleted_at'] = Variable<DateTime>(deletedAt.value);
    }
    if (syncStatus.present) {
      map['sync_status'] = Variable<String>(
        $OrdersTable.$convertersyncStatus.toSql(syncStatus.value),
      );
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('OrdersCompanion(')
          ..write('uuid: $uuid, ')
          ..write('id: $id, ')
          ..write('date: $date, ')
          ..write('orderCompleted: $orderCompleted, ')
          ..write('orderMissed: $orderMissed, ')
          ..write('orderCanceled: $orderCanceled, ')
          ..write('cbsOrder: $cbsOrder, ')
          ..write('incomingOrder: $incomingOrder, ')
          ..write('orderReceived: $orderReceived, ')
          ..write('bidAcceptance: $bidAcceptance, ')
          ..write('tripCompletion: $tripCompletion, ')
          ..write('points: $points, ')
          ..write('trip: $trip, ')
          ..write('bonus: $bonus, ')
          ..write('tips: $tips, ')
          ..write('income: $income, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('deletedAt: $deletedAt, ')
          ..write('syncStatus: $syncStatus')
          ..write(')'))
        .toString();
  }
}

class $PerformanceTable extends Performance
    with TableInfo<$PerformanceTable, PerformanceData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $PerformanceTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _uuidMeta = const VerificationMeta('uuid');
  @override
  late final GeneratedColumn<String> uuid = GeneratedColumn<String>(
    'uuid',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: false,
    clientDefault: () => const Uuid().v4(),
  );
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
    'id',
    aliasedName,
    false,
    hasAutoIncrement: true,
    type: DriftSqlType.int,
    requiredDuringInsert: false,
    defaultConstraints: GeneratedColumn.constraintIsAlways(
      'PRIMARY KEY AUTOINCREMENT',
    ),
  );
  @override
  late final GeneratedColumnWithTypeConverter<DateTime, DateTime> date =
      GeneratedColumn<DateTime>(
        'date',
        aliasedName,
        false,
        type: DriftSqlType.dateTime,
        requiredDuringInsert: true,
      ).withConverter<DateTime>($PerformanceTable.$converterdate);
  static const VerificationMeta _bidPerformanceMeta = const VerificationMeta(
    'bidPerformance',
  );
  @override
  late final GeneratedColumn<double> bidPerformance = GeneratedColumn<double>(
    'bid_performance',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _tripPerformanceMeta = const VerificationMeta(
    'tripPerformance',
  );
  @override
  late final GeneratedColumn<double> tripPerformance = GeneratedColumn<double>(
    'trip_performance',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _activeDaysMeta = const VerificationMeta(
    'activeDays',
  );
  @override
  late final GeneratedColumn<int> activeDays = GeneratedColumn<int>(
    'active_days',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _onlineHoursMeta = const VerificationMeta(
    'onlineHours',
  );
  @override
  late final GeneratedColumn<double> onlineHours = GeneratedColumn<double>(
    'online_hours',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _avgCompletedMeta = const VerificationMeta(
    'avgCompleted',
  );
  @override
  late final GeneratedColumn<double> avgCompleted = GeneratedColumn<double>(
    'avg_completed',
    aliasedName,
    true,
    type: DriftSqlType.double,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _avgOnlineMeta = const VerificationMeta(
    'avgOnline',
  );
  @override
  late final GeneratedColumn<double> avgOnline = GeneratedColumn<double>(
    'avg_online',
    aliasedName,
    true,
    type: DriftSqlType.double,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _retentionMeta = const VerificationMeta(
    'retention',
  );
  @override
  late final GeneratedColumn<double> retention = GeneratedColumn<double>(
    'retention',
    aliasedName,
    true,
    type: DriftSqlType.double,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _createdAtMeta = const VerificationMeta(
    'createdAt',
  );
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
    'created_at',
    aliasedName,
    false,
    type: DriftSqlType.dateTime,
    requiredDuringInsert: false,
    clientDefault: () => DateTime.now().toUtc(),
  );
  static const VerificationMeta _updatedAtMeta = const VerificationMeta(
    'updatedAt',
  );
  @override
  late final GeneratedColumn<DateTime> updatedAt = GeneratedColumn<DateTime>(
    'updated_at',
    aliasedName,
    false,
    type: DriftSqlType.dateTime,
    requiredDuringInsert: false,
    clientDefault: () => DateTime.now().toUtc(),
  );
  static const VerificationMeta _deletedAtMeta = const VerificationMeta(
    'deletedAt',
  );
  @override
  late final GeneratedColumn<DateTime> deletedAt = GeneratedColumn<DateTime>(
    'deleted_at',
    aliasedName,
    true,
    type: DriftSqlType.dateTime,
    requiredDuringInsert: false,
  );
  @override
  late final GeneratedColumnWithTypeConverter<SyncStatus, String> syncStatus =
      GeneratedColumn<String>(
        'sync_status',
        aliasedName,
        false,
        type: DriftSqlType.string,
        requiredDuringInsert: false,
        defaultValue: const Constant('pendingUpload'),
      ).withConverter<SyncStatus>($PerformanceTable.$convertersyncStatus);
  @override
  List<GeneratedColumn> get $columns => [
    uuid,
    id,
    date,
    bidPerformance,
    tripPerformance,
    activeDays,
    onlineHours,
    avgCompleted,
    avgOnline,
    retention,
    createdAt,
    updatedAt,
    deletedAt,
    syncStatus,
  ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'performance';
  @override
  VerificationContext validateIntegrity(
    Insertable<PerformanceData> instance, {
    bool isInserting = false,
  }) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('uuid')) {
      context.handle(
        _uuidMeta,
        uuid.isAcceptableOrUnknown(data['uuid']!, _uuidMeta),
      );
    }
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('bid_performance')) {
      context.handle(
        _bidPerformanceMeta,
        bidPerformance.isAcceptableOrUnknown(
          data['bid_performance']!,
          _bidPerformanceMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_bidPerformanceMeta);
    }
    if (data.containsKey('trip_performance')) {
      context.handle(
        _tripPerformanceMeta,
        tripPerformance.isAcceptableOrUnknown(
          data['trip_performance']!,
          _tripPerformanceMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_tripPerformanceMeta);
    }
    if (data.containsKey('active_days')) {
      context.handle(
        _activeDaysMeta,
        activeDays.isAcceptableOrUnknown(data['active_days']!, _activeDaysMeta),
      );
    } else if (isInserting) {
      context.missing(_activeDaysMeta);
    }
    if (data.containsKey('online_hours')) {
      context.handle(
        _onlineHoursMeta,
        onlineHours.isAcceptableOrUnknown(
          data['online_hours']!,
          _onlineHoursMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_onlineHoursMeta);
    }
    if (data.containsKey('avg_completed')) {
      context.handle(
        _avgCompletedMeta,
        avgCompleted.isAcceptableOrUnknown(
          data['avg_completed']!,
          _avgCompletedMeta,
        ),
      );
    }
    if (data.containsKey('avg_online')) {
      context.handle(
        _avgOnlineMeta,
        avgOnline.isAcceptableOrUnknown(data['avg_online']!, _avgOnlineMeta),
      );
    }
    if (data.containsKey('retention')) {
      context.handle(
        _retentionMeta,
        retention.isAcceptableOrUnknown(data['retention']!, _retentionMeta),
      );
    }
    if (data.containsKey('created_at')) {
      context.handle(
        _createdAtMeta,
        createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta),
      );
    }
    if (data.containsKey('updated_at')) {
      context.handle(
        _updatedAtMeta,
        updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta),
      );
    }
    if (data.containsKey('deleted_at')) {
      context.handle(
        _deletedAtMeta,
        deletedAt.isAcceptableOrUnknown(data['deleted_at']!, _deletedAtMeta),
      );
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  PerformanceData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return PerformanceData(
      uuid: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}uuid'],
      )!,
      id: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}id'],
      )!,
      date: $PerformanceTable.$converterdate.fromSql(
        attachedDatabase.typeMapping.read(
          DriftSqlType.dateTime,
          data['${effectivePrefix}date'],
        )!,
      ),
      bidPerformance: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}bid_performance'],
      )!,
      tripPerformance: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}trip_performance'],
      )!,
      activeDays: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}active_days'],
      )!,
      onlineHours: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}online_hours'],
      )!,
      avgCompleted: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}avg_completed'],
      ),
      avgOnline: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}avg_online'],
      ),
      retention: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}retention'],
      ),
      createdAt: attachedDatabase.typeMapping.read(
        DriftSqlType.dateTime,
        data['${effectivePrefix}created_at'],
      )!,
      updatedAt: attachedDatabase.typeMapping.read(
        DriftSqlType.dateTime,
        data['${effectivePrefix}updated_at'],
      )!,
      deletedAt: attachedDatabase.typeMapping.read(
        DriftSqlType.dateTime,
        data['${effectivePrefix}deleted_at'],
      ),
      syncStatus: $PerformanceTable.$convertersyncStatus.fromSql(
        attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}sync_status'],
        )!,
      ),
    );
  }

  @override
  $PerformanceTable createAlias(String alias) {
    return $PerformanceTable(attachedDatabase, alias);
  }

  static TypeConverter<DateTime, DateTime> $converterdate =
      const UtcDateTimeConverter();
  static TypeConverter<SyncStatus, String> $convertersyncStatus =
      const SyncStatusConverter();
}

class PerformanceData extends DataClass implements Insertable<PerformanceData> {
  final String uuid;
  final int id;
  final DateTime date;
  final double bidPerformance;
  final double tripPerformance;
  final int activeDays;
  final double onlineHours;
  final double? avgCompleted;
  final double? avgOnline;
  final double? retention;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;
  final SyncStatus syncStatus;
  const PerformanceData({
    required this.uuid,
    required this.id,
    required this.date,
    required this.bidPerformance,
    required this.tripPerformance,
    required this.activeDays,
    required this.onlineHours,
    this.avgCompleted,
    this.avgOnline,
    this.retention,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    required this.syncStatus,
  });
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['uuid'] = Variable<String>(uuid);
    map['id'] = Variable<int>(id);
    {
      map['date'] = Variable<DateTime>(
        $PerformanceTable.$converterdate.toSql(date),
      );
    }
    map['bid_performance'] = Variable<double>(bidPerformance);
    map['trip_performance'] = Variable<double>(tripPerformance);
    map['active_days'] = Variable<int>(activeDays);
    map['online_hours'] = Variable<double>(onlineHours);
    if (!nullToAbsent || avgCompleted != null) {
      map['avg_completed'] = Variable<double>(avgCompleted);
    }
    if (!nullToAbsent || avgOnline != null) {
      map['avg_online'] = Variable<double>(avgOnline);
    }
    if (!nullToAbsent || retention != null) {
      map['retention'] = Variable<double>(retention);
    }
    map['created_at'] = Variable<DateTime>(createdAt);
    map['updated_at'] = Variable<DateTime>(updatedAt);
    if (!nullToAbsent || deletedAt != null) {
      map['deleted_at'] = Variable<DateTime>(deletedAt);
    }
    {
      map['sync_status'] = Variable<String>(
        $PerformanceTable.$convertersyncStatus.toSql(syncStatus),
      );
    }
    return map;
  }

  PerformanceCompanion toCompanion(bool nullToAbsent) {
    return PerformanceCompanion(
      uuid: Value(uuid),
      id: Value(id),
      date: Value(date),
      bidPerformance: Value(bidPerformance),
      tripPerformance: Value(tripPerformance),
      activeDays: Value(activeDays),
      onlineHours: Value(onlineHours),
      avgCompleted: avgCompleted == null && nullToAbsent
          ? const Value.absent()
          : Value(avgCompleted),
      avgOnline: avgOnline == null && nullToAbsent
          ? const Value.absent()
          : Value(avgOnline),
      retention: retention == null && nullToAbsent
          ? const Value.absent()
          : Value(retention),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
      deletedAt: deletedAt == null && nullToAbsent
          ? const Value.absent()
          : Value(deletedAt),
      syncStatus: Value(syncStatus),
    );
  }

  factory PerformanceData.fromJson(
    Map<String, dynamic> json, {
    ValueSerializer? serializer,
  }) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return PerformanceData(
      uuid: serializer.fromJson<String>(json['uuid']),
      id: serializer.fromJson<int>(json['id']),
      date: serializer.fromJson<DateTime>(json['date']),
      bidPerformance: serializer.fromJson<double>(json['bidPerformance']),
      tripPerformance: serializer.fromJson<double>(json['tripPerformance']),
      activeDays: serializer.fromJson<int>(json['activeDays']),
      onlineHours: serializer.fromJson<double>(json['onlineHours']),
      avgCompleted: serializer.fromJson<double?>(json['avgCompleted']),
      avgOnline: serializer.fromJson<double?>(json['avgOnline']),
      retention: serializer.fromJson<double?>(json['retention']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      updatedAt: serializer.fromJson<DateTime>(json['updatedAt']),
      deletedAt: serializer.fromJson<DateTime?>(json['deletedAt']),
      syncStatus: serializer.fromJson<SyncStatus>(json['syncStatus']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'uuid': serializer.toJson<String>(uuid),
      'id': serializer.toJson<int>(id),
      'date': serializer.toJson<DateTime>(date),
      'bidPerformance': serializer.toJson<double>(bidPerformance),
      'tripPerformance': serializer.toJson<double>(tripPerformance),
      'activeDays': serializer.toJson<int>(activeDays),
      'onlineHours': serializer.toJson<double>(onlineHours),
      'avgCompleted': serializer.toJson<double?>(avgCompleted),
      'avgOnline': serializer.toJson<double?>(avgOnline),
      'retention': serializer.toJson<double?>(retention),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'updatedAt': serializer.toJson<DateTime>(updatedAt),
      'deletedAt': serializer.toJson<DateTime?>(deletedAt),
      'syncStatus': serializer.toJson<SyncStatus>(syncStatus),
    };
  }

  PerformanceData copyWith({
    String? uuid,
    int? id,
    DateTime? date,
    double? bidPerformance,
    double? tripPerformance,
    int? activeDays,
    double? onlineHours,
    Value<double?> avgCompleted = const Value.absent(),
    Value<double?> avgOnline = const Value.absent(),
    Value<double?> retention = const Value.absent(),
    DateTime? createdAt,
    DateTime? updatedAt,
    Value<DateTime?> deletedAt = const Value.absent(),
    SyncStatus? syncStatus,
  }) => PerformanceData(
    uuid: uuid ?? this.uuid,
    id: id ?? this.id,
    date: date ?? this.date,
    bidPerformance: bidPerformance ?? this.bidPerformance,
    tripPerformance: tripPerformance ?? this.tripPerformance,
    activeDays: activeDays ?? this.activeDays,
    onlineHours: onlineHours ?? this.onlineHours,
    avgCompleted: avgCompleted.present ? avgCompleted.value : this.avgCompleted,
    avgOnline: avgOnline.present ? avgOnline.value : this.avgOnline,
    retention: retention.present ? retention.value : this.retention,
    createdAt: createdAt ?? this.createdAt,
    updatedAt: updatedAt ?? this.updatedAt,
    deletedAt: deletedAt.present ? deletedAt.value : this.deletedAt,
    syncStatus: syncStatus ?? this.syncStatus,
  );
  PerformanceData copyWithCompanion(PerformanceCompanion data) {
    return PerformanceData(
      uuid: data.uuid.present ? data.uuid.value : this.uuid,
      id: data.id.present ? data.id.value : this.id,
      date: data.date.present ? data.date.value : this.date,
      bidPerformance: data.bidPerformance.present
          ? data.bidPerformance.value
          : this.bidPerformance,
      tripPerformance: data.tripPerformance.present
          ? data.tripPerformance.value
          : this.tripPerformance,
      activeDays: data.activeDays.present
          ? data.activeDays.value
          : this.activeDays,
      onlineHours: data.onlineHours.present
          ? data.onlineHours.value
          : this.onlineHours,
      avgCompleted: data.avgCompleted.present
          ? data.avgCompleted.value
          : this.avgCompleted,
      avgOnline: data.avgOnline.present ? data.avgOnline.value : this.avgOnline,
      retention: data.retention.present ? data.retention.value : this.retention,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
      deletedAt: data.deletedAt.present ? data.deletedAt.value : this.deletedAt,
      syncStatus: data.syncStatus.present
          ? data.syncStatus.value
          : this.syncStatus,
    );
  }

  @override
  String toString() {
    return (StringBuffer('PerformanceData(')
          ..write('uuid: $uuid, ')
          ..write('id: $id, ')
          ..write('date: $date, ')
          ..write('bidPerformance: $bidPerformance, ')
          ..write('tripPerformance: $tripPerformance, ')
          ..write('activeDays: $activeDays, ')
          ..write('onlineHours: $onlineHours, ')
          ..write('avgCompleted: $avgCompleted, ')
          ..write('avgOnline: $avgOnline, ')
          ..write('retention: $retention, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('deletedAt: $deletedAt, ')
          ..write('syncStatus: $syncStatus')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
    uuid,
    id,
    date,
    bidPerformance,
    tripPerformance,
    activeDays,
    onlineHours,
    avgCompleted,
    avgOnline,
    retention,
    createdAt,
    updatedAt,
    deletedAt,
    syncStatus,
  );
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is PerformanceData &&
          other.uuid == this.uuid &&
          other.id == this.id &&
          other.date == this.date &&
          other.bidPerformance == this.bidPerformance &&
          other.tripPerformance == this.tripPerformance &&
          other.activeDays == this.activeDays &&
          other.onlineHours == this.onlineHours &&
          other.avgCompleted == this.avgCompleted &&
          other.avgOnline == this.avgOnline &&
          other.retention == this.retention &&
          other.createdAt == this.createdAt &&
          other.updatedAt == this.updatedAt &&
          other.deletedAt == this.deletedAt &&
          other.syncStatus == this.syncStatus);
}

class PerformanceCompanion extends UpdateCompanion<PerformanceData> {
  final Value<String> uuid;
  final Value<int> id;
  final Value<DateTime> date;
  final Value<double> bidPerformance;
  final Value<double> tripPerformance;
  final Value<int> activeDays;
  final Value<double> onlineHours;
  final Value<double?> avgCompleted;
  final Value<double?> avgOnline;
  final Value<double?> retention;
  final Value<DateTime> createdAt;
  final Value<DateTime> updatedAt;
  final Value<DateTime?> deletedAt;
  final Value<SyncStatus> syncStatus;
  const PerformanceCompanion({
    this.uuid = const Value.absent(),
    this.id = const Value.absent(),
    this.date = const Value.absent(),
    this.bidPerformance = const Value.absent(),
    this.tripPerformance = const Value.absent(),
    this.activeDays = const Value.absent(),
    this.onlineHours = const Value.absent(),
    this.avgCompleted = const Value.absent(),
    this.avgOnline = const Value.absent(),
    this.retention = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.deletedAt = const Value.absent(),
    this.syncStatus = const Value.absent(),
  });
  PerformanceCompanion.insert({
    this.uuid = const Value.absent(),
    this.id = const Value.absent(),
    required DateTime date,
    required double bidPerformance,
    required double tripPerformance,
    required int activeDays,
    required double onlineHours,
    this.avgCompleted = const Value.absent(),
    this.avgOnline = const Value.absent(),
    this.retention = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.deletedAt = const Value.absent(),
    this.syncStatus = const Value.absent(),
  }) : date = Value(date),
       bidPerformance = Value(bidPerformance),
       tripPerformance = Value(tripPerformance),
       activeDays = Value(activeDays),
       onlineHours = Value(onlineHours);
  static Insertable<PerformanceData> custom({
    Expression<String>? uuid,
    Expression<int>? id,
    Expression<DateTime>? date,
    Expression<double>? bidPerformance,
    Expression<double>? tripPerformance,
    Expression<int>? activeDays,
    Expression<double>? onlineHours,
    Expression<double>? avgCompleted,
    Expression<double>? avgOnline,
    Expression<double>? retention,
    Expression<DateTime>? createdAt,
    Expression<DateTime>? updatedAt,
    Expression<DateTime>? deletedAt,
    Expression<String>? syncStatus,
  }) {
    return RawValuesInsertable({
      if (uuid != null) 'uuid': uuid,
      if (id != null) 'id': id,
      if (date != null) 'date': date,
      if (bidPerformance != null) 'bid_performance': bidPerformance,
      if (tripPerformance != null) 'trip_performance': tripPerformance,
      if (activeDays != null) 'active_days': activeDays,
      if (onlineHours != null) 'online_hours': onlineHours,
      if (avgCompleted != null) 'avg_completed': avgCompleted,
      if (avgOnline != null) 'avg_online': avgOnline,
      if (retention != null) 'retention': retention,
      if (createdAt != null) 'created_at': createdAt,
      if (updatedAt != null) 'updated_at': updatedAt,
      if (deletedAt != null) 'deleted_at': deletedAt,
      if (syncStatus != null) 'sync_status': syncStatus,
    });
  }

  PerformanceCompanion copyWith({
    Value<String>? uuid,
    Value<int>? id,
    Value<DateTime>? date,
    Value<double>? bidPerformance,
    Value<double>? tripPerformance,
    Value<int>? activeDays,
    Value<double>? onlineHours,
    Value<double?>? avgCompleted,
    Value<double?>? avgOnline,
    Value<double?>? retention,
    Value<DateTime>? createdAt,
    Value<DateTime>? updatedAt,
    Value<DateTime?>? deletedAt,
    Value<SyncStatus>? syncStatus,
  }) {
    return PerformanceCompanion(
      uuid: uuid ?? this.uuid,
      id: id ?? this.id,
      date: date ?? this.date,
      bidPerformance: bidPerformance ?? this.bidPerformance,
      tripPerformance: tripPerformance ?? this.tripPerformance,
      activeDays: activeDays ?? this.activeDays,
      onlineHours: onlineHours ?? this.onlineHours,
      avgCompleted: avgCompleted ?? this.avgCompleted,
      avgOnline: avgOnline ?? this.avgOnline,
      retention: retention ?? this.retention,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      syncStatus: syncStatus ?? this.syncStatus,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (uuid.present) {
      map['uuid'] = Variable<String>(uuid.value);
    }
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (date.present) {
      map['date'] = Variable<DateTime>(
        $PerformanceTable.$converterdate.toSql(date.value),
      );
    }
    if (bidPerformance.present) {
      map['bid_performance'] = Variable<double>(bidPerformance.value);
    }
    if (tripPerformance.present) {
      map['trip_performance'] = Variable<double>(tripPerformance.value);
    }
    if (activeDays.present) {
      map['active_days'] = Variable<int>(activeDays.value);
    }
    if (onlineHours.present) {
      map['online_hours'] = Variable<double>(onlineHours.value);
    }
    if (avgCompleted.present) {
      map['avg_completed'] = Variable<double>(avgCompleted.value);
    }
    if (avgOnline.present) {
      map['avg_online'] = Variable<double>(avgOnline.value);
    }
    if (retention.present) {
      map['retention'] = Variable<double>(retention.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<DateTime>(updatedAt.value);
    }
    if (deletedAt.present) {
      map['deleted_at'] = Variable<DateTime>(deletedAt.value);
    }
    if (syncStatus.present) {
      map['sync_status'] = Variable<String>(
        $PerformanceTable.$convertersyncStatus.toSql(syncStatus.value),
      );
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('PerformanceCompanion(')
          ..write('uuid: $uuid, ')
          ..write('id: $id, ')
          ..write('date: $date, ')
          ..write('bidPerformance: $bidPerformance, ')
          ..write('tripPerformance: $tripPerformance, ')
          ..write('activeDays: $activeDays, ')
          ..write('onlineHours: $onlineHours, ')
          ..write('avgCompleted: $avgCompleted, ')
          ..write('avgOnline: $avgOnline, ')
          ..write('retention: $retention, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('deletedAt: $deletedAt, ')
          ..write('syncStatus: $syncStatus')
          ..write(')'))
        .toString();
  }
}

class $LevelSettingsTable extends LevelSettings
    with TableInfo<$LevelSettingsTable, LevelSetting> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $LevelSettingsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
    'id',
    aliasedName,
    false,
    hasAutoIncrement: true,
    type: DriftSqlType.int,
    requiredDuringInsert: false,
    defaultConstraints: GeneratedColumn.constraintIsAlways(
      'PRIMARY KEY AUTOINCREMENT',
    ),
  );
  static const VerificationMeta _platinumPointsReqMeta = const VerificationMeta(
    'platinumPointsReq',
  );
  @override
  late final GeneratedColumn<int> platinumPointsReq = GeneratedColumn<int>(
    'platinum_points_req',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _platinumBidReqMeta = const VerificationMeta(
    'platinumBidReq',
  );
  @override
  late final GeneratedColumn<double> platinumBidReq = GeneratedColumn<double>(
    'platinum_bid_req',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _platinumTripReqMeta = const VerificationMeta(
    'platinumTripReq',
  );
  @override
  late final GeneratedColumn<double> platinumTripReq = GeneratedColumn<double>(
    'platinum_trip_req',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _goldPointsReqMeta = const VerificationMeta(
    'goldPointsReq',
  );
  @override
  late final GeneratedColumn<int> goldPointsReq = GeneratedColumn<int>(
    'gold_points_req',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _goldBidReqMeta = const VerificationMeta(
    'goldBidReq',
  );
  @override
  late final GeneratedColumn<double> goldBidReq = GeneratedColumn<double>(
    'gold_bid_req',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _goldTripReqMeta = const VerificationMeta(
    'goldTripReq',
  );
  @override
  late final GeneratedColumn<double> goldTripReq = GeneratedColumn<double>(
    'gold_trip_req',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _silverPointsReqMeta = const VerificationMeta(
    'silverPointsReq',
  );
  @override
  late final GeneratedColumn<int> silverPointsReq = GeneratedColumn<int>(
    'silver_points_req',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _silverBidReqMeta = const VerificationMeta(
    'silverBidReq',
  );
  @override
  late final GeneratedColumn<double> silverBidReq = GeneratedColumn<double>(
    'silver_bid_req',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _silverTripReqMeta = const VerificationMeta(
    'silverTripReq',
  );
  @override
  late final GeneratedColumn<double> silverTripReq = GeneratedColumn<double>(
    'silver_trip_req',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  @override
  List<GeneratedColumn> get $columns => [
    id,
    platinumPointsReq,
    platinumBidReq,
    platinumTripReq,
    goldPointsReq,
    goldBidReq,
    goldTripReq,
    silverPointsReq,
    silverBidReq,
    silverTripReq,
  ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'level_settings';
  @override
  VerificationContext validateIntegrity(
    Insertable<LevelSetting> instance, {
    bool isInserting = false,
  }) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('platinum_points_req')) {
      context.handle(
        _platinumPointsReqMeta,
        platinumPointsReq.isAcceptableOrUnknown(
          data['platinum_points_req']!,
          _platinumPointsReqMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_platinumPointsReqMeta);
    }
    if (data.containsKey('platinum_bid_req')) {
      context.handle(
        _platinumBidReqMeta,
        platinumBidReq.isAcceptableOrUnknown(
          data['platinum_bid_req']!,
          _platinumBidReqMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_platinumBidReqMeta);
    }
    if (data.containsKey('platinum_trip_req')) {
      context.handle(
        _platinumTripReqMeta,
        platinumTripReq.isAcceptableOrUnknown(
          data['platinum_trip_req']!,
          _platinumTripReqMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_platinumTripReqMeta);
    }
    if (data.containsKey('gold_points_req')) {
      context.handle(
        _goldPointsReqMeta,
        goldPointsReq.isAcceptableOrUnknown(
          data['gold_points_req']!,
          _goldPointsReqMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_goldPointsReqMeta);
    }
    if (data.containsKey('gold_bid_req')) {
      context.handle(
        _goldBidReqMeta,
        goldBidReq.isAcceptableOrUnknown(
          data['gold_bid_req']!,
          _goldBidReqMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_goldBidReqMeta);
    }
    if (data.containsKey('gold_trip_req')) {
      context.handle(
        _goldTripReqMeta,
        goldTripReq.isAcceptableOrUnknown(
          data['gold_trip_req']!,
          _goldTripReqMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_goldTripReqMeta);
    }
    if (data.containsKey('silver_points_req')) {
      context.handle(
        _silverPointsReqMeta,
        silverPointsReq.isAcceptableOrUnknown(
          data['silver_points_req']!,
          _silverPointsReqMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_silverPointsReqMeta);
    }
    if (data.containsKey('silver_bid_req')) {
      context.handle(
        _silverBidReqMeta,
        silverBidReq.isAcceptableOrUnknown(
          data['silver_bid_req']!,
          _silverBidReqMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_silverBidReqMeta);
    }
    if (data.containsKey('silver_trip_req')) {
      context.handle(
        _silverTripReqMeta,
        silverTripReq.isAcceptableOrUnknown(
          data['silver_trip_req']!,
          _silverTripReqMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_silverTripReqMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  LevelSetting map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return LevelSetting(
      id: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}id'],
      )!,
      platinumPointsReq: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}platinum_points_req'],
      )!,
      platinumBidReq: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}platinum_bid_req'],
      )!,
      platinumTripReq: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}platinum_trip_req'],
      )!,
      goldPointsReq: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}gold_points_req'],
      )!,
      goldBidReq: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}gold_bid_req'],
      )!,
      goldTripReq: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}gold_trip_req'],
      )!,
      silverPointsReq: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}silver_points_req'],
      )!,
      silverBidReq: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}silver_bid_req'],
      )!,
      silverTripReq: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}silver_trip_req'],
      )!,
    );
  }

  @override
  $LevelSettingsTable createAlias(String alias) {
    return $LevelSettingsTable(attachedDatabase, alias);
  }
}

class LevelSetting extends DataClass implements Insertable<LevelSetting> {
  final int id;
  final int platinumPointsReq;
  final double platinumBidReq;
  final double platinumTripReq;
  final int goldPointsReq;
  final double goldBidReq;
  final double goldTripReq;
  final int silverPointsReq;
  final double silverBidReq;
  final double silverTripReq;
  const LevelSetting({
    required this.id,
    required this.platinumPointsReq,
    required this.platinumBidReq,
    required this.platinumTripReq,
    required this.goldPointsReq,
    required this.goldBidReq,
    required this.goldTripReq,
    required this.silverPointsReq,
    required this.silverBidReq,
    required this.silverTripReq,
  });
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['platinum_points_req'] = Variable<int>(platinumPointsReq);
    map['platinum_bid_req'] = Variable<double>(platinumBidReq);
    map['platinum_trip_req'] = Variable<double>(platinumTripReq);
    map['gold_points_req'] = Variable<int>(goldPointsReq);
    map['gold_bid_req'] = Variable<double>(goldBidReq);
    map['gold_trip_req'] = Variable<double>(goldTripReq);
    map['silver_points_req'] = Variable<int>(silverPointsReq);
    map['silver_bid_req'] = Variable<double>(silverBidReq);
    map['silver_trip_req'] = Variable<double>(silverTripReq);
    return map;
  }

  LevelSettingsCompanion toCompanion(bool nullToAbsent) {
    return LevelSettingsCompanion(
      id: Value(id),
      platinumPointsReq: Value(platinumPointsReq),
      platinumBidReq: Value(platinumBidReq),
      platinumTripReq: Value(platinumTripReq),
      goldPointsReq: Value(goldPointsReq),
      goldBidReq: Value(goldBidReq),
      goldTripReq: Value(goldTripReq),
      silverPointsReq: Value(silverPointsReq),
      silverBidReq: Value(silverBidReq),
      silverTripReq: Value(silverTripReq),
    );
  }

  factory LevelSetting.fromJson(
    Map<String, dynamic> json, {
    ValueSerializer? serializer,
  }) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return LevelSetting(
      id: serializer.fromJson<int>(json['id']),
      platinumPointsReq: serializer.fromJson<int>(json['platinumPointsReq']),
      platinumBidReq: serializer.fromJson<double>(json['platinumBidReq']),
      platinumTripReq: serializer.fromJson<double>(json['platinumTripReq']),
      goldPointsReq: serializer.fromJson<int>(json['goldPointsReq']),
      goldBidReq: serializer.fromJson<double>(json['goldBidReq']),
      goldTripReq: serializer.fromJson<double>(json['goldTripReq']),
      silverPointsReq: serializer.fromJson<int>(json['silverPointsReq']),
      silverBidReq: serializer.fromJson<double>(json['silverBidReq']),
      silverTripReq: serializer.fromJson<double>(json['silverTripReq']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'platinumPointsReq': serializer.toJson<int>(platinumPointsReq),
      'platinumBidReq': serializer.toJson<double>(platinumBidReq),
      'platinumTripReq': serializer.toJson<double>(platinumTripReq),
      'goldPointsReq': serializer.toJson<int>(goldPointsReq),
      'goldBidReq': serializer.toJson<double>(goldBidReq),
      'goldTripReq': serializer.toJson<double>(goldTripReq),
      'silverPointsReq': serializer.toJson<int>(silverPointsReq),
      'silverBidReq': serializer.toJson<double>(silverBidReq),
      'silverTripReq': serializer.toJson<double>(silverTripReq),
    };
  }

  LevelSetting copyWith({
    int? id,
    int? platinumPointsReq,
    double? platinumBidReq,
    double? platinumTripReq,
    int? goldPointsReq,
    double? goldBidReq,
    double? goldTripReq,
    int? silverPointsReq,
    double? silverBidReq,
    double? silverTripReq,
  }) => LevelSetting(
    id: id ?? this.id,
    platinumPointsReq: platinumPointsReq ?? this.platinumPointsReq,
    platinumBidReq: platinumBidReq ?? this.platinumBidReq,
    platinumTripReq: platinumTripReq ?? this.platinumTripReq,
    goldPointsReq: goldPointsReq ?? this.goldPointsReq,
    goldBidReq: goldBidReq ?? this.goldBidReq,
    goldTripReq: goldTripReq ?? this.goldTripReq,
    silverPointsReq: silverPointsReq ?? this.silverPointsReq,
    silverBidReq: silverBidReq ?? this.silverBidReq,
    silverTripReq: silverTripReq ?? this.silverTripReq,
  );
  LevelSetting copyWithCompanion(LevelSettingsCompanion data) {
    return LevelSetting(
      id: data.id.present ? data.id.value : this.id,
      platinumPointsReq: data.platinumPointsReq.present
          ? data.platinumPointsReq.value
          : this.platinumPointsReq,
      platinumBidReq: data.platinumBidReq.present
          ? data.platinumBidReq.value
          : this.platinumBidReq,
      platinumTripReq: data.platinumTripReq.present
          ? data.platinumTripReq.value
          : this.platinumTripReq,
      goldPointsReq: data.goldPointsReq.present
          ? data.goldPointsReq.value
          : this.goldPointsReq,
      goldBidReq: data.goldBidReq.present
          ? data.goldBidReq.value
          : this.goldBidReq,
      goldTripReq: data.goldTripReq.present
          ? data.goldTripReq.value
          : this.goldTripReq,
      silverPointsReq: data.silverPointsReq.present
          ? data.silverPointsReq.value
          : this.silverPointsReq,
      silverBidReq: data.silverBidReq.present
          ? data.silverBidReq.value
          : this.silverBidReq,
      silverTripReq: data.silverTripReq.present
          ? data.silverTripReq.value
          : this.silverTripReq,
    );
  }

  @override
  String toString() {
    return (StringBuffer('LevelSetting(')
          ..write('id: $id, ')
          ..write('platinumPointsReq: $platinumPointsReq, ')
          ..write('platinumBidReq: $platinumBidReq, ')
          ..write('platinumTripReq: $platinumTripReq, ')
          ..write('goldPointsReq: $goldPointsReq, ')
          ..write('goldBidReq: $goldBidReq, ')
          ..write('goldTripReq: $goldTripReq, ')
          ..write('silverPointsReq: $silverPointsReq, ')
          ..write('silverBidReq: $silverBidReq, ')
          ..write('silverTripReq: $silverTripReq')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
    id,
    platinumPointsReq,
    platinumBidReq,
    platinumTripReq,
    goldPointsReq,
    goldBidReq,
    goldTripReq,
    silverPointsReq,
    silverBidReq,
    silverTripReq,
  );
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is LevelSetting &&
          other.id == this.id &&
          other.platinumPointsReq == this.platinumPointsReq &&
          other.platinumBidReq == this.platinumBidReq &&
          other.platinumTripReq == this.platinumTripReq &&
          other.goldPointsReq == this.goldPointsReq &&
          other.goldBidReq == this.goldBidReq &&
          other.goldTripReq == this.goldTripReq &&
          other.silverPointsReq == this.silverPointsReq &&
          other.silverBidReq == this.silverBidReq &&
          other.silverTripReq == this.silverTripReq);
}

class LevelSettingsCompanion extends UpdateCompanion<LevelSetting> {
  final Value<int> id;
  final Value<int> platinumPointsReq;
  final Value<double> platinumBidReq;
  final Value<double> platinumTripReq;
  final Value<int> goldPointsReq;
  final Value<double> goldBidReq;
  final Value<double> goldTripReq;
  final Value<int> silverPointsReq;
  final Value<double> silverBidReq;
  final Value<double> silverTripReq;
  const LevelSettingsCompanion({
    this.id = const Value.absent(),
    this.platinumPointsReq = const Value.absent(),
    this.platinumBidReq = const Value.absent(),
    this.platinumTripReq = const Value.absent(),
    this.goldPointsReq = const Value.absent(),
    this.goldBidReq = const Value.absent(),
    this.goldTripReq = const Value.absent(),
    this.silverPointsReq = const Value.absent(),
    this.silverBidReq = const Value.absent(),
    this.silverTripReq = const Value.absent(),
  });
  LevelSettingsCompanion.insert({
    this.id = const Value.absent(),
    required int platinumPointsReq,
    required double platinumBidReq,
    required double platinumTripReq,
    required int goldPointsReq,
    required double goldBidReq,
    required double goldTripReq,
    required int silverPointsReq,
    required double silverBidReq,
    required double silverTripReq,
  }) : platinumPointsReq = Value(platinumPointsReq),
       platinumBidReq = Value(platinumBidReq),
       platinumTripReq = Value(platinumTripReq),
       goldPointsReq = Value(goldPointsReq),
       goldBidReq = Value(goldBidReq),
       goldTripReq = Value(goldTripReq),
       silverPointsReq = Value(silverPointsReq),
       silverBidReq = Value(silverBidReq),
       silverTripReq = Value(silverTripReq);
  static Insertable<LevelSetting> custom({
    Expression<int>? id,
    Expression<int>? platinumPointsReq,
    Expression<double>? platinumBidReq,
    Expression<double>? platinumTripReq,
    Expression<int>? goldPointsReq,
    Expression<double>? goldBidReq,
    Expression<double>? goldTripReq,
    Expression<int>? silverPointsReq,
    Expression<double>? silverBidReq,
    Expression<double>? silverTripReq,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (platinumPointsReq != null) 'platinum_points_req': platinumPointsReq,
      if (platinumBidReq != null) 'platinum_bid_req': platinumBidReq,
      if (platinumTripReq != null) 'platinum_trip_req': platinumTripReq,
      if (goldPointsReq != null) 'gold_points_req': goldPointsReq,
      if (goldBidReq != null) 'gold_bid_req': goldBidReq,
      if (goldTripReq != null) 'gold_trip_req': goldTripReq,
      if (silverPointsReq != null) 'silver_points_req': silverPointsReq,
      if (silverBidReq != null) 'silver_bid_req': silverBidReq,
      if (silverTripReq != null) 'silver_trip_req': silverTripReq,
    });
  }

  LevelSettingsCompanion copyWith({
    Value<int>? id,
    Value<int>? platinumPointsReq,
    Value<double>? platinumBidReq,
    Value<double>? platinumTripReq,
    Value<int>? goldPointsReq,
    Value<double>? goldBidReq,
    Value<double>? goldTripReq,
    Value<int>? silverPointsReq,
    Value<double>? silverBidReq,
    Value<double>? silverTripReq,
  }) {
    return LevelSettingsCompanion(
      id: id ?? this.id,
      platinumPointsReq: platinumPointsReq ?? this.platinumPointsReq,
      platinumBidReq: platinumBidReq ?? this.platinumBidReq,
      platinumTripReq: platinumTripReq ?? this.platinumTripReq,
      goldPointsReq: goldPointsReq ?? this.goldPointsReq,
      goldBidReq: goldBidReq ?? this.goldBidReq,
      goldTripReq: goldTripReq ?? this.goldTripReq,
      silverPointsReq: silverPointsReq ?? this.silverPointsReq,
      silverBidReq: silverBidReq ?? this.silverBidReq,
      silverTripReq: silverTripReq ?? this.silverTripReq,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (platinumPointsReq.present) {
      map['platinum_points_req'] = Variable<int>(platinumPointsReq.value);
    }
    if (platinumBidReq.present) {
      map['platinum_bid_req'] = Variable<double>(platinumBidReq.value);
    }
    if (platinumTripReq.present) {
      map['platinum_trip_req'] = Variable<double>(platinumTripReq.value);
    }
    if (goldPointsReq.present) {
      map['gold_points_req'] = Variable<int>(goldPointsReq.value);
    }
    if (goldBidReq.present) {
      map['gold_bid_req'] = Variable<double>(goldBidReq.value);
    }
    if (goldTripReq.present) {
      map['gold_trip_req'] = Variable<double>(goldTripReq.value);
    }
    if (silverPointsReq.present) {
      map['silver_points_req'] = Variable<int>(silverPointsReq.value);
    }
    if (silverBidReq.present) {
      map['silver_bid_req'] = Variable<double>(silverBidReq.value);
    }
    if (silverTripReq.present) {
      map['silver_trip_req'] = Variable<double>(silverTripReq.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('LevelSettingsCompanion(')
          ..write('id: $id, ')
          ..write('platinumPointsReq: $platinumPointsReq, ')
          ..write('platinumBidReq: $platinumBidReq, ')
          ..write('platinumTripReq: $platinumTripReq, ')
          ..write('goldPointsReq: $goldPointsReq, ')
          ..write('goldBidReq: $goldBidReq, ')
          ..write('goldTripReq: $goldTripReq, ')
          ..write('silverPointsReq: $silverPointsReq, ')
          ..write('silverBidReq: $silverBidReq, ')
          ..write('silverTripReq: $silverTripReq')
          ..write(')'))
        .toString();
  }
}

class $SparePartsTable extends SpareParts
    with TableInfo<$SparePartsTable, SparePart> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $SparePartsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _uuidMeta = const VerificationMeta('uuid');
  @override
  late final GeneratedColumn<String> uuid = GeneratedColumn<String>(
    'uuid',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: false,
    clientDefault: () => const Uuid().v4(),
  );
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
    'id',
    aliasedName,
    false,
    hasAutoIncrement: true,
    type: DriftSqlType.int,
    requiredDuringInsert: false,
    defaultConstraints: GeneratedColumn.constraintIsAlways(
      'PRIMARY KEY AUTOINCREMENT',
    ),
  );
  static const VerificationMeta _partNameMeta = const VerificationMeta(
    'partName',
  );
  @override
  late final GeneratedColumn<String> partName = GeneratedColumn<String>(
    'part_name',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _partTypeMeta = const VerificationMeta(
    'partType',
  );
  @override
  late final GeneratedColumn<String> partType = GeneratedColumn<String>(
    'part_type',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _priceMeta = const VerificationMeta('price');
  @override
  late final GeneratedColumn<double> price = GeneratedColumn<double>(
    'price',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _mileageLimitMeta = const VerificationMeta(
    'mileageLimit',
  );
  @override
  late final GeneratedColumn<int> mileageLimit = GeneratedColumn<int>(
    'mileage_limit',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _initialMileageMeta = const VerificationMeta(
    'initialMileage',
  );
  @override
  late final GeneratedColumn<int> initialMileage = GeneratedColumn<int>(
    'initial_mileage',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _installationDateMeta = const VerificationMeta(
    'installationDate',
  );
  @override
  late final GeneratedColumn<DateTime> installationDate =
      GeneratedColumn<DateTime>(
        'installation_date',
        aliasedName,
        false,
        type: DriftSqlType.dateTime,
        requiredDuringInsert: false,
        clientDefault: () => DateTime.now().toUtc(),
      );
  static const VerificationMeta _currentMileageMeta = const VerificationMeta(
    'currentMileage',
  );
  @override
  late final GeneratedColumn<int> currentMileage = GeneratedColumn<int>(
    'current_mileage',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: false,
    defaultValue: const Constant(0),
  );
  static const VerificationMeta _warningStatusMeta = const VerificationMeta(
    'warningStatus',
  );
  @override
  late final GeneratedColumn<bool> warningStatus = GeneratedColumn<bool>(
    'warning_status',
    aliasedName,
    false,
    type: DriftSqlType.bool,
    requiredDuringInsert: false,
    defaultConstraints: GeneratedColumn.constraintIsAlways(
      'CHECK ("warning_status" IN (0, 1))',
    ),
    defaultValue: const Constant(false),
  );
  static const VerificationMeta _replacementCountMeta = const VerificationMeta(
    'replacementCount',
  );
  @override
  late final GeneratedColumn<int> replacementCount = GeneratedColumn<int>(
    'replacement_count',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: false,
    defaultValue: const Constant(0),
  );
  static const VerificationMeta _notesMeta = const VerificationMeta('notes');
  @override
  late final GeneratedColumn<String> notes = GeneratedColumn<String>(
    'notes',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: false,
    defaultValue: const Constant(''),
  );
  static const VerificationMeta _createdAtMeta = const VerificationMeta(
    'createdAt',
  );
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
    'created_at',
    aliasedName,
    false,
    type: DriftSqlType.dateTime,
    requiredDuringInsert: false,
    clientDefault: () => DateTime.now().toUtc(),
  );
  static const VerificationMeta _updatedAtMeta = const VerificationMeta(
    'updatedAt',
  );
  @override
  late final GeneratedColumn<DateTime> updatedAt = GeneratedColumn<DateTime>(
    'updated_at',
    aliasedName,
    false,
    type: DriftSqlType.dateTime,
    requiredDuringInsert: false,
    clientDefault: () => DateTime.now().toUtc(),
  );
  static const VerificationMeta _deletedAtMeta = const VerificationMeta(
    'deletedAt',
  );
  @override
  late final GeneratedColumn<DateTime> deletedAt = GeneratedColumn<DateTime>(
    'deleted_at',
    aliasedName,
    true,
    type: DriftSqlType.dateTime,
    requiredDuringInsert: false,
  );
  @override
  late final GeneratedColumnWithTypeConverter<SyncStatus, String> syncStatus =
      GeneratedColumn<String>(
        'sync_status',
        aliasedName,
        false,
        type: DriftSqlType.string,
        requiredDuringInsert: false,
        defaultValue: const Constant('pendingUpload'),
      ).withConverter<SyncStatus>($SparePartsTable.$convertersyncStatus);
  @override
  List<GeneratedColumn> get $columns => [
    uuid,
    id,
    partName,
    partType,
    price,
    mileageLimit,
    initialMileage,
    installationDate,
    currentMileage,
    warningStatus,
    replacementCount,
    notes,
    createdAt,
    updatedAt,
    deletedAt,
    syncStatus,
  ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'spare_parts';
  @override
  VerificationContext validateIntegrity(
    Insertable<SparePart> instance, {
    bool isInserting = false,
  }) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('uuid')) {
      context.handle(
        _uuidMeta,
        uuid.isAcceptableOrUnknown(data['uuid']!, _uuidMeta),
      );
    }
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('part_name')) {
      context.handle(
        _partNameMeta,
        partName.isAcceptableOrUnknown(data['part_name']!, _partNameMeta),
      );
    } else if (isInserting) {
      context.missing(_partNameMeta);
    }
    if (data.containsKey('part_type')) {
      context.handle(
        _partTypeMeta,
        partType.isAcceptableOrUnknown(data['part_type']!, _partTypeMeta),
      );
    } else if (isInserting) {
      context.missing(_partTypeMeta);
    }
    if (data.containsKey('price')) {
      context.handle(
        _priceMeta,
        price.isAcceptableOrUnknown(data['price']!, _priceMeta),
      );
    } else if (isInserting) {
      context.missing(_priceMeta);
    }
    if (data.containsKey('mileage_limit')) {
      context.handle(
        _mileageLimitMeta,
        mileageLimit.isAcceptableOrUnknown(
          data['mileage_limit']!,
          _mileageLimitMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_mileageLimitMeta);
    }
    if (data.containsKey('initial_mileage')) {
      context.handle(
        _initialMileageMeta,
        initialMileage.isAcceptableOrUnknown(
          data['initial_mileage']!,
          _initialMileageMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_initialMileageMeta);
    }
    if (data.containsKey('installation_date')) {
      context.handle(
        _installationDateMeta,
        installationDate.isAcceptableOrUnknown(
          data['installation_date']!,
          _installationDateMeta,
        ),
      );
    }
    if (data.containsKey('current_mileage')) {
      context.handle(
        _currentMileageMeta,
        currentMileage.isAcceptableOrUnknown(
          data['current_mileage']!,
          _currentMileageMeta,
        ),
      );
    }
    if (data.containsKey('warning_status')) {
      context.handle(
        _warningStatusMeta,
        warningStatus.isAcceptableOrUnknown(
          data['warning_status']!,
          _warningStatusMeta,
        ),
      );
    }
    if (data.containsKey('replacement_count')) {
      context.handle(
        _replacementCountMeta,
        replacementCount.isAcceptableOrUnknown(
          data['replacement_count']!,
          _replacementCountMeta,
        ),
      );
    }
    if (data.containsKey('notes')) {
      context.handle(
        _notesMeta,
        notes.isAcceptableOrUnknown(data['notes']!, _notesMeta),
      );
    }
    if (data.containsKey('created_at')) {
      context.handle(
        _createdAtMeta,
        createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta),
      );
    }
    if (data.containsKey('updated_at')) {
      context.handle(
        _updatedAtMeta,
        updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta),
      );
    }
    if (data.containsKey('deleted_at')) {
      context.handle(
        _deletedAtMeta,
        deletedAt.isAcceptableOrUnknown(data['deleted_at']!, _deletedAtMeta),
      );
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  SparePart map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return SparePart(
      uuid: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}uuid'],
      )!,
      id: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}id'],
      )!,
      partName: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}part_name'],
      )!,
      partType: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}part_type'],
      )!,
      price: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}price'],
      )!,
      mileageLimit: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}mileage_limit'],
      )!,
      initialMileage: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}initial_mileage'],
      )!,
      installationDate: attachedDatabase.typeMapping.read(
        DriftSqlType.dateTime,
        data['${effectivePrefix}installation_date'],
      )!,
      currentMileage: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}current_mileage'],
      )!,
      warningStatus: attachedDatabase.typeMapping.read(
        DriftSqlType.bool,
        data['${effectivePrefix}warning_status'],
      )!,
      replacementCount: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}replacement_count'],
      )!,
      notes: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}notes'],
      )!,
      createdAt: attachedDatabase.typeMapping.read(
        DriftSqlType.dateTime,
        data['${effectivePrefix}created_at'],
      )!,
      updatedAt: attachedDatabase.typeMapping.read(
        DriftSqlType.dateTime,
        data['${effectivePrefix}updated_at'],
      )!,
      deletedAt: attachedDatabase.typeMapping.read(
        DriftSqlType.dateTime,
        data['${effectivePrefix}deleted_at'],
      ),
      syncStatus: $SparePartsTable.$convertersyncStatus.fromSql(
        attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}sync_status'],
        )!,
      ),
    );
  }

  @override
  $SparePartsTable createAlias(String alias) {
    return $SparePartsTable(attachedDatabase, alias);
  }

  static TypeConverter<SyncStatus, String> $convertersyncStatus =
      const SyncStatusConverter();
}

class SparePart extends DataClass implements Insertable<SparePart> {
  final String uuid;
  final int id;
  final String partName;
  final String partType;
  final double price;
  final int mileageLimit;
  final int initialMileage;
  final DateTime installationDate;
  final int currentMileage;
  final bool warningStatus;
  final int replacementCount;
  final String notes;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;
  final SyncStatus syncStatus;
  const SparePart({
    required this.uuid,
    required this.id,
    required this.partName,
    required this.partType,
    required this.price,
    required this.mileageLimit,
    required this.initialMileage,
    required this.installationDate,
    required this.currentMileage,
    required this.warningStatus,
    required this.replacementCount,
    required this.notes,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    required this.syncStatus,
  });
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['uuid'] = Variable<String>(uuid);
    map['id'] = Variable<int>(id);
    map['part_name'] = Variable<String>(partName);
    map['part_type'] = Variable<String>(partType);
    map['price'] = Variable<double>(price);
    map['mileage_limit'] = Variable<int>(mileageLimit);
    map['initial_mileage'] = Variable<int>(initialMileage);
    map['installation_date'] = Variable<DateTime>(installationDate);
    map['current_mileage'] = Variable<int>(currentMileage);
    map['warning_status'] = Variable<bool>(warningStatus);
    map['replacement_count'] = Variable<int>(replacementCount);
    map['notes'] = Variable<String>(notes);
    map['created_at'] = Variable<DateTime>(createdAt);
    map['updated_at'] = Variable<DateTime>(updatedAt);
    if (!nullToAbsent || deletedAt != null) {
      map['deleted_at'] = Variable<DateTime>(deletedAt);
    }
    {
      map['sync_status'] = Variable<String>(
        $SparePartsTable.$convertersyncStatus.toSql(syncStatus),
      );
    }
    return map;
  }

  SparePartsCompanion toCompanion(bool nullToAbsent) {
    return SparePartsCompanion(
      uuid: Value(uuid),
      id: Value(id),
      partName: Value(partName),
      partType: Value(partType),
      price: Value(price),
      mileageLimit: Value(mileageLimit),
      initialMileage: Value(initialMileage),
      installationDate: Value(installationDate),
      currentMileage: Value(currentMileage),
      warningStatus: Value(warningStatus),
      replacementCount: Value(replacementCount),
      notes: Value(notes),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
      deletedAt: deletedAt == null && nullToAbsent
          ? const Value.absent()
          : Value(deletedAt),
      syncStatus: Value(syncStatus),
    );
  }

  factory SparePart.fromJson(
    Map<String, dynamic> json, {
    ValueSerializer? serializer,
  }) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return SparePart(
      uuid: serializer.fromJson<String>(json['uuid']),
      id: serializer.fromJson<int>(json['id']),
      partName: serializer.fromJson<String>(json['partName']),
      partType: serializer.fromJson<String>(json['partType']),
      price: serializer.fromJson<double>(json['price']),
      mileageLimit: serializer.fromJson<int>(json['mileageLimit']),
      initialMileage: serializer.fromJson<int>(json['initialMileage']),
      installationDate: serializer.fromJson<DateTime>(json['installationDate']),
      currentMileage: serializer.fromJson<int>(json['currentMileage']),
      warningStatus: serializer.fromJson<bool>(json['warningStatus']),
      replacementCount: serializer.fromJson<int>(json['replacementCount']),
      notes: serializer.fromJson<String>(json['notes']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      updatedAt: serializer.fromJson<DateTime>(json['updatedAt']),
      deletedAt: serializer.fromJson<DateTime?>(json['deletedAt']),
      syncStatus: serializer.fromJson<SyncStatus>(json['syncStatus']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'uuid': serializer.toJson<String>(uuid),
      'id': serializer.toJson<int>(id),
      'partName': serializer.toJson<String>(partName),
      'partType': serializer.toJson<String>(partType),
      'price': serializer.toJson<double>(price),
      'mileageLimit': serializer.toJson<int>(mileageLimit),
      'initialMileage': serializer.toJson<int>(initialMileage),
      'installationDate': serializer.toJson<DateTime>(installationDate),
      'currentMileage': serializer.toJson<int>(currentMileage),
      'warningStatus': serializer.toJson<bool>(warningStatus),
      'replacementCount': serializer.toJson<int>(replacementCount),
      'notes': serializer.toJson<String>(notes),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'updatedAt': serializer.toJson<DateTime>(updatedAt),
      'deletedAt': serializer.toJson<DateTime?>(deletedAt),
      'syncStatus': serializer.toJson<SyncStatus>(syncStatus),
    };
  }

  SparePart copyWith({
    String? uuid,
    int? id,
    String? partName,
    String? partType,
    double? price,
    int? mileageLimit,
    int? initialMileage,
    DateTime? installationDate,
    int? currentMileage,
    bool? warningStatus,
    int? replacementCount,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    Value<DateTime?> deletedAt = const Value.absent(),
    SyncStatus? syncStatus,
  }) => SparePart(
    uuid: uuid ?? this.uuid,
    id: id ?? this.id,
    partName: partName ?? this.partName,
    partType: partType ?? this.partType,
    price: price ?? this.price,
    mileageLimit: mileageLimit ?? this.mileageLimit,
    initialMileage: initialMileage ?? this.initialMileage,
    installationDate: installationDate ?? this.installationDate,
    currentMileage: currentMileage ?? this.currentMileage,
    warningStatus: warningStatus ?? this.warningStatus,
    replacementCount: replacementCount ?? this.replacementCount,
    notes: notes ?? this.notes,
    createdAt: createdAt ?? this.createdAt,
    updatedAt: updatedAt ?? this.updatedAt,
    deletedAt: deletedAt.present ? deletedAt.value : this.deletedAt,
    syncStatus: syncStatus ?? this.syncStatus,
  );
  SparePart copyWithCompanion(SparePartsCompanion data) {
    return SparePart(
      uuid: data.uuid.present ? data.uuid.value : this.uuid,
      id: data.id.present ? data.id.value : this.id,
      partName: data.partName.present ? data.partName.value : this.partName,
      partType: data.partType.present ? data.partType.value : this.partType,
      price: data.price.present ? data.price.value : this.price,
      mileageLimit: data.mileageLimit.present
          ? data.mileageLimit.value
          : this.mileageLimit,
      initialMileage: data.initialMileage.present
          ? data.initialMileage.value
          : this.initialMileage,
      installationDate: data.installationDate.present
          ? data.installationDate.value
          : this.installationDate,
      currentMileage: data.currentMileage.present
          ? data.currentMileage.value
          : this.currentMileage,
      warningStatus: data.warningStatus.present
          ? data.warningStatus.value
          : this.warningStatus,
      replacementCount: data.replacementCount.present
          ? data.replacementCount.value
          : this.replacementCount,
      notes: data.notes.present ? data.notes.value : this.notes,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
      deletedAt: data.deletedAt.present ? data.deletedAt.value : this.deletedAt,
      syncStatus: data.syncStatus.present
          ? data.syncStatus.value
          : this.syncStatus,
    );
  }

  @override
  String toString() {
    return (StringBuffer('SparePart(')
          ..write('uuid: $uuid, ')
          ..write('id: $id, ')
          ..write('partName: $partName, ')
          ..write('partType: $partType, ')
          ..write('price: $price, ')
          ..write('mileageLimit: $mileageLimit, ')
          ..write('initialMileage: $initialMileage, ')
          ..write('installationDate: $installationDate, ')
          ..write('currentMileage: $currentMileage, ')
          ..write('warningStatus: $warningStatus, ')
          ..write('replacementCount: $replacementCount, ')
          ..write('notes: $notes, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('deletedAt: $deletedAt, ')
          ..write('syncStatus: $syncStatus')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
    uuid,
    id,
    partName,
    partType,
    price,
    mileageLimit,
    initialMileage,
    installationDate,
    currentMileage,
    warningStatus,
    replacementCount,
    notes,
    createdAt,
    updatedAt,
    deletedAt,
    syncStatus,
  );
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is SparePart &&
          other.uuid == this.uuid &&
          other.id == this.id &&
          other.partName == this.partName &&
          other.partType == this.partType &&
          other.price == this.price &&
          other.mileageLimit == this.mileageLimit &&
          other.initialMileage == this.initialMileage &&
          other.installationDate == this.installationDate &&
          other.currentMileage == this.currentMileage &&
          other.warningStatus == this.warningStatus &&
          other.replacementCount == this.replacementCount &&
          other.notes == this.notes &&
          other.createdAt == this.createdAt &&
          other.updatedAt == this.updatedAt &&
          other.deletedAt == this.deletedAt &&
          other.syncStatus == this.syncStatus);
}

class SparePartsCompanion extends UpdateCompanion<SparePart> {
  final Value<String> uuid;
  final Value<int> id;
  final Value<String> partName;
  final Value<String> partType;
  final Value<double> price;
  final Value<int> mileageLimit;
  final Value<int> initialMileage;
  final Value<DateTime> installationDate;
  final Value<int> currentMileage;
  final Value<bool> warningStatus;
  final Value<int> replacementCount;
  final Value<String> notes;
  final Value<DateTime> createdAt;
  final Value<DateTime> updatedAt;
  final Value<DateTime?> deletedAt;
  final Value<SyncStatus> syncStatus;
  const SparePartsCompanion({
    this.uuid = const Value.absent(),
    this.id = const Value.absent(),
    this.partName = const Value.absent(),
    this.partType = const Value.absent(),
    this.price = const Value.absent(),
    this.mileageLimit = const Value.absent(),
    this.initialMileage = const Value.absent(),
    this.installationDate = const Value.absent(),
    this.currentMileage = const Value.absent(),
    this.warningStatus = const Value.absent(),
    this.replacementCount = const Value.absent(),
    this.notes = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.deletedAt = const Value.absent(),
    this.syncStatus = const Value.absent(),
  });
  SparePartsCompanion.insert({
    this.uuid = const Value.absent(),
    this.id = const Value.absent(),
    required String partName,
    required String partType,
    required double price,
    required int mileageLimit,
    required int initialMileage,
    this.installationDate = const Value.absent(),
    this.currentMileage = const Value.absent(),
    this.warningStatus = const Value.absent(),
    this.replacementCount = const Value.absent(),
    this.notes = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.deletedAt = const Value.absent(),
    this.syncStatus = const Value.absent(),
  }) : partName = Value(partName),
       partType = Value(partType),
       price = Value(price),
       mileageLimit = Value(mileageLimit),
       initialMileage = Value(initialMileage);
  static Insertable<SparePart> custom({
    Expression<String>? uuid,
    Expression<int>? id,
    Expression<String>? partName,
    Expression<String>? partType,
    Expression<double>? price,
    Expression<int>? mileageLimit,
    Expression<int>? initialMileage,
    Expression<DateTime>? installationDate,
    Expression<int>? currentMileage,
    Expression<bool>? warningStatus,
    Expression<int>? replacementCount,
    Expression<String>? notes,
    Expression<DateTime>? createdAt,
    Expression<DateTime>? updatedAt,
    Expression<DateTime>? deletedAt,
    Expression<String>? syncStatus,
  }) {
    return RawValuesInsertable({
      if (uuid != null) 'uuid': uuid,
      if (id != null) 'id': id,
      if (partName != null) 'part_name': partName,
      if (partType != null) 'part_type': partType,
      if (price != null) 'price': price,
      if (mileageLimit != null) 'mileage_limit': mileageLimit,
      if (initialMileage != null) 'initial_mileage': initialMileage,
      if (installationDate != null) 'installation_date': installationDate,
      if (currentMileage != null) 'current_mileage': currentMileage,
      if (warningStatus != null) 'warning_status': warningStatus,
      if (replacementCount != null) 'replacement_count': replacementCount,
      if (notes != null) 'notes': notes,
      if (createdAt != null) 'created_at': createdAt,
      if (updatedAt != null) 'updated_at': updatedAt,
      if (deletedAt != null) 'deleted_at': deletedAt,
      if (syncStatus != null) 'sync_status': syncStatus,
    });
  }

  SparePartsCompanion copyWith({
    Value<String>? uuid,
    Value<int>? id,
    Value<String>? partName,
    Value<String>? partType,
    Value<double>? price,
    Value<int>? mileageLimit,
    Value<int>? initialMileage,
    Value<DateTime>? installationDate,
    Value<int>? currentMileage,
    Value<bool>? warningStatus,
    Value<int>? replacementCount,
    Value<String>? notes,
    Value<DateTime>? createdAt,
    Value<DateTime>? updatedAt,
    Value<DateTime?>? deletedAt,
    Value<SyncStatus>? syncStatus,
  }) {
    return SparePartsCompanion(
      uuid: uuid ?? this.uuid,
      id: id ?? this.id,
      partName: partName ?? this.partName,
      partType: partType ?? this.partType,
      price: price ?? this.price,
      mileageLimit: mileageLimit ?? this.mileageLimit,
      initialMileage: initialMileage ?? this.initialMileage,
      installationDate: installationDate ?? this.installationDate,
      currentMileage: currentMileage ?? this.currentMileage,
      warningStatus: warningStatus ?? this.warningStatus,
      replacementCount: replacementCount ?? this.replacementCount,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      syncStatus: syncStatus ?? this.syncStatus,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (uuid.present) {
      map['uuid'] = Variable<String>(uuid.value);
    }
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (partName.present) {
      map['part_name'] = Variable<String>(partName.value);
    }
    if (partType.present) {
      map['part_type'] = Variable<String>(partType.value);
    }
    if (price.present) {
      map['price'] = Variable<double>(price.value);
    }
    if (mileageLimit.present) {
      map['mileage_limit'] = Variable<int>(mileageLimit.value);
    }
    if (initialMileage.present) {
      map['initial_mileage'] = Variable<int>(initialMileage.value);
    }
    if (installationDate.present) {
      map['installation_date'] = Variable<DateTime>(installationDate.value);
    }
    if (currentMileage.present) {
      map['current_mileage'] = Variable<int>(currentMileage.value);
    }
    if (warningStatus.present) {
      map['warning_status'] = Variable<bool>(warningStatus.value);
    }
    if (replacementCount.present) {
      map['replacement_count'] = Variable<int>(replacementCount.value);
    }
    if (notes.present) {
      map['notes'] = Variable<String>(notes.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<DateTime>(updatedAt.value);
    }
    if (deletedAt.present) {
      map['deleted_at'] = Variable<DateTime>(deletedAt.value);
    }
    if (syncStatus.present) {
      map['sync_status'] = Variable<String>(
        $SparePartsTable.$convertersyncStatus.toSql(syncStatus.value),
      );
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('SparePartsCompanion(')
          ..write('uuid: $uuid, ')
          ..write('id: $id, ')
          ..write('partName: $partName, ')
          ..write('partType: $partType, ')
          ..write('price: $price, ')
          ..write('mileageLimit: $mileageLimit, ')
          ..write('initialMileage: $initialMileage, ')
          ..write('installationDate: $installationDate, ')
          ..write('currentMileage: $currentMileage, ')
          ..write('warningStatus: $warningStatus, ')
          ..write('replacementCount: $replacementCount, ')
          ..write('notes: $notes, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('deletedAt: $deletedAt, ')
          ..write('syncStatus: $syncStatus')
          ..write(')'))
        .toString();
  }
}

class $SparePartsHistoryTable extends SparePartsHistory
    with TableInfo<$SparePartsHistoryTable, SparePartsHistoryData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $SparePartsHistoryTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _uuidMeta = const VerificationMeta('uuid');
  @override
  late final GeneratedColumn<String> uuid = GeneratedColumn<String>(
    'uuid',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: false,
    clientDefault: () => const Uuid().v4(),
  );
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
    'id',
    aliasedName,
    false,
    hasAutoIncrement: true,
    type: DriftSqlType.int,
    requiredDuringInsert: false,
    defaultConstraints: GeneratedColumn.constraintIsAlways(
      'PRIMARY KEY AUTOINCREMENT',
    ),
  );
  static const VerificationMeta _partNameMeta = const VerificationMeta(
    'partName',
  );
  @override
  late final GeneratedColumn<String> partName = GeneratedColumn<String>(
    'part_name',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _partTypeMeta = const VerificationMeta(
    'partType',
  );
  @override
  late final GeneratedColumn<String> partType = GeneratedColumn<String>(
    'part_type',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _priceMeta = const VerificationMeta('price');
  @override
  late final GeneratedColumn<double> price = GeneratedColumn<double>(
    'price',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  @override
  late final GeneratedColumnWithTypeConverter<DateTime, DateTime>
  replacementDate = GeneratedColumn<DateTime>(
    'replacement_date',
    aliasedName,
    false,
    type: DriftSqlType.dateTime,
    requiredDuringInsert: true,
  ).withConverter<DateTime>($SparePartsHistoryTable.$converterreplacementDate);
  static const VerificationMeta _mileageAtReplacementMeta =
      const VerificationMeta('mileageAtReplacement');
  @override
  late final GeneratedColumn<int> mileageAtReplacement = GeneratedColumn<int>(
    'mileage_at_replacement',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _sparePartIdMeta = const VerificationMeta(
    'sparePartId',
  );
  @override
  late final GeneratedColumn<int> sparePartId = GeneratedColumn<int>(
    'spare_part_id',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: true,
    defaultConstraints: GeneratedColumn.constraintIsAlways(
      'REFERENCES spare_parts (id)',
    ),
  );
  @override
  late final GeneratedColumnWithTypeConverter<DateTime, DateTime>
  installationDate = GeneratedColumn<DateTime>(
    'installation_date',
    aliasedName,
    false,
    type: DriftSqlType.dateTime,
    requiredDuringInsert: true,
  ).withConverter<DateTime>($SparePartsHistoryTable.$converterinstallationDate);
  static const VerificationMeta _initialMileageMeta = const VerificationMeta(
    'initialMileage',
  );
  @override
  late final GeneratedColumn<int> initialMileage = GeneratedColumn<int>(
    'initial_mileage',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _replacementReasonMeta = const VerificationMeta(
    'replacementReason',
  );
  @override
  late final GeneratedColumn<String> replacementReason =
      GeneratedColumn<String>(
        'replacement_reason',
        aliasedName,
        false,
        type: DriftSqlType.string,
        requiredDuringInsert: false,
        defaultValue: const Constant('Regular maintenance'),
      );
  static const VerificationMeta _replacedByPartIdMeta = const VerificationMeta(
    'replacedByPartId',
  );
  @override
  late final GeneratedColumn<int> replacedByPartId = GeneratedColumn<int>(
    'replaced_by_part_id',
    aliasedName,
    true,
    type: DriftSqlType.int,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _replacementCountMeta = const VerificationMeta(
    'replacementCount',
  );
  @override
  late final GeneratedColumn<int> replacementCount = GeneratedColumn<int>(
    'replacement_count',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: false,
    defaultValue: const Constant(1),
  );
  static const VerificationMeta _usageDaysMeta = const VerificationMeta(
    'usageDays',
  );
  @override
  late final GeneratedColumn<int> usageDays = GeneratedColumn<int>(
    'usage_days',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: false,
    defaultValue: const Constant(0),
  );
  static const VerificationMeta _usageMileageMeta = const VerificationMeta(
    'usageMileage',
  );
  @override
  late final GeneratedColumn<int> usageMileage = GeneratedColumn<int>(
    'usage_mileage',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: false,
    defaultValue: const Constant(0),
  );
  static const VerificationMeta _notesMeta = const VerificationMeta('notes');
  @override
  late final GeneratedColumn<String> notes = GeneratedColumn<String>(
    'notes',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: false,
    defaultValue: const Constant(''),
  );
  static const VerificationMeta _createdAtMeta = const VerificationMeta(
    'createdAt',
  );
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
    'created_at',
    aliasedName,
    false,
    type: DriftSqlType.dateTime,
    requiredDuringInsert: false,
    clientDefault: () => DateTime.now().toUtc(),
  );
  static const VerificationMeta _updatedAtMeta = const VerificationMeta(
    'updatedAt',
  );
  @override
  late final GeneratedColumn<DateTime> updatedAt = GeneratedColumn<DateTime>(
    'updated_at',
    aliasedName,
    false,
    type: DriftSqlType.dateTime,
    requiredDuringInsert: false,
    clientDefault: () => DateTime.now().toUtc(),
  );
  static const VerificationMeta _deletedAtMeta = const VerificationMeta(
    'deletedAt',
  );
  @override
  late final GeneratedColumn<DateTime> deletedAt = GeneratedColumn<DateTime>(
    'deleted_at',
    aliasedName,
    true,
    type: DriftSqlType.dateTime,
    requiredDuringInsert: false,
  );
  @override
  late final GeneratedColumnWithTypeConverter<SyncStatus, String> syncStatus =
      GeneratedColumn<String>(
        'sync_status',
        aliasedName,
        false,
        type: DriftSqlType.string,
        requiredDuringInsert: false,
        defaultValue: const Constant('pendingUpload'),
      ).withConverter<SyncStatus>($SparePartsHistoryTable.$convertersyncStatus);
  @override
  List<GeneratedColumn> get $columns => [
    uuid,
    id,
    partName,
    partType,
    price,
    replacementDate,
    mileageAtReplacement,
    sparePartId,
    installationDate,
    initialMileage,
    replacementReason,
    replacedByPartId,
    replacementCount,
    usageDays,
    usageMileage,
    notes,
    createdAt,
    updatedAt,
    deletedAt,
    syncStatus,
  ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'spare_parts_history';
  @override
  VerificationContext validateIntegrity(
    Insertable<SparePartsHistoryData> instance, {
    bool isInserting = false,
  }) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('uuid')) {
      context.handle(
        _uuidMeta,
        uuid.isAcceptableOrUnknown(data['uuid']!, _uuidMeta),
      );
    }
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('part_name')) {
      context.handle(
        _partNameMeta,
        partName.isAcceptableOrUnknown(data['part_name']!, _partNameMeta),
      );
    } else if (isInserting) {
      context.missing(_partNameMeta);
    }
    if (data.containsKey('part_type')) {
      context.handle(
        _partTypeMeta,
        partType.isAcceptableOrUnknown(data['part_type']!, _partTypeMeta),
      );
    } else if (isInserting) {
      context.missing(_partTypeMeta);
    }
    if (data.containsKey('price')) {
      context.handle(
        _priceMeta,
        price.isAcceptableOrUnknown(data['price']!, _priceMeta),
      );
    } else if (isInserting) {
      context.missing(_priceMeta);
    }
    if (data.containsKey('mileage_at_replacement')) {
      context.handle(
        _mileageAtReplacementMeta,
        mileageAtReplacement.isAcceptableOrUnknown(
          data['mileage_at_replacement']!,
          _mileageAtReplacementMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_mileageAtReplacementMeta);
    }
    if (data.containsKey('spare_part_id')) {
      context.handle(
        _sparePartIdMeta,
        sparePartId.isAcceptableOrUnknown(
          data['spare_part_id']!,
          _sparePartIdMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_sparePartIdMeta);
    }
    if (data.containsKey('initial_mileage')) {
      context.handle(
        _initialMileageMeta,
        initialMileage.isAcceptableOrUnknown(
          data['initial_mileage']!,
          _initialMileageMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_initialMileageMeta);
    }
    if (data.containsKey('replacement_reason')) {
      context.handle(
        _replacementReasonMeta,
        replacementReason.isAcceptableOrUnknown(
          data['replacement_reason']!,
          _replacementReasonMeta,
        ),
      );
    }
    if (data.containsKey('replaced_by_part_id')) {
      context.handle(
        _replacedByPartIdMeta,
        replacedByPartId.isAcceptableOrUnknown(
          data['replaced_by_part_id']!,
          _replacedByPartIdMeta,
        ),
      );
    }
    if (data.containsKey('replacement_count')) {
      context.handle(
        _replacementCountMeta,
        replacementCount.isAcceptableOrUnknown(
          data['replacement_count']!,
          _replacementCountMeta,
        ),
      );
    }
    if (data.containsKey('usage_days')) {
      context.handle(
        _usageDaysMeta,
        usageDays.isAcceptableOrUnknown(data['usage_days']!, _usageDaysMeta),
      );
    }
    if (data.containsKey('usage_mileage')) {
      context.handle(
        _usageMileageMeta,
        usageMileage.isAcceptableOrUnknown(
          data['usage_mileage']!,
          _usageMileageMeta,
        ),
      );
    }
    if (data.containsKey('notes')) {
      context.handle(
        _notesMeta,
        notes.isAcceptableOrUnknown(data['notes']!, _notesMeta),
      );
    }
    if (data.containsKey('created_at')) {
      context.handle(
        _createdAtMeta,
        createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta),
      );
    }
    if (data.containsKey('updated_at')) {
      context.handle(
        _updatedAtMeta,
        updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta),
      );
    }
    if (data.containsKey('deleted_at')) {
      context.handle(
        _deletedAtMeta,
        deletedAt.isAcceptableOrUnknown(data['deleted_at']!, _deletedAtMeta),
      );
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  SparePartsHistoryData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return SparePartsHistoryData(
      uuid: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}uuid'],
      )!,
      id: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}id'],
      )!,
      partName: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}part_name'],
      )!,
      partType: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}part_type'],
      )!,
      price: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}price'],
      )!,
      replacementDate: $SparePartsHistoryTable.$converterreplacementDate
          .fromSql(
            attachedDatabase.typeMapping.read(
              DriftSqlType.dateTime,
              data['${effectivePrefix}replacement_date'],
            )!,
          ),
      mileageAtReplacement: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}mileage_at_replacement'],
      )!,
      sparePartId: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}spare_part_id'],
      )!,
      installationDate: $SparePartsHistoryTable.$converterinstallationDate
          .fromSql(
            attachedDatabase.typeMapping.read(
              DriftSqlType.dateTime,
              data['${effectivePrefix}installation_date'],
            )!,
          ),
      initialMileage: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}initial_mileage'],
      )!,
      replacementReason: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}replacement_reason'],
      )!,
      replacedByPartId: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}replaced_by_part_id'],
      ),
      replacementCount: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}replacement_count'],
      )!,
      usageDays: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}usage_days'],
      )!,
      usageMileage: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}usage_mileage'],
      )!,
      notes: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}notes'],
      )!,
      createdAt: attachedDatabase.typeMapping.read(
        DriftSqlType.dateTime,
        data['${effectivePrefix}created_at'],
      )!,
      updatedAt: attachedDatabase.typeMapping.read(
        DriftSqlType.dateTime,
        data['${effectivePrefix}updated_at'],
      )!,
      deletedAt: attachedDatabase.typeMapping.read(
        DriftSqlType.dateTime,
        data['${effectivePrefix}deleted_at'],
      ),
      syncStatus: $SparePartsHistoryTable.$convertersyncStatus.fromSql(
        attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}sync_status'],
        )!,
      ),
    );
  }

  @override
  $SparePartsHistoryTable createAlias(String alias) {
    return $SparePartsHistoryTable(attachedDatabase, alias);
  }

  static TypeConverter<DateTime, DateTime> $converterreplacementDate =
      const UtcDateTimeConverter();
  static TypeConverter<DateTime, DateTime> $converterinstallationDate =
      const UtcDateTimeConverter();
  static TypeConverter<SyncStatus, String> $convertersyncStatus =
      const SyncStatusConverter();
}

class SparePartsHistoryData extends DataClass
    implements Insertable<SparePartsHistoryData> {
  final String uuid;
  final int id;
  final String partName;
  final String partType;
  final double price;
  final DateTime replacementDate;
  final int mileageAtReplacement;
  final int sparePartId;
  final DateTime installationDate;
  final int initialMileage;
  final String replacementReason;
  final int? replacedByPartId;
  final int replacementCount;
  final int usageDays;
  final int usageMileage;
  final String notes;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;
  final SyncStatus syncStatus;
  const SparePartsHistoryData({
    required this.uuid,
    required this.id,
    required this.partName,
    required this.partType,
    required this.price,
    required this.replacementDate,
    required this.mileageAtReplacement,
    required this.sparePartId,
    required this.installationDate,
    required this.initialMileage,
    required this.replacementReason,
    this.replacedByPartId,
    required this.replacementCount,
    required this.usageDays,
    required this.usageMileage,
    required this.notes,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    required this.syncStatus,
  });
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['uuid'] = Variable<String>(uuid);
    map['id'] = Variable<int>(id);
    map['part_name'] = Variable<String>(partName);
    map['part_type'] = Variable<String>(partType);
    map['price'] = Variable<double>(price);
    {
      map['replacement_date'] = Variable<DateTime>(
        $SparePartsHistoryTable.$converterreplacementDate.toSql(
          replacementDate,
        ),
      );
    }
    map['mileage_at_replacement'] = Variable<int>(mileageAtReplacement);
    map['spare_part_id'] = Variable<int>(sparePartId);
    {
      map['installation_date'] = Variable<DateTime>(
        $SparePartsHistoryTable.$converterinstallationDate.toSql(
          installationDate,
        ),
      );
    }
    map['initial_mileage'] = Variable<int>(initialMileage);
    map['replacement_reason'] = Variable<String>(replacementReason);
    if (!nullToAbsent || replacedByPartId != null) {
      map['replaced_by_part_id'] = Variable<int>(replacedByPartId);
    }
    map['replacement_count'] = Variable<int>(replacementCount);
    map['usage_days'] = Variable<int>(usageDays);
    map['usage_mileage'] = Variable<int>(usageMileage);
    map['notes'] = Variable<String>(notes);
    map['created_at'] = Variable<DateTime>(createdAt);
    map['updated_at'] = Variable<DateTime>(updatedAt);
    if (!nullToAbsent || deletedAt != null) {
      map['deleted_at'] = Variable<DateTime>(deletedAt);
    }
    {
      map['sync_status'] = Variable<String>(
        $SparePartsHistoryTable.$convertersyncStatus.toSql(syncStatus),
      );
    }
    return map;
  }

  SparePartsHistoryCompanion toCompanion(bool nullToAbsent) {
    return SparePartsHistoryCompanion(
      uuid: Value(uuid),
      id: Value(id),
      partName: Value(partName),
      partType: Value(partType),
      price: Value(price),
      replacementDate: Value(replacementDate),
      mileageAtReplacement: Value(mileageAtReplacement),
      sparePartId: Value(sparePartId),
      installationDate: Value(installationDate),
      initialMileage: Value(initialMileage),
      replacementReason: Value(replacementReason),
      replacedByPartId: replacedByPartId == null && nullToAbsent
          ? const Value.absent()
          : Value(replacedByPartId),
      replacementCount: Value(replacementCount),
      usageDays: Value(usageDays),
      usageMileage: Value(usageMileage),
      notes: Value(notes),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
      deletedAt: deletedAt == null && nullToAbsent
          ? const Value.absent()
          : Value(deletedAt),
      syncStatus: Value(syncStatus),
    );
  }

  factory SparePartsHistoryData.fromJson(
    Map<String, dynamic> json, {
    ValueSerializer? serializer,
  }) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return SparePartsHistoryData(
      uuid: serializer.fromJson<String>(json['uuid']),
      id: serializer.fromJson<int>(json['id']),
      partName: serializer.fromJson<String>(json['partName']),
      partType: serializer.fromJson<String>(json['partType']),
      price: serializer.fromJson<double>(json['price']),
      replacementDate: serializer.fromJson<DateTime>(json['replacementDate']),
      mileageAtReplacement: serializer.fromJson<int>(
        json['mileageAtReplacement'],
      ),
      sparePartId: serializer.fromJson<int>(json['sparePartId']),
      installationDate: serializer.fromJson<DateTime>(json['installationDate']),
      initialMileage: serializer.fromJson<int>(json['initialMileage']),
      replacementReason: serializer.fromJson<String>(json['replacementReason']),
      replacedByPartId: serializer.fromJson<int?>(json['replacedByPartId']),
      replacementCount: serializer.fromJson<int>(json['replacementCount']),
      usageDays: serializer.fromJson<int>(json['usageDays']),
      usageMileage: serializer.fromJson<int>(json['usageMileage']),
      notes: serializer.fromJson<String>(json['notes']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      updatedAt: serializer.fromJson<DateTime>(json['updatedAt']),
      deletedAt: serializer.fromJson<DateTime?>(json['deletedAt']),
      syncStatus: serializer.fromJson<SyncStatus>(json['syncStatus']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'uuid': serializer.toJson<String>(uuid),
      'id': serializer.toJson<int>(id),
      'partName': serializer.toJson<String>(partName),
      'partType': serializer.toJson<String>(partType),
      'price': serializer.toJson<double>(price),
      'replacementDate': serializer.toJson<DateTime>(replacementDate),
      'mileageAtReplacement': serializer.toJson<int>(mileageAtReplacement),
      'sparePartId': serializer.toJson<int>(sparePartId),
      'installationDate': serializer.toJson<DateTime>(installationDate),
      'initialMileage': serializer.toJson<int>(initialMileage),
      'replacementReason': serializer.toJson<String>(replacementReason),
      'replacedByPartId': serializer.toJson<int?>(replacedByPartId),
      'replacementCount': serializer.toJson<int>(replacementCount),
      'usageDays': serializer.toJson<int>(usageDays),
      'usageMileage': serializer.toJson<int>(usageMileage),
      'notes': serializer.toJson<String>(notes),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'updatedAt': serializer.toJson<DateTime>(updatedAt),
      'deletedAt': serializer.toJson<DateTime?>(deletedAt),
      'syncStatus': serializer.toJson<SyncStatus>(syncStatus),
    };
  }

  SparePartsHistoryData copyWith({
    String? uuid,
    int? id,
    String? partName,
    String? partType,
    double? price,
    DateTime? replacementDate,
    int? mileageAtReplacement,
    int? sparePartId,
    DateTime? installationDate,
    int? initialMileage,
    String? replacementReason,
    Value<int?> replacedByPartId = const Value.absent(),
    int? replacementCount,
    int? usageDays,
    int? usageMileage,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    Value<DateTime?> deletedAt = const Value.absent(),
    SyncStatus? syncStatus,
  }) => SparePartsHistoryData(
    uuid: uuid ?? this.uuid,
    id: id ?? this.id,
    partName: partName ?? this.partName,
    partType: partType ?? this.partType,
    price: price ?? this.price,
    replacementDate: replacementDate ?? this.replacementDate,
    mileageAtReplacement: mileageAtReplacement ?? this.mileageAtReplacement,
    sparePartId: sparePartId ?? this.sparePartId,
    installationDate: installationDate ?? this.installationDate,
    initialMileage: initialMileage ?? this.initialMileage,
    replacementReason: replacementReason ?? this.replacementReason,
    replacedByPartId: replacedByPartId.present
        ? replacedByPartId.value
        : this.replacedByPartId,
    replacementCount: replacementCount ?? this.replacementCount,
    usageDays: usageDays ?? this.usageDays,
    usageMileage: usageMileage ?? this.usageMileage,
    notes: notes ?? this.notes,
    createdAt: createdAt ?? this.createdAt,
    updatedAt: updatedAt ?? this.updatedAt,
    deletedAt: deletedAt.present ? deletedAt.value : this.deletedAt,
    syncStatus: syncStatus ?? this.syncStatus,
  );
  SparePartsHistoryData copyWithCompanion(SparePartsHistoryCompanion data) {
    return SparePartsHistoryData(
      uuid: data.uuid.present ? data.uuid.value : this.uuid,
      id: data.id.present ? data.id.value : this.id,
      partName: data.partName.present ? data.partName.value : this.partName,
      partType: data.partType.present ? data.partType.value : this.partType,
      price: data.price.present ? data.price.value : this.price,
      replacementDate: data.replacementDate.present
          ? data.replacementDate.value
          : this.replacementDate,
      mileageAtReplacement: data.mileageAtReplacement.present
          ? data.mileageAtReplacement.value
          : this.mileageAtReplacement,
      sparePartId: data.sparePartId.present
          ? data.sparePartId.value
          : this.sparePartId,
      installationDate: data.installationDate.present
          ? data.installationDate.value
          : this.installationDate,
      initialMileage: data.initialMileage.present
          ? data.initialMileage.value
          : this.initialMileage,
      replacementReason: data.replacementReason.present
          ? data.replacementReason.value
          : this.replacementReason,
      replacedByPartId: data.replacedByPartId.present
          ? data.replacedByPartId.value
          : this.replacedByPartId,
      replacementCount: data.replacementCount.present
          ? data.replacementCount.value
          : this.replacementCount,
      usageDays: data.usageDays.present ? data.usageDays.value : this.usageDays,
      usageMileage: data.usageMileage.present
          ? data.usageMileage.value
          : this.usageMileage,
      notes: data.notes.present ? data.notes.value : this.notes,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
      deletedAt: data.deletedAt.present ? data.deletedAt.value : this.deletedAt,
      syncStatus: data.syncStatus.present
          ? data.syncStatus.value
          : this.syncStatus,
    );
  }

  @override
  String toString() {
    return (StringBuffer('SparePartsHistoryData(')
          ..write('uuid: $uuid, ')
          ..write('id: $id, ')
          ..write('partName: $partName, ')
          ..write('partType: $partType, ')
          ..write('price: $price, ')
          ..write('replacementDate: $replacementDate, ')
          ..write('mileageAtReplacement: $mileageAtReplacement, ')
          ..write('sparePartId: $sparePartId, ')
          ..write('installationDate: $installationDate, ')
          ..write('initialMileage: $initialMileage, ')
          ..write('replacementReason: $replacementReason, ')
          ..write('replacedByPartId: $replacedByPartId, ')
          ..write('replacementCount: $replacementCount, ')
          ..write('usageDays: $usageDays, ')
          ..write('usageMileage: $usageMileage, ')
          ..write('notes: $notes, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('deletedAt: $deletedAt, ')
          ..write('syncStatus: $syncStatus')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
    uuid,
    id,
    partName,
    partType,
    price,
    replacementDate,
    mileageAtReplacement,
    sparePartId,
    installationDate,
    initialMileage,
    replacementReason,
    replacedByPartId,
    replacementCount,
    usageDays,
    usageMileage,
    notes,
    createdAt,
    updatedAt,
    deletedAt,
    syncStatus,
  );
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is SparePartsHistoryData &&
          other.uuid == this.uuid &&
          other.id == this.id &&
          other.partName == this.partName &&
          other.partType == this.partType &&
          other.price == this.price &&
          other.replacementDate == this.replacementDate &&
          other.mileageAtReplacement == this.mileageAtReplacement &&
          other.sparePartId == this.sparePartId &&
          other.installationDate == this.installationDate &&
          other.initialMileage == this.initialMileage &&
          other.replacementReason == this.replacementReason &&
          other.replacedByPartId == this.replacedByPartId &&
          other.replacementCount == this.replacementCount &&
          other.usageDays == this.usageDays &&
          other.usageMileage == this.usageMileage &&
          other.notes == this.notes &&
          other.createdAt == this.createdAt &&
          other.updatedAt == this.updatedAt &&
          other.deletedAt == this.deletedAt &&
          other.syncStatus == this.syncStatus);
}

class SparePartsHistoryCompanion
    extends UpdateCompanion<SparePartsHistoryData> {
  final Value<String> uuid;
  final Value<int> id;
  final Value<String> partName;
  final Value<String> partType;
  final Value<double> price;
  final Value<DateTime> replacementDate;
  final Value<int> mileageAtReplacement;
  final Value<int> sparePartId;
  final Value<DateTime> installationDate;
  final Value<int> initialMileage;
  final Value<String> replacementReason;
  final Value<int?> replacedByPartId;
  final Value<int> replacementCount;
  final Value<int> usageDays;
  final Value<int> usageMileage;
  final Value<String> notes;
  final Value<DateTime> createdAt;
  final Value<DateTime> updatedAt;
  final Value<DateTime?> deletedAt;
  final Value<SyncStatus> syncStatus;
  const SparePartsHistoryCompanion({
    this.uuid = const Value.absent(),
    this.id = const Value.absent(),
    this.partName = const Value.absent(),
    this.partType = const Value.absent(),
    this.price = const Value.absent(),
    this.replacementDate = const Value.absent(),
    this.mileageAtReplacement = const Value.absent(),
    this.sparePartId = const Value.absent(),
    this.installationDate = const Value.absent(),
    this.initialMileage = const Value.absent(),
    this.replacementReason = const Value.absent(),
    this.replacedByPartId = const Value.absent(),
    this.replacementCount = const Value.absent(),
    this.usageDays = const Value.absent(),
    this.usageMileage = const Value.absent(),
    this.notes = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.deletedAt = const Value.absent(),
    this.syncStatus = const Value.absent(),
  });
  SparePartsHistoryCompanion.insert({
    this.uuid = const Value.absent(),
    this.id = const Value.absent(),
    required String partName,
    required String partType,
    required double price,
    required DateTime replacementDate,
    required int mileageAtReplacement,
    required int sparePartId,
    required DateTime installationDate,
    required int initialMileage,
    this.replacementReason = const Value.absent(),
    this.replacedByPartId = const Value.absent(),
    this.replacementCount = const Value.absent(),
    this.usageDays = const Value.absent(),
    this.usageMileage = const Value.absent(),
    this.notes = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.deletedAt = const Value.absent(),
    this.syncStatus = const Value.absent(),
  }) : partName = Value(partName),
       partType = Value(partType),
       price = Value(price),
       replacementDate = Value(replacementDate),
       mileageAtReplacement = Value(mileageAtReplacement),
       sparePartId = Value(sparePartId),
       installationDate = Value(installationDate),
       initialMileage = Value(initialMileage);
  static Insertable<SparePartsHistoryData> custom({
    Expression<String>? uuid,
    Expression<int>? id,
    Expression<String>? partName,
    Expression<String>? partType,
    Expression<double>? price,
    Expression<DateTime>? replacementDate,
    Expression<int>? mileageAtReplacement,
    Expression<int>? sparePartId,
    Expression<DateTime>? installationDate,
    Expression<int>? initialMileage,
    Expression<String>? replacementReason,
    Expression<int>? replacedByPartId,
    Expression<int>? replacementCount,
    Expression<int>? usageDays,
    Expression<int>? usageMileage,
    Expression<String>? notes,
    Expression<DateTime>? createdAt,
    Expression<DateTime>? updatedAt,
    Expression<DateTime>? deletedAt,
    Expression<String>? syncStatus,
  }) {
    return RawValuesInsertable({
      if (uuid != null) 'uuid': uuid,
      if (id != null) 'id': id,
      if (partName != null) 'part_name': partName,
      if (partType != null) 'part_type': partType,
      if (price != null) 'price': price,
      if (replacementDate != null) 'replacement_date': replacementDate,
      if (mileageAtReplacement != null)
        'mileage_at_replacement': mileageAtReplacement,
      if (sparePartId != null) 'spare_part_id': sparePartId,
      if (installationDate != null) 'installation_date': installationDate,
      if (initialMileage != null) 'initial_mileage': initialMileage,
      if (replacementReason != null) 'replacement_reason': replacementReason,
      if (replacedByPartId != null) 'replaced_by_part_id': replacedByPartId,
      if (replacementCount != null) 'replacement_count': replacementCount,
      if (usageDays != null) 'usage_days': usageDays,
      if (usageMileage != null) 'usage_mileage': usageMileage,
      if (notes != null) 'notes': notes,
      if (createdAt != null) 'created_at': createdAt,
      if (updatedAt != null) 'updated_at': updatedAt,
      if (deletedAt != null) 'deleted_at': deletedAt,
      if (syncStatus != null) 'sync_status': syncStatus,
    });
  }

  SparePartsHistoryCompanion copyWith({
    Value<String>? uuid,
    Value<int>? id,
    Value<String>? partName,
    Value<String>? partType,
    Value<double>? price,
    Value<DateTime>? replacementDate,
    Value<int>? mileageAtReplacement,
    Value<int>? sparePartId,
    Value<DateTime>? installationDate,
    Value<int>? initialMileage,
    Value<String>? replacementReason,
    Value<int?>? replacedByPartId,
    Value<int>? replacementCount,
    Value<int>? usageDays,
    Value<int>? usageMileage,
    Value<String>? notes,
    Value<DateTime>? createdAt,
    Value<DateTime>? updatedAt,
    Value<DateTime?>? deletedAt,
    Value<SyncStatus>? syncStatus,
  }) {
    return SparePartsHistoryCompanion(
      uuid: uuid ?? this.uuid,
      id: id ?? this.id,
      partName: partName ?? this.partName,
      partType: partType ?? this.partType,
      price: price ?? this.price,
      replacementDate: replacementDate ?? this.replacementDate,
      mileageAtReplacement: mileageAtReplacement ?? this.mileageAtReplacement,
      sparePartId: sparePartId ?? this.sparePartId,
      installationDate: installationDate ?? this.installationDate,
      initialMileage: initialMileage ?? this.initialMileage,
      replacementReason: replacementReason ?? this.replacementReason,
      replacedByPartId: replacedByPartId ?? this.replacedByPartId,
      replacementCount: replacementCount ?? this.replacementCount,
      usageDays: usageDays ?? this.usageDays,
      usageMileage: usageMileage ?? this.usageMileage,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      syncStatus: syncStatus ?? this.syncStatus,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (uuid.present) {
      map['uuid'] = Variable<String>(uuid.value);
    }
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (partName.present) {
      map['part_name'] = Variable<String>(partName.value);
    }
    if (partType.present) {
      map['part_type'] = Variable<String>(partType.value);
    }
    if (price.present) {
      map['price'] = Variable<double>(price.value);
    }
    if (replacementDate.present) {
      map['replacement_date'] = Variable<DateTime>(
        $SparePartsHistoryTable.$converterreplacementDate.toSql(
          replacementDate.value,
        ),
      );
    }
    if (mileageAtReplacement.present) {
      map['mileage_at_replacement'] = Variable<int>(mileageAtReplacement.value);
    }
    if (sparePartId.present) {
      map['spare_part_id'] = Variable<int>(sparePartId.value);
    }
    if (installationDate.present) {
      map['installation_date'] = Variable<DateTime>(
        $SparePartsHistoryTable.$converterinstallationDate.toSql(
          installationDate.value,
        ),
      );
    }
    if (initialMileage.present) {
      map['initial_mileage'] = Variable<int>(initialMileage.value);
    }
    if (replacementReason.present) {
      map['replacement_reason'] = Variable<String>(replacementReason.value);
    }
    if (replacedByPartId.present) {
      map['replaced_by_part_id'] = Variable<int>(replacedByPartId.value);
    }
    if (replacementCount.present) {
      map['replacement_count'] = Variable<int>(replacementCount.value);
    }
    if (usageDays.present) {
      map['usage_days'] = Variable<int>(usageDays.value);
    }
    if (usageMileage.present) {
      map['usage_mileage'] = Variable<int>(usageMileage.value);
    }
    if (notes.present) {
      map['notes'] = Variable<String>(notes.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<DateTime>(updatedAt.value);
    }
    if (deletedAt.present) {
      map['deleted_at'] = Variable<DateTime>(deletedAt.value);
    }
    if (syncStatus.present) {
      map['sync_status'] = Variable<String>(
        $SparePartsHistoryTable.$convertersyncStatus.toSql(syncStatus.value),
      );
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('SparePartsHistoryCompanion(')
          ..write('uuid: $uuid, ')
          ..write('id: $id, ')
          ..write('partName: $partName, ')
          ..write('partType: $partType, ')
          ..write('price: $price, ')
          ..write('replacementDate: $replacementDate, ')
          ..write('mileageAtReplacement: $mileageAtReplacement, ')
          ..write('sparePartId: $sparePartId, ')
          ..write('installationDate: $installationDate, ')
          ..write('initialMileage: $initialMileage, ')
          ..write('replacementReason: $replacementReason, ')
          ..write('replacedByPartId: $replacedByPartId, ')
          ..write('replacementCount: $replacementCount, ')
          ..write('usageDays: $usageDays, ')
          ..write('usageMileage: $usageMileage, ')
          ..write('notes: $notes, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('deletedAt: $deletedAt, ')
          ..write('syncStatus: $syncStatus')
          ..write(')'))
        .toString();
  }
}

class $AppSettingsTable extends AppSettings
    with TableInfo<$AppSettingsTable, AppSetting> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $AppSettingsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
    'id',
    aliasedName,
    false,
    hasAutoIncrement: true,
    type: DriftSqlType.int,
    requiredDuringInsert: false,
    defaultConstraints: GeneratedColumn.constraintIsAlways(
      'PRIMARY KEY AUTOINCREMENT',
    ),
  );
  @override
  late final GeneratedColumnWithTypeConverter<DateTime, DateTime>
  dateRangeStart = GeneratedColumn<DateTime>(
    'date_range_start',
    aliasedName,
    false,
    type: DriftSqlType.dateTime,
    requiredDuringInsert: true,
  ).withConverter<DateTime>($AppSettingsTable.$converterdateRangeStart);
  @override
  late final GeneratedColumnWithTypeConverter<DateTime, DateTime> dateRangeEnd =
      GeneratedColumn<DateTime>(
        'date_range_end',
        aliasedName,
        false,
        type: DriftSqlType.dateTime,
        requiredDuringInsert: true,
      ).withConverter<DateTime>($AppSettingsTable.$converterdateRangeEnd);
  static const VerificationMeta _backupDirectoryPathMeta =
      const VerificationMeta('backupDirectoryPath');
  @override
  late final GeneratedColumn<String> backupDirectoryPath =
      GeneratedColumn<String>(
        'backup_directory_path',
        aliasedName,
        true,
        type: DriftSqlType.string,
        requiredDuringInsert: false,
      );
  static const VerificationMeta _updatedAtMeta = const VerificationMeta(
    'updatedAt',
  );
  @override
  late final GeneratedColumn<DateTime> updatedAt = GeneratedColumn<DateTime>(
    'updated_at',
    aliasedName,
    false,
    type: DriftSqlType.dateTime,
    requiredDuringInsert: false,
    clientDefault: () => DateTime.now().toUtc(),
  );
  @override
  late final GeneratedColumnWithTypeConverter<DateTime?, DateTime>
  lastSyncTime = GeneratedColumn<DateTime>(
    'last_sync_time',
    aliasedName,
    true,
    type: DriftSqlType.dateTime,
    requiredDuringInsert: false,
  ).withConverter<DateTime?>($AppSettingsTable.$converterlastSyncTimen);
  @override
  List<GeneratedColumn> get $columns => [
    id,
    dateRangeStart,
    dateRangeEnd,
    backupDirectoryPath,
    updatedAt,
    lastSyncTime,
  ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'app_settings';
  @override
  VerificationContext validateIntegrity(
    Insertable<AppSetting> instance, {
    bool isInserting = false,
  }) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('backup_directory_path')) {
      context.handle(
        _backupDirectoryPathMeta,
        backupDirectoryPath.isAcceptableOrUnknown(
          data['backup_directory_path']!,
          _backupDirectoryPathMeta,
        ),
      );
    }
    if (data.containsKey('updated_at')) {
      context.handle(
        _updatedAtMeta,
        updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta),
      );
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  AppSetting map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return AppSetting(
      id: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}id'],
      )!,
      dateRangeStart: $AppSettingsTable.$converterdateRangeStart.fromSql(
        attachedDatabase.typeMapping.read(
          DriftSqlType.dateTime,
          data['${effectivePrefix}date_range_start'],
        )!,
      ),
      dateRangeEnd: $AppSettingsTable.$converterdateRangeEnd.fromSql(
        attachedDatabase.typeMapping.read(
          DriftSqlType.dateTime,
          data['${effectivePrefix}date_range_end'],
        )!,
      ),
      backupDirectoryPath: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}backup_directory_path'],
      ),
      updatedAt: attachedDatabase.typeMapping.read(
        DriftSqlType.dateTime,
        data['${effectivePrefix}updated_at'],
      )!,
      lastSyncTime: $AppSettingsTable.$converterlastSyncTimen.fromSql(
        attachedDatabase.typeMapping.read(
          DriftSqlType.dateTime,
          data['${effectivePrefix}last_sync_time'],
        ),
      ),
    );
  }

  @override
  $AppSettingsTable createAlias(String alias) {
    return $AppSettingsTable(attachedDatabase, alias);
  }

  static TypeConverter<DateTime, DateTime> $converterdateRangeStart =
      const UtcDateTimeConverter();
  static TypeConverter<DateTime, DateTime> $converterdateRangeEnd =
      const UtcDateTimeConverter();
  static TypeConverter<DateTime, DateTime> $converterlastSyncTime =
      const UtcDateTimeConverter();
  static TypeConverter<DateTime?, DateTime?> $converterlastSyncTimen =
      NullAwareTypeConverter.wrap($converterlastSyncTime);
}

class AppSetting extends DataClass implements Insertable<AppSetting> {
  final int id;
  final DateTime dateRangeStart;
  final DateTime dateRangeEnd;
  final String? backupDirectoryPath;
  final DateTime updatedAt;
  final DateTime? lastSyncTime;
  const AppSetting({
    required this.id,
    required this.dateRangeStart,
    required this.dateRangeEnd,
    this.backupDirectoryPath,
    required this.updatedAt,
    this.lastSyncTime,
  });
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    {
      map['date_range_start'] = Variable<DateTime>(
        $AppSettingsTable.$converterdateRangeStart.toSql(dateRangeStart),
      );
    }
    {
      map['date_range_end'] = Variable<DateTime>(
        $AppSettingsTable.$converterdateRangeEnd.toSql(dateRangeEnd),
      );
    }
    if (!nullToAbsent || backupDirectoryPath != null) {
      map['backup_directory_path'] = Variable<String>(backupDirectoryPath);
    }
    map['updated_at'] = Variable<DateTime>(updatedAt);
    if (!nullToAbsent || lastSyncTime != null) {
      map['last_sync_time'] = Variable<DateTime>(
        $AppSettingsTable.$converterlastSyncTimen.toSql(lastSyncTime),
      );
    }
    return map;
  }

  AppSettingsCompanion toCompanion(bool nullToAbsent) {
    return AppSettingsCompanion(
      id: Value(id),
      dateRangeStart: Value(dateRangeStart),
      dateRangeEnd: Value(dateRangeEnd),
      backupDirectoryPath: backupDirectoryPath == null && nullToAbsent
          ? const Value.absent()
          : Value(backupDirectoryPath),
      updatedAt: Value(updatedAt),
      lastSyncTime: lastSyncTime == null && nullToAbsent
          ? const Value.absent()
          : Value(lastSyncTime),
    );
  }

  factory AppSetting.fromJson(
    Map<String, dynamic> json, {
    ValueSerializer? serializer,
  }) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return AppSetting(
      id: serializer.fromJson<int>(json['id']),
      dateRangeStart: serializer.fromJson<DateTime>(json['dateRangeStart']),
      dateRangeEnd: serializer.fromJson<DateTime>(json['dateRangeEnd']),
      backupDirectoryPath: serializer.fromJson<String?>(
        json['backupDirectoryPath'],
      ),
      updatedAt: serializer.fromJson<DateTime>(json['updatedAt']),
      lastSyncTime: serializer.fromJson<DateTime?>(json['lastSyncTime']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'dateRangeStart': serializer.toJson<DateTime>(dateRangeStart),
      'dateRangeEnd': serializer.toJson<DateTime>(dateRangeEnd),
      'backupDirectoryPath': serializer.toJson<String?>(backupDirectoryPath),
      'updatedAt': serializer.toJson<DateTime>(updatedAt),
      'lastSyncTime': serializer.toJson<DateTime?>(lastSyncTime),
    };
  }

  AppSetting copyWith({
    int? id,
    DateTime? dateRangeStart,
    DateTime? dateRangeEnd,
    Value<String?> backupDirectoryPath = const Value.absent(),
    DateTime? updatedAt,
    Value<DateTime?> lastSyncTime = const Value.absent(),
  }) => AppSetting(
    id: id ?? this.id,
    dateRangeStart: dateRangeStart ?? this.dateRangeStart,
    dateRangeEnd: dateRangeEnd ?? this.dateRangeEnd,
    backupDirectoryPath: backupDirectoryPath.present
        ? backupDirectoryPath.value
        : this.backupDirectoryPath,
    updatedAt: updatedAt ?? this.updatedAt,
    lastSyncTime: lastSyncTime.present ? lastSyncTime.value : this.lastSyncTime,
  );
  AppSetting copyWithCompanion(AppSettingsCompanion data) {
    return AppSetting(
      id: data.id.present ? data.id.value : this.id,
      dateRangeStart: data.dateRangeStart.present
          ? data.dateRangeStart.value
          : this.dateRangeStart,
      dateRangeEnd: data.dateRangeEnd.present
          ? data.dateRangeEnd.value
          : this.dateRangeEnd,
      backupDirectoryPath: data.backupDirectoryPath.present
          ? data.backupDirectoryPath.value
          : this.backupDirectoryPath,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
      lastSyncTime: data.lastSyncTime.present
          ? data.lastSyncTime.value
          : this.lastSyncTime,
    );
  }

  @override
  String toString() {
    return (StringBuffer('AppSetting(')
          ..write('id: $id, ')
          ..write('dateRangeStart: $dateRangeStart, ')
          ..write('dateRangeEnd: $dateRangeEnd, ')
          ..write('backupDirectoryPath: $backupDirectoryPath, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('lastSyncTime: $lastSyncTime')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
    id,
    dateRangeStart,
    dateRangeEnd,
    backupDirectoryPath,
    updatedAt,
    lastSyncTime,
  );
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is AppSetting &&
          other.id == this.id &&
          other.dateRangeStart == this.dateRangeStart &&
          other.dateRangeEnd == this.dateRangeEnd &&
          other.backupDirectoryPath == this.backupDirectoryPath &&
          other.updatedAt == this.updatedAt &&
          other.lastSyncTime == this.lastSyncTime);
}

class AppSettingsCompanion extends UpdateCompanion<AppSetting> {
  final Value<int> id;
  final Value<DateTime> dateRangeStart;
  final Value<DateTime> dateRangeEnd;
  final Value<String?> backupDirectoryPath;
  final Value<DateTime> updatedAt;
  final Value<DateTime?> lastSyncTime;
  const AppSettingsCompanion({
    this.id = const Value.absent(),
    this.dateRangeStart = const Value.absent(),
    this.dateRangeEnd = const Value.absent(),
    this.backupDirectoryPath = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.lastSyncTime = const Value.absent(),
  });
  AppSettingsCompanion.insert({
    this.id = const Value.absent(),
    required DateTime dateRangeStart,
    required DateTime dateRangeEnd,
    this.backupDirectoryPath = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.lastSyncTime = const Value.absent(),
  }) : dateRangeStart = Value(dateRangeStart),
       dateRangeEnd = Value(dateRangeEnd);
  static Insertable<AppSetting> custom({
    Expression<int>? id,
    Expression<DateTime>? dateRangeStart,
    Expression<DateTime>? dateRangeEnd,
    Expression<String>? backupDirectoryPath,
    Expression<DateTime>? updatedAt,
    Expression<DateTime>? lastSyncTime,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (dateRangeStart != null) 'date_range_start': dateRangeStart,
      if (dateRangeEnd != null) 'date_range_end': dateRangeEnd,
      if (backupDirectoryPath != null)
        'backup_directory_path': backupDirectoryPath,
      if (updatedAt != null) 'updated_at': updatedAt,
      if (lastSyncTime != null) 'last_sync_time': lastSyncTime,
    });
  }

  AppSettingsCompanion copyWith({
    Value<int>? id,
    Value<DateTime>? dateRangeStart,
    Value<DateTime>? dateRangeEnd,
    Value<String?>? backupDirectoryPath,
    Value<DateTime>? updatedAt,
    Value<DateTime?>? lastSyncTime,
  }) {
    return AppSettingsCompanion(
      id: id ?? this.id,
      dateRangeStart: dateRangeStart ?? this.dateRangeStart,
      dateRangeEnd: dateRangeEnd ?? this.dateRangeEnd,
      backupDirectoryPath: backupDirectoryPath ?? this.backupDirectoryPath,
      updatedAt: updatedAt ?? this.updatedAt,
      lastSyncTime: lastSyncTime ?? this.lastSyncTime,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (dateRangeStart.present) {
      map['date_range_start'] = Variable<DateTime>(
        $AppSettingsTable.$converterdateRangeStart.toSql(dateRangeStart.value),
      );
    }
    if (dateRangeEnd.present) {
      map['date_range_end'] = Variable<DateTime>(
        $AppSettingsTable.$converterdateRangeEnd.toSql(dateRangeEnd.value),
      );
    }
    if (backupDirectoryPath.present) {
      map['backup_directory_path'] = Variable<String>(
        backupDirectoryPath.value,
      );
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<DateTime>(updatedAt.value);
    }
    if (lastSyncTime.present) {
      map['last_sync_time'] = Variable<DateTime>(
        $AppSettingsTable.$converterlastSyncTimen.toSql(lastSyncTime.value),
      );
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('AppSettingsCompanion(')
          ..write('id: $id, ')
          ..write('dateRangeStart: $dateRangeStart, ')
          ..write('dateRangeEnd: $dateRangeEnd, ')
          ..write('backupDirectoryPath: $backupDirectoryPath, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('lastSyncTime: $lastSyncTime')
          ..write(')'))
        .toString();
  }
}

abstract class _$AppDatabase extends GeneratedDatabase {
  _$AppDatabase(QueryExecutor e) : super(e);
  $AppDatabaseManager get managers => $AppDatabaseManager(this);
  late final $IncomeTable income = $IncomeTable(this);
  late final $OrdersTable orders = $OrdersTable(this);
  late final $PerformanceTable performance = $PerformanceTable(this);
  late final $LevelSettingsTable levelSettings = $LevelSettingsTable(this);
  late final $SparePartsTable spareParts = $SparePartsTable(this);
  late final $SparePartsHistoryTable sparePartsHistory =
      $SparePartsHistoryTable(this);
  late final $AppSettingsTable appSettings = $AppSettingsTable(this);
  @override
  Iterable<TableInfo<Table, Object?>> get allTables =>
      allSchemaEntities.whereType<TableInfo<Table, Object?>>();
  @override
  List<DatabaseSchemaEntity> get allSchemaEntities => [
    income,
    orders,
    performance,
    levelSettings,
    spareParts,
    sparePartsHistory,
    appSettings,
  ];
}

typedef $$IncomeTableCreateCompanionBuilder =
    IncomeCompanion Function({
      Value<String> uuid,
      Value<int> id,
      required DateTime date,
      required int initialMileage,
      required int finalMileage,
      required double initialGopay,
      required double initialBca,
      required double initialCash,
      required double initialOvo,
      required double initialBri,
      required double initialRekpon,
      required double finalGopay,
      required double finalBca,
      required double finalCash,
      required double finalOvo,
      required double finalBri,
      required double finalRekpon,
      Value<double?> initialCapital,
      Value<double?> finalResult,
      Value<int?> mileage,
      Value<double?> netIncome,
      Value<DateTime> createdAt,
      Value<DateTime> updatedAt,
      Value<DateTime?> deletedAt,
      Value<SyncStatus> syncStatus,
    });
typedef $$IncomeTableUpdateCompanionBuilder =
    IncomeCompanion Function({
      Value<String> uuid,
      Value<int> id,
      Value<DateTime> date,
      Value<int> initialMileage,
      Value<int> finalMileage,
      Value<double> initialGopay,
      Value<double> initialBca,
      Value<double> initialCash,
      Value<double> initialOvo,
      Value<double> initialBri,
      Value<double> initialRekpon,
      Value<double> finalGopay,
      Value<double> finalBca,
      Value<double> finalCash,
      Value<double> finalOvo,
      Value<double> finalBri,
      Value<double> finalRekpon,
      Value<double?> initialCapital,
      Value<double?> finalResult,
      Value<int?> mileage,
      Value<double?> netIncome,
      Value<DateTime> createdAt,
      Value<DateTime> updatedAt,
      Value<DateTime?> deletedAt,
      Value<SyncStatus> syncStatus,
    });

class $$IncomeTableFilterComposer
    extends Composer<_$AppDatabase, $IncomeTable> {
  $$IncomeTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get uuid => $composableBuilder(
    column: $table.uuid,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnFilters(column),
  );

  ColumnWithTypeConverterFilters<DateTime, DateTime, DateTime> get date =>
      $composableBuilder(
        column: $table.date,
        builder: (column) => ColumnWithTypeConverterFilters(column),
      );

  ColumnFilters<int> get initialMileage => $composableBuilder(
    column: $table.initialMileage,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get finalMileage => $composableBuilder(
    column: $table.finalMileage,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get initialGopay => $composableBuilder(
    column: $table.initialGopay,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get initialBca => $composableBuilder(
    column: $table.initialBca,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get initialCash => $composableBuilder(
    column: $table.initialCash,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get initialOvo => $composableBuilder(
    column: $table.initialOvo,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get initialBri => $composableBuilder(
    column: $table.initialBri,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get initialRekpon => $composableBuilder(
    column: $table.initialRekpon,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get finalGopay => $composableBuilder(
    column: $table.finalGopay,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get finalBca => $composableBuilder(
    column: $table.finalBca,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get finalCash => $composableBuilder(
    column: $table.finalCash,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get finalOvo => $composableBuilder(
    column: $table.finalOvo,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get finalBri => $composableBuilder(
    column: $table.finalBri,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get finalRekpon => $composableBuilder(
    column: $table.finalRekpon,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get initialCapital => $composableBuilder(
    column: $table.initialCapital,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get finalResult => $composableBuilder(
    column: $table.finalResult,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get mileage => $composableBuilder(
    column: $table.mileage,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get netIncome => $composableBuilder(
    column: $table.netIncome,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
    column: $table.createdAt,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<DateTime> get updatedAt => $composableBuilder(
    column: $table.updatedAt,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<DateTime> get deletedAt => $composableBuilder(
    column: $table.deletedAt,
    builder: (column) => ColumnFilters(column),
  );

  ColumnWithTypeConverterFilters<SyncStatus, SyncStatus, String>
  get syncStatus => $composableBuilder(
    column: $table.syncStatus,
    builder: (column) => ColumnWithTypeConverterFilters(column),
  );
}

class $$IncomeTableOrderingComposer
    extends Composer<_$AppDatabase, $IncomeTable> {
  $$IncomeTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get uuid => $composableBuilder(
    column: $table.uuid,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get date => $composableBuilder(
    column: $table.date,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get initialMileage => $composableBuilder(
    column: $table.initialMileage,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get finalMileage => $composableBuilder(
    column: $table.finalMileage,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get initialGopay => $composableBuilder(
    column: $table.initialGopay,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get initialBca => $composableBuilder(
    column: $table.initialBca,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get initialCash => $composableBuilder(
    column: $table.initialCash,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get initialOvo => $composableBuilder(
    column: $table.initialOvo,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get initialBri => $composableBuilder(
    column: $table.initialBri,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get initialRekpon => $composableBuilder(
    column: $table.initialRekpon,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get finalGopay => $composableBuilder(
    column: $table.finalGopay,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get finalBca => $composableBuilder(
    column: $table.finalBca,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get finalCash => $composableBuilder(
    column: $table.finalCash,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get finalOvo => $composableBuilder(
    column: $table.finalOvo,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get finalBri => $composableBuilder(
    column: $table.finalBri,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get finalRekpon => $composableBuilder(
    column: $table.finalRekpon,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get initialCapital => $composableBuilder(
    column: $table.initialCapital,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get finalResult => $composableBuilder(
    column: $table.finalResult,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get mileage => $composableBuilder(
    column: $table.mileage,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get netIncome => $composableBuilder(
    column: $table.netIncome,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
    column: $table.createdAt,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get updatedAt => $composableBuilder(
    column: $table.updatedAt,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get deletedAt => $composableBuilder(
    column: $table.deletedAt,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get syncStatus => $composableBuilder(
    column: $table.syncStatus,
    builder: (column) => ColumnOrderings(column),
  );
}

class $$IncomeTableAnnotationComposer
    extends Composer<_$AppDatabase, $IncomeTable> {
  $$IncomeTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get uuid =>
      $composableBuilder(column: $table.uuid, builder: (column) => column);

  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumnWithTypeConverter<DateTime, DateTime> get date =>
      $composableBuilder(column: $table.date, builder: (column) => column);

  GeneratedColumn<int> get initialMileage => $composableBuilder(
    column: $table.initialMileage,
    builder: (column) => column,
  );

  GeneratedColumn<int> get finalMileage => $composableBuilder(
    column: $table.finalMileage,
    builder: (column) => column,
  );

  GeneratedColumn<double> get initialGopay => $composableBuilder(
    column: $table.initialGopay,
    builder: (column) => column,
  );

  GeneratedColumn<double> get initialBca => $composableBuilder(
    column: $table.initialBca,
    builder: (column) => column,
  );

  GeneratedColumn<double> get initialCash => $composableBuilder(
    column: $table.initialCash,
    builder: (column) => column,
  );

  GeneratedColumn<double> get initialOvo => $composableBuilder(
    column: $table.initialOvo,
    builder: (column) => column,
  );

  GeneratedColumn<double> get initialBri => $composableBuilder(
    column: $table.initialBri,
    builder: (column) => column,
  );

  GeneratedColumn<double> get initialRekpon => $composableBuilder(
    column: $table.initialRekpon,
    builder: (column) => column,
  );

  GeneratedColumn<double> get finalGopay => $composableBuilder(
    column: $table.finalGopay,
    builder: (column) => column,
  );

  GeneratedColumn<double> get finalBca =>
      $composableBuilder(column: $table.finalBca, builder: (column) => column);

  GeneratedColumn<double> get finalCash =>
      $composableBuilder(column: $table.finalCash, builder: (column) => column);

  GeneratedColumn<double> get finalOvo =>
      $composableBuilder(column: $table.finalOvo, builder: (column) => column);

  GeneratedColumn<double> get finalBri =>
      $composableBuilder(column: $table.finalBri, builder: (column) => column);

  GeneratedColumn<double> get finalRekpon => $composableBuilder(
    column: $table.finalRekpon,
    builder: (column) => column,
  );

  GeneratedColumn<double> get initialCapital => $composableBuilder(
    column: $table.initialCapital,
    builder: (column) => column,
  );

  GeneratedColumn<double> get finalResult => $composableBuilder(
    column: $table.finalResult,
    builder: (column) => column,
  );

  GeneratedColumn<int> get mileage =>
      $composableBuilder(column: $table.mileage, builder: (column) => column);

  GeneratedColumn<double> get netIncome =>
      $composableBuilder(column: $table.netIncome, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  GeneratedColumn<DateTime> get updatedAt =>
      $composableBuilder(column: $table.updatedAt, builder: (column) => column);

  GeneratedColumn<DateTime> get deletedAt =>
      $composableBuilder(column: $table.deletedAt, builder: (column) => column);

  GeneratedColumnWithTypeConverter<SyncStatus, String> get syncStatus =>
      $composableBuilder(
        column: $table.syncStatus,
        builder: (column) => column,
      );
}

class $$IncomeTableTableManager
    extends
        RootTableManager<
          _$AppDatabase,
          $IncomeTable,
          IncomeData,
          $$IncomeTableFilterComposer,
          $$IncomeTableOrderingComposer,
          $$IncomeTableAnnotationComposer,
          $$IncomeTableCreateCompanionBuilder,
          $$IncomeTableUpdateCompanionBuilder,
          (IncomeData, BaseReferences<_$AppDatabase, $IncomeTable, IncomeData>),
          IncomeData,
          PrefetchHooks Function()
        > {
  $$IncomeTableTableManager(_$AppDatabase db, $IncomeTable table)
    : super(
        TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$IncomeTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$IncomeTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$IncomeTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback:
              ({
                Value<String> uuid = const Value.absent(),
                Value<int> id = const Value.absent(),
                Value<DateTime> date = const Value.absent(),
                Value<int> initialMileage = const Value.absent(),
                Value<int> finalMileage = const Value.absent(),
                Value<double> initialGopay = const Value.absent(),
                Value<double> initialBca = const Value.absent(),
                Value<double> initialCash = const Value.absent(),
                Value<double> initialOvo = const Value.absent(),
                Value<double> initialBri = const Value.absent(),
                Value<double> initialRekpon = const Value.absent(),
                Value<double> finalGopay = const Value.absent(),
                Value<double> finalBca = const Value.absent(),
                Value<double> finalCash = const Value.absent(),
                Value<double> finalOvo = const Value.absent(),
                Value<double> finalBri = const Value.absent(),
                Value<double> finalRekpon = const Value.absent(),
                Value<double?> initialCapital = const Value.absent(),
                Value<double?> finalResult = const Value.absent(),
                Value<int?> mileage = const Value.absent(),
                Value<double?> netIncome = const Value.absent(),
                Value<DateTime> createdAt = const Value.absent(),
                Value<DateTime> updatedAt = const Value.absent(),
                Value<DateTime?> deletedAt = const Value.absent(),
                Value<SyncStatus> syncStatus = const Value.absent(),
              }) => IncomeCompanion(
                uuid: uuid,
                id: id,
                date: date,
                initialMileage: initialMileage,
                finalMileage: finalMileage,
                initialGopay: initialGopay,
                initialBca: initialBca,
                initialCash: initialCash,
                initialOvo: initialOvo,
                initialBri: initialBri,
                initialRekpon: initialRekpon,
                finalGopay: finalGopay,
                finalBca: finalBca,
                finalCash: finalCash,
                finalOvo: finalOvo,
                finalBri: finalBri,
                finalRekpon: finalRekpon,
                initialCapital: initialCapital,
                finalResult: finalResult,
                mileage: mileage,
                netIncome: netIncome,
                createdAt: createdAt,
                updatedAt: updatedAt,
                deletedAt: deletedAt,
                syncStatus: syncStatus,
              ),
          createCompanionCallback:
              ({
                Value<String> uuid = const Value.absent(),
                Value<int> id = const Value.absent(),
                required DateTime date,
                required int initialMileage,
                required int finalMileage,
                required double initialGopay,
                required double initialBca,
                required double initialCash,
                required double initialOvo,
                required double initialBri,
                required double initialRekpon,
                required double finalGopay,
                required double finalBca,
                required double finalCash,
                required double finalOvo,
                required double finalBri,
                required double finalRekpon,
                Value<double?> initialCapital = const Value.absent(),
                Value<double?> finalResult = const Value.absent(),
                Value<int?> mileage = const Value.absent(),
                Value<double?> netIncome = const Value.absent(),
                Value<DateTime> createdAt = const Value.absent(),
                Value<DateTime> updatedAt = const Value.absent(),
                Value<DateTime?> deletedAt = const Value.absent(),
                Value<SyncStatus> syncStatus = const Value.absent(),
              }) => IncomeCompanion.insert(
                uuid: uuid,
                id: id,
                date: date,
                initialMileage: initialMileage,
                finalMileage: finalMileage,
                initialGopay: initialGopay,
                initialBca: initialBca,
                initialCash: initialCash,
                initialOvo: initialOvo,
                initialBri: initialBri,
                initialRekpon: initialRekpon,
                finalGopay: finalGopay,
                finalBca: finalBca,
                finalCash: finalCash,
                finalOvo: finalOvo,
                finalBri: finalBri,
                finalRekpon: finalRekpon,
                initialCapital: initialCapital,
                finalResult: finalResult,
                mileage: mileage,
                netIncome: netIncome,
                createdAt: createdAt,
                updatedAt: updatedAt,
                deletedAt: deletedAt,
                syncStatus: syncStatus,
              ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ),
      );
}

typedef $$IncomeTableProcessedTableManager =
    ProcessedTableManager<
      _$AppDatabase,
      $IncomeTable,
      IncomeData,
      $$IncomeTableFilterComposer,
      $$IncomeTableOrderingComposer,
      $$IncomeTableAnnotationComposer,
      $$IncomeTableCreateCompanionBuilder,
      $$IncomeTableUpdateCompanionBuilder,
      (IncomeData, BaseReferences<_$AppDatabase, $IncomeTable, IncomeData>),
      IncomeData,
      PrefetchHooks Function()
    >;
typedef $$OrdersTableCreateCompanionBuilder =
    OrdersCompanion Function({
      Value<String> uuid,
      Value<int> id,
      required DateTime date,
      required int orderCompleted,
      required int orderMissed,
      required int orderCanceled,
      required int cbsOrder,
      Value<int?> incomingOrder,
      Value<int?> orderReceived,
      Value<double?> bidAcceptance,
      Value<double?> tripCompletion,
      required int points,
      required double trip,
      required double bonus,
      required double tips,
      Value<double?> income,
      Value<DateTime> createdAt,
      Value<DateTime> updatedAt,
      Value<DateTime?> deletedAt,
      Value<SyncStatus> syncStatus,
    });
typedef $$OrdersTableUpdateCompanionBuilder =
    OrdersCompanion Function({
      Value<String> uuid,
      Value<int> id,
      Value<DateTime> date,
      Value<int> orderCompleted,
      Value<int> orderMissed,
      Value<int> orderCanceled,
      Value<int> cbsOrder,
      Value<int?> incomingOrder,
      Value<int?> orderReceived,
      Value<double?> bidAcceptance,
      Value<double?> tripCompletion,
      Value<int> points,
      Value<double> trip,
      Value<double> bonus,
      Value<double> tips,
      Value<double?> income,
      Value<DateTime> createdAt,
      Value<DateTime> updatedAt,
      Value<DateTime?> deletedAt,
      Value<SyncStatus> syncStatus,
    });

class $$OrdersTableFilterComposer
    extends Composer<_$AppDatabase, $OrdersTable> {
  $$OrdersTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get uuid => $composableBuilder(
    column: $table.uuid,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnFilters(column),
  );

  ColumnWithTypeConverterFilters<DateTime, DateTime, DateTime> get date =>
      $composableBuilder(
        column: $table.date,
        builder: (column) => ColumnWithTypeConverterFilters(column),
      );

  ColumnFilters<int> get orderCompleted => $composableBuilder(
    column: $table.orderCompleted,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get orderMissed => $composableBuilder(
    column: $table.orderMissed,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get orderCanceled => $composableBuilder(
    column: $table.orderCanceled,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get cbsOrder => $composableBuilder(
    column: $table.cbsOrder,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get incomingOrder => $composableBuilder(
    column: $table.incomingOrder,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get orderReceived => $composableBuilder(
    column: $table.orderReceived,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get bidAcceptance => $composableBuilder(
    column: $table.bidAcceptance,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get tripCompletion => $composableBuilder(
    column: $table.tripCompletion,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get points => $composableBuilder(
    column: $table.points,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get trip => $composableBuilder(
    column: $table.trip,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get bonus => $composableBuilder(
    column: $table.bonus,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get tips => $composableBuilder(
    column: $table.tips,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get income => $composableBuilder(
    column: $table.income,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
    column: $table.createdAt,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<DateTime> get updatedAt => $composableBuilder(
    column: $table.updatedAt,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<DateTime> get deletedAt => $composableBuilder(
    column: $table.deletedAt,
    builder: (column) => ColumnFilters(column),
  );

  ColumnWithTypeConverterFilters<SyncStatus, SyncStatus, String>
  get syncStatus => $composableBuilder(
    column: $table.syncStatus,
    builder: (column) => ColumnWithTypeConverterFilters(column),
  );
}

class $$OrdersTableOrderingComposer
    extends Composer<_$AppDatabase, $OrdersTable> {
  $$OrdersTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get uuid => $composableBuilder(
    column: $table.uuid,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get date => $composableBuilder(
    column: $table.date,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get orderCompleted => $composableBuilder(
    column: $table.orderCompleted,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get orderMissed => $composableBuilder(
    column: $table.orderMissed,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get orderCanceled => $composableBuilder(
    column: $table.orderCanceled,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get cbsOrder => $composableBuilder(
    column: $table.cbsOrder,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get incomingOrder => $composableBuilder(
    column: $table.incomingOrder,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get orderReceived => $composableBuilder(
    column: $table.orderReceived,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get bidAcceptance => $composableBuilder(
    column: $table.bidAcceptance,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get tripCompletion => $composableBuilder(
    column: $table.tripCompletion,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get points => $composableBuilder(
    column: $table.points,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get trip => $composableBuilder(
    column: $table.trip,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get bonus => $composableBuilder(
    column: $table.bonus,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get tips => $composableBuilder(
    column: $table.tips,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get income => $composableBuilder(
    column: $table.income,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
    column: $table.createdAt,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get updatedAt => $composableBuilder(
    column: $table.updatedAt,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get deletedAt => $composableBuilder(
    column: $table.deletedAt,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get syncStatus => $composableBuilder(
    column: $table.syncStatus,
    builder: (column) => ColumnOrderings(column),
  );
}

class $$OrdersTableAnnotationComposer
    extends Composer<_$AppDatabase, $OrdersTable> {
  $$OrdersTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get uuid =>
      $composableBuilder(column: $table.uuid, builder: (column) => column);

  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumnWithTypeConverter<DateTime, DateTime> get date =>
      $composableBuilder(column: $table.date, builder: (column) => column);

  GeneratedColumn<int> get orderCompleted => $composableBuilder(
    column: $table.orderCompleted,
    builder: (column) => column,
  );

  GeneratedColumn<int> get orderMissed => $composableBuilder(
    column: $table.orderMissed,
    builder: (column) => column,
  );

  GeneratedColumn<int> get orderCanceled => $composableBuilder(
    column: $table.orderCanceled,
    builder: (column) => column,
  );

  GeneratedColumn<int> get cbsOrder =>
      $composableBuilder(column: $table.cbsOrder, builder: (column) => column);

  GeneratedColumn<int> get incomingOrder => $composableBuilder(
    column: $table.incomingOrder,
    builder: (column) => column,
  );

  GeneratedColumn<int> get orderReceived => $composableBuilder(
    column: $table.orderReceived,
    builder: (column) => column,
  );

  GeneratedColumn<double> get bidAcceptance => $composableBuilder(
    column: $table.bidAcceptance,
    builder: (column) => column,
  );

  GeneratedColumn<double> get tripCompletion => $composableBuilder(
    column: $table.tripCompletion,
    builder: (column) => column,
  );

  GeneratedColumn<int> get points =>
      $composableBuilder(column: $table.points, builder: (column) => column);

  GeneratedColumn<double> get trip =>
      $composableBuilder(column: $table.trip, builder: (column) => column);

  GeneratedColumn<double> get bonus =>
      $composableBuilder(column: $table.bonus, builder: (column) => column);

  GeneratedColumn<double> get tips =>
      $composableBuilder(column: $table.tips, builder: (column) => column);

  GeneratedColumn<double> get income =>
      $composableBuilder(column: $table.income, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  GeneratedColumn<DateTime> get updatedAt =>
      $composableBuilder(column: $table.updatedAt, builder: (column) => column);

  GeneratedColumn<DateTime> get deletedAt =>
      $composableBuilder(column: $table.deletedAt, builder: (column) => column);

  GeneratedColumnWithTypeConverter<SyncStatus, String> get syncStatus =>
      $composableBuilder(
        column: $table.syncStatus,
        builder: (column) => column,
      );
}

class $$OrdersTableTableManager
    extends
        RootTableManager<
          _$AppDatabase,
          $OrdersTable,
          Order,
          $$OrdersTableFilterComposer,
          $$OrdersTableOrderingComposer,
          $$OrdersTableAnnotationComposer,
          $$OrdersTableCreateCompanionBuilder,
          $$OrdersTableUpdateCompanionBuilder,
          (Order, BaseReferences<_$AppDatabase, $OrdersTable, Order>),
          Order,
          PrefetchHooks Function()
        > {
  $$OrdersTableTableManager(_$AppDatabase db, $OrdersTable table)
    : super(
        TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$OrdersTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$OrdersTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$OrdersTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback:
              ({
                Value<String> uuid = const Value.absent(),
                Value<int> id = const Value.absent(),
                Value<DateTime> date = const Value.absent(),
                Value<int> orderCompleted = const Value.absent(),
                Value<int> orderMissed = const Value.absent(),
                Value<int> orderCanceled = const Value.absent(),
                Value<int> cbsOrder = const Value.absent(),
                Value<int?> incomingOrder = const Value.absent(),
                Value<int?> orderReceived = const Value.absent(),
                Value<double?> bidAcceptance = const Value.absent(),
                Value<double?> tripCompletion = const Value.absent(),
                Value<int> points = const Value.absent(),
                Value<double> trip = const Value.absent(),
                Value<double> bonus = const Value.absent(),
                Value<double> tips = const Value.absent(),
                Value<double?> income = const Value.absent(),
                Value<DateTime> createdAt = const Value.absent(),
                Value<DateTime> updatedAt = const Value.absent(),
                Value<DateTime?> deletedAt = const Value.absent(),
                Value<SyncStatus> syncStatus = const Value.absent(),
              }) => OrdersCompanion(
                uuid: uuid,
                id: id,
                date: date,
                orderCompleted: orderCompleted,
                orderMissed: orderMissed,
                orderCanceled: orderCanceled,
                cbsOrder: cbsOrder,
                incomingOrder: incomingOrder,
                orderReceived: orderReceived,
                bidAcceptance: bidAcceptance,
                tripCompletion: tripCompletion,
                points: points,
                trip: trip,
                bonus: bonus,
                tips: tips,
                income: income,
                createdAt: createdAt,
                updatedAt: updatedAt,
                deletedAt: deletedAt,
                syncStatus: syncStatus,
              ),
          createCompanionCallback:
              ({
                Value<String> uuid = const Value.absent(),
                Value<int> id = const Value.absent(),
                required DateTime date,
                required int orderCompleted,
                required int orderMissed,
                required int orderCanceled,
                required int cbsOrder,
                Value<int?> incomingOrder = const Value.absent(),
                Value<int?> orderReceived = const Value.absent(),
                Value<double?> bidAcceptance = const Value.absent(),
                Value<double?> tripCompletion = const Value.absent(),
                required int points,
                required double trip,
                required double bonus,
                required double tips,
                Value<double?> income = const Value.absent(),
                Value<DateTime> createdAt = const Value.absent(),
                Value<DateTime> updatedAt = const Value.absent(),
                Value<DateTime?> deletedAt = const Value.absent(),
                Value<SyncStatus> syncStatus = const Value.absent(),
              }) => OrdersCompanion.insert(
                uuid: uuid,
                id: id,
                date: date,
                orderCompleted: orderCompleted,
                orderMissed: orderMissed,
                orderCanceled: orderCanceled,
                cbsOrder: cbsOrder,
                incomingOrder: incomingOrder,
                orderReceived: orderReceived,
                bidAcceptance: bidAcceptance,
                tripCompletion: tripCompletion,
                points: points,
                trip: trip,
                bonus: bonus,
                tips: tips,
                income: income,
                createdAt: createdAt,
                updatedAt: updatedAt,
                deletedAt: deletedAt,
                syncStatus: syncStatus,
              ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ),
      );
}

typedef $$OrdersTableProcessedTableManager =
    ProcessedTableManager<
      _$AppDatabase,
      $OrdersTable,
      Order,
      $$OrdersTableFilterComposer,
      $$OrdersTableOrderingComposer,
      $$OrdersTableAnnotationComposer,
      $$OrdersTableCreateCompanionBuilder,
      $$OrdersTableUpdateCompanionBuilder,
      (Order, BaseReferences<_$AppDatabase, $OrdersTable, Order>),
      Order,
      PrefetchHooks Function()
    >;
typedef $$PerformanceTableCreateCompanionBuilder =
    PerformanceCompanion Function({
      Value<String> uuid,
      Value<int> id,
      required DateTime date,
      required double bidPerformance,
      required double tripPerformance,
      required int activeDays,
      required double onlineHours,
      Value<double?> avgCompleted,
      Value<double?> avgOnline,
      Value<double?> retention,
      Value<DateTime> createdAt,
      Value<DateTime> updatedAt,
      Value<DateTime?> deletedAt,
      Value<SyncStatus> syncStatus,
    });
typedef $$PerformanceTableUpdateCompanionBuilder =
    PerformanceCompanion Function({
      Value<String> uuid,
      Value<int> id,
      Value<DateTime> date,
      Value<double> bidPerformance,
      Value<double> tripPerformance,
      Value<int> activeDays,
      Value<double> onlineHours,
      Value<double?> avgCompleted,
      Value<double?> avgOnline,
      Value<double?> retention,
      Value<DateTime> createdAt,
      Value<DateTime> updatedAt,
      Value<DateTime?> deletedAt,
      Value<SyncStatus> syncStatus,
    });

class $$PerformanceTableFilterComposer
    extends Composer<_$AppDatabase, $PerformanceTable> {
  $$PerformanceTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get uuid => $composableBuilder(
    column: $table.uuid,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnFilters(column),
  );

  ColumnWithTypeConverterFilters<DateTime, DateTime, DateTime> get date =>
      $composableBuilder(
        column: $table.date,
        builder: (column) => ColumnWithTypeConverterFilters(column),
      );

  ColumnFilters<double> get bidPerformance => $composableBuilder(
    column: $table.bidPerformance,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get tripPerformance => $composableBuilder(
    column: $table.tripPerformance,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get activeDays => $composableBuilder(
    column: $table.activeDays,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get onlineHours => $composableBuilder(
    column: $table.onlineHours,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get avgCompleted => $composableBuilder(
    column: $table.avgCompleted,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get avgOnline => $composableBuilder(
    column: $table.avgOnline,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get retention => $composableBuilder(
    column: $table.retention,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
    column: $table.createdAt,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<DateTime> get updatedAt => $composableBuilder(
    column: $table.updatedAt,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<DateTime> get deletedAt => $composableBuilder(
    column: $table.deletedAt,
    builder: (column) => ColumnFilters(column),
  );

  ColumnWithTypeConverterFilters<SyncStatus, SyncStatus, String>
  get syncStatus => $composableBuilder(
    column: $table.syncStatus,
    builder: (column) => ColumnWithTypeConverterFilters(column),
  );
}

class $$PerformanceTableOrderingComposer
    extends Composer<_$AppDatabase, $PerformanceTable> {
  $$PerformanceTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get uuid => $composableBuilder(
    column: $table.uuid,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get date => $composableBuilder(
    column: $table.date,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get bidPerformance => $composableBuilder(
    column: $table.bidPerformance,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get tripPerformance => $composableBuilder(
    column: $table.tripPerformance,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get activeDays => $composableBuilder(
    column: $table.activeDays,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get onlineHours => $composableBuilder(
    column: $table.onlineHours,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get avgCompleted => $composableBuilder(
    column: $table.avgCompleted,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get avgOnline => $composableBuilder(
    column: $table.avgOnline,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get retention => $composableBuilder(
    column: $table.retention,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
    column: $table.createdAt,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get updatedAt => $composableBuilder(
    column: $table.updatedAt,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get deletedAt => $composableBuilder(
    column: $table.deletedAt,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get syncStatus => $composableBuilder(
    column: $table.syncStatus,
    builder: (column) => ColumnOrderings(column),
  );
}

class $$PerformanceTableAnnotationComposer
    extends Composer<_$AppDatabase, $PerformanceTable> {
  $$PerformanceTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get uuid =>
      $composableBuilder(column: $table.uuid, builder: (column) => column);

  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumnWithTypeConverter<DateTime, DateTime> get date =>
      $composableBuilder(column: $table.date, builder: (column) => column);

  GeneratedColumn<double> get bidPerformance => $composableBuilder(
    column: $table.bidPerformance,
    builder: (column) => column,
  );

  GeneratedColumn<double> get tripPerformance => $composableBuilder(
    column: $table.tripPerformance,
    builder: (column) => column,
  );

  GeneratedColumn<int> get activeDays => $composableBuilder(
    column: $table.activeDays,
    builder: (column) => column,
  );

  GeneratedColumn<double> get onlineHours => $composableBuilder(
    column: $table.onlineHours,
    builder: (column) => column,
  );

  GeneratedColumn<double> get avgCompleted => $composableBuilder(
    column: $table.avgCompleted,
    builder: (column) => column,
  );

  GeneratedColumn<double> get avgOnline =>
      $composableBuilder(column: $table.avgOnline, builder: (column) => column);

  GeneratedColumn<double> get retention =>
      $composableBuilder(column: $table.retention, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  GeneratedColumn<DateTime> get updatedAt =>
      $composableBuilder(column: $table.updatedAt, builder: (column) => column);

  GeneratedColumn<DateTime> get deletedAt =>
      $composableBuilder(column: $table.deletedAt, builder: (column) => column);

  GeneratedColumnWithTypeConverter<SyncStatus, String> get syncStatus =>
      $composableBuilder(
        column: $table.syncStatus,
        builder: (column) => column,
      );
}

class $$PerformanceTableTableManager
    extends
        RootTableManager<
          _$AppDatabase,
          $PerformanceTable,
          PerformanceData,
          $$PerformanceTableFilterComposer,
          $$PerformanceTableOrderingComposer,
          $$PerformanceTableAnnotationComposer,
          $$PerformanceTableCreateCompanionBuilder,
          $$PerformanceTableUpdateCompanionBuilder,
          (
            PerformanceData,
            BaseReferences<_$AppDatabase, $PerformanceTable, PerformanceData>,
          ),
          PerformanceData,
          PrefetchHooks Function()
        > {
  $$PerformanceTableTableManager(_$AppDatabase db, $PerformanceTable table)
    : super(
        TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$PerformanceTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$PerformanceTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$PerformanceTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback:
              ({
                Value<String> uuid = const Value.absent(),
                Value<int> id = const Value.absent(),
                Value<DateTime> date = const Value.absent(),
                Value<double> bidPerformance = const Value.absent(),
                Value<double> tripPerformance = const Value.absent(),
                Value<int> activeDays = const Value.absent(),
                Value<double> onlineHours = const Value.absent(),
                Value<double?> avgCompleted = const Value.absent(),
                Value<double?> avgOnline = const Value.absent(),
                Value<double?> retention = const Value.absent(),
                Value<DateTime> createdAt = const Value.absent(),
                Value<DateTime> updatedAt = const Value.absent(),
                Value<DateTime?> deletedAt = const Value.absent(),
                Value<SyncStatus> syncStatus = const Value.absent(),
              }) => PerformanceCompanion(
                uuid: uuid,
                id: id,
                date: date,
                bidPerformance: bidPerformance,
                tripPerformance: tripPerformance,
                activeDays: activeDays,
                onlineHours: onlineHours,
                avgCompleted: avgCompleted,
                avgOnline: avgOnline,
                retention: retention,
                createdAt: createdAt,
                updatedAt: updatedAt,
                deletedAt: deletedAt,
                syncStatus: syncStatus,
              ),
          createCompanionCallback:
              ({
                Value<String> uuid = const Value.absent(),
                Value<int> id = const Value.absent(),
                required DateTime date,
                required double bidPerformance,
                required double tripPerformance,
                required int activeDays,
                required double onlineHours,
                Value<double?> avgCompleted = const Value.absent(),
                Value<double?> avgOnline = const Value.absent(),
                Value<double?> retention = const Value.absent(),
                Value<DateTime> createdAt = const Value.absent(),
                Value<DateTime> updatedAt = const Value.absent(),
                Value<DateTime?> deletedAt = const Value.absent(),
                Value<SyncStatus> syncStatus = const Value.absent(),
              }) => PerformanceCompanion.insert(
                uuid: uuid,
                id: id,
                date: date,
                bidPerformance: bidPerformance,
                tripPerformance: tripPerformance,
                activeDays: activeDays,
                onlineHours: onlineHours,
                avgCompleted: avgCompleted,
                avgOnline: avgOnline,
                retention: retention,
                createdAt: createdAt,
                updatedAt: updatedAt,
                deletedAt: deletedAt,
                syncStatus: syncStatus,
              ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ),
      );
}

typedef $$PerformanceTableProcessedTableManager =
    ProcessedTableManager<
      _$AppDatabase,
      $PerformanceTable,
      PerformanceData,
      $$PerformanceTableFilterComposer,
      $$PerformanceTableOrderingComposer,
      $$PerformanceTableAnnotationComposer,
      $$PerformanceTableCreateCompanionBuilder,
      $$PerformanceTableUpdateCompanionBuilder,
      (
        PerformanceData,
        BaseReferences<_$AppDatabase, $PerformanceTable, PerformanceData>,
      ),
      PerformanceData,
      PrefetchHooks Function()
    >;
typedef $$LevelSettingsTableCreateCompanionBuilder =
    LevelSettingsCompanion Function({
      Value<int> id,
      required int platinumPointsReq,
      required double platinumBidReq,
      required double platinumTripReq,
      required int goldPointsReq,
      required double goldBidReq,
      required double goldTripReq,
      required int silverPointsReq,
      required double silverBidReq,
      required double silverTripReq,
    });
typedef $$LevelSettingsTableUpdateCompanionBuilder =
    LevelSettingsCompanion Function({
      Value<int> id,
      Value<int> platinumPointsReq,
      Value<double> platinumBidReq,
      Value<double> platinumTripReq,
      Value<int> goldPointsReq,
      Value<double> goldBidReq,
      Value<double> goldTripReq,
      Value<int> silverPointsReq,
      Value<double> silverBidReq,
      Value<double> silverTripReq,
    });

class $$LevelSettingsTableFilterComposer
    extends Composer<_$AppDatabase, $LevelSettingsTable> {
  $$LevelSettingsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get platinumPointsReq => $composableBuilder(
    column: $table.platinumPointsReq,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get platinumBidReq => $composableBuilder(
    column: $table.platinumBidReq,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get platinumTripReq => $composableBuilder(
    column: $table.platinumTripReq,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get goldPointsReq => $composableBuilder(
    column: $table.goldPointsReq,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get goldBidReq => $composableBuilder(
    column: $table.goldBidReq,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get goldTripReq => $composableBuilder(
    column: $table.goldTripReq,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get silverPointsReq => $composableBuilder(
    column: $table.silverPointsReq,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get silverBidReq => $composableBuilder(
    column: $table.silverBidReq,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get silverTripReq => $composableBuilder(
    column: $table.silverTripReq,
    builder: (column) => ColumnFilters(column),
  );
}

class $$LevelSettingsTableOrderingComposer
    extends Composer<_$AppDatabase, $LevelSettingsTable> {
  $$LevelSettingsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get platinumPointsReq => $composableBuilder(
    column: $table.platinumPointsReq,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get platinumBidReq => $composableBuilder(
    column: $table.platinumBidReq,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get platinumTripReq => $composableBuilder(
    column: $table.platinumTripReq,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get goldPointsReq => $composableBuilder(
    column: $table.goldPointsReq,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get goldBidReq => $composableBuilder(
    column: $table.goldBidReq,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get goldTripReq => $composableBuilder(
    column: $table.goldTripReq,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get silverPointsReq => $composableBuilder(
    column: $table.silverPointsReq,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get silverBidReq => $composableBuilder(
    column: $table.silverBidReq,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get silverTripReq => $composableBuilder(
    column: $table.silverTripReq,
    builder: (column) => ColumnOrderings(column),
  );
}

class $$LevelSettingsTableAnnotationComposer
    extends Composer<_$AppDatabase, $LevelSettingsTable> {
  $$LevelSettingsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<int> get platinumPointsReq => $composableBuilder(
    column: $table.platinumPointsReq,
    builder: (column) => column,
  );

  GeneratedColumn<double> get platinumBidReq => $composableBuilder(
    column: $table.platinumBidReq,
    builder: (column) => column,
  );

  GeneratedColumn<double> get platinumTripReq => $composableBuilder(
    column: $table.platinumTripReq,
    builder: (column) => column,
  );

  GeneratedColumn<int> get goldPointsReq => $composableBuilder(
    column: $table.goldPointsReq,
    builder: (column) => column,
  );

  GeneratedColumn<double> get goldBidReq => $composableBuilder(
    column: $table.goldBidReq,
    builder: (column) => column,
  );

  GeneratedColumn<double> get goldTripReq => $composableBuilder(
    column: $table.goldTripReq,
    builder: (column) => column,
  );

  GeneratedColumn<int> get silverPointsReq => $composableBuilder(
    column: $table.silverPointsReq,
    builder: (column) => column,
  );

  GeneratedColumn<double> get silverBidReq => $composableBuilder(
    column: $table.silverBidReq,
    builder: (column) => column,
  );

  GeneratedColumn<double> get silverTripReq => $composableBuilder(
    column: $table.silverTripReq,
    builder: (column) => column,
  );
}

class $$LevelSettingsTableTableManager
    extends
        RootTableManager<
          _$AppDatabase,
          $LevelSettingsTable,
          LevelSetting,
          $$LevelSettingsTableFilterComposer,
          $$LevelSettingsTableOrderingComposer,
          $$LevelSettingsTableAnnotationComposer,
          $$LevelSettingsTableCreateCompanionBuilder,
          $$LevelSettingsTableUpdateCompanionBuilder,
          (
            LevelSetting,
            BaseReferences<_$AppDatabase, $LevelSettingsTable, LevelSetting>,
          ),
          LevelSetting,
          PrefetchHooks Function()
        > {
  $$LevelSettingsTableTableManager(_$AppDatabase db, $LevelSettingsTable table)
    : super(
        TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$LevelSettingsTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$LevelSettingsTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$LevelSettingsTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback:
              ({
                Value<int> id = const Value.absent(),
                Value<int> platinumPointsReq = const Value.absent(),
                Value<double> platinumBidReq = const Value.absent(),
                Value<double> platinumTripReq = const Value.absent(),
                Value<int> goldPointsReq = const Value.absent(),
                Value<double> goldBidReq = const Value.absent(),
                Value<double> goldTripReq = const Value.absent(),
                Value<int> silverPointsReq = const Value.absent(),
                Value<double> silverBidReq = const Value.absent(),
                Value<double> silverTripReq = const Value.absent(),
              }) => LevelSettingsCompanion(
                id: id,
                platinumPointsReq: platinumPointsReq,
                platinumBidReq: platinumBidReq,
                platinumTripReq: platinumTripReq,
                goldPointsReq: goldPointsReq,
                goldBidReq: goldBidReq,
                goldTripReq: goldTripReq,
                silverPointsReq: silverPointsReq,
                silverBidReq: silverBidReq,
                silverTripReq: silverTripReq,
              ),
          createCompanionCallback:
              ({
                Value<int> id = const Value.absent(),
                required int platinumPointsReq,
                required double platinumBidReq,
                required double platinumTripReq,
                required int goldPointsReq,
                required double goldBidReq,
                required double goldTripReq,
                required int silverPointsReq,
                required double silverBidReq,
                required double silverTripReq,
              }) => LevelSettingsCompanion.insert(
                id: id,
                platinumPointsReq: platinumPointsReq,
                platinumBidReq: platinumBidReq,
                platinumTripReq: platinumTripReq,
                goldPointsReq: goldPointsReq,
                goldBidReq: goldBidReq,
                goldTripReq: goldTripReq,
                silverPointsReq: silverPointsReq,
                silverBidReq: silverBidReq,
                silverTripReq: silverTripReq,
              ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ),
      );
}

typedef $$LevelSettingsTableProcessedTableManager =
    ProcessedTableManager<
      _$AppDatabase,
      $LevelSettingsTable,
      LevelSetting,
      $$LevelSettingsTableFilterComposer,
      $$LevelSettingsTableOrderingComposer,
      $$LevelSettingsTableAnnotationComposer,
      $$LevelSettingsTableCreateCompanionBuilder,
      $$LevelSettingsTableUpdateCompanionBuilder,
      (
        LevelSetting,
        BaseReferences<_$AppDatabase, $LevelSettingsTable, LevelSetting>,
      ),
      LevelSetting,
      PrefetchHooks Function()
    >;
typedef $$SparePartsTableCreateCompanionBuilder =
    SparePartsCompanion Function({
      Value<String> uuid,
      Value<int> id,
      required String partName,
      required String partType,
      required double price,
      required int mileageLimit,
      required int initialMileage,
      Value<DateTime> installationDate,
      Value<int> currentMileage,
      Value<bool> warningStatus,
      Value<int> replacementCount,
      Value<String> notes,
      Value<DateTime> createdAt,
      Value<DateTime> updatedAt,
      Value<DateTime?> deletedAt,
      Value<SyncStatus> syncStatus,
    });
typedef $$SparePartsTableUpdateCompanionBuilder =
    SparePartsCompanion Function({
      Value<String> uuid,
      Value<int> id,
      Value<String> partName,
      Value<String> partType,
      Value<double> price,
      Value<int> mileageLimit,
      Value<int> initialMileage,
      Value<DateTime> installationDate,
      Value<int> currentMileage,
      Value<bool> warningStatus,
      Value<int> replacementCount,
      Value<String> notes,
      Value<DateTime> createdAt,
      Value<DateTime> updatedAt,
      Value<DateTime?> deletedAt,
      Value<SyncStatus> syncStatus,
    });

final class $$SparePartsTableReferences
    extends BaseReferences<_$AppDatabase, $SparePartsTable, SparePart> {
  $$SparePartsTableReferences(super.$_db, super.$_table, super.$_typedResult);

  static MultiTypedResultKey<
    $SparePartsHistoryTable,
    List<SparePartsHistoryData>
  >
  _sparePartsHistoryRefsTable(_$AppDatabase db) =>
      MultiTypedResultKey.fromTable(
        db.sparePartsHistory,
        aliasName: $_aliasNameGenerator(
          db.spareParts.id,
          db.sparePartsHistory.sparePartId,
        ),
      );

  $$SparePartsHistoryTableProcessedTableManager get sparePartsHistoryRefs {
    final manager = $$SparePartsHistoryTableTableManager(
      $_db,
      $_db.sparePartsHistory,
    ).filter((f) => f.sparePartId.id.sqlEquals($_itemColumn<int>('id')!));

    final cache = $_typedResult.readTableOrNull(
      _sparePartsHistoryRefsTable($_db),
    );
    return ProcessedTableManager(
      manager.$state.copyWith(prefetchedData: cache),
    );
  }
}

class $$SparePartsTableFilterComposer
    extends Composer<_$AppDatabase, $SparePartsTable> {
  $$SparePartsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get uuid => $composableBuilder(
    column: $table.uuid,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get partName => $composableBuilder(
    column: $table.partName,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get partType => $composableBuilder(
    column: $table.partType,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get price => $composableBuilder(
    column: $table.price,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get mileageLimit => $composableBuilder(
    column: $table.mileageLimit,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get initialMileage => $composableBuilder(
    column: $table.initialMileage,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<DateTime> get installationDate => $composableBuilder(
    column: $table.installationDate,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get currentMileage => $composableBuilder(
    column: $table.currentMileage,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<bool> get warningStatus => $composableBuilder(
    column: $table.warningStatus,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get replacementCount => $composableBuilder(
    column: $table.replacementCount,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get notes => $composableBuilder(
    column: $table.notes,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
    column: $table.createdAt,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<DateTime> get updatedAt => $composableBuilder(
    column: $table.updatedAt,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<DateTime> get deletedAt => $composableBuilder(
    column: $table.deletedAt,
    builder: (column) => ColumnFilters(column),
  );

  ColumnWithTypeConverterFilters<SyncStatus, SyncStatus, String>
  get syncStatus => $composableBuilder(
    column: $table.syncStatus,
    builder: (column) => ColumnWithTypeConverterFilters(column),
  );

  Expression<bool> sparePartsHistoryRefs(
    Expression<bool> Function($$SparePartsHistoryTableFilterComposer f) f,
  ) {
    final $$SparePartsHistoryTableFilterComposer composer = $composerBuilder(
      composer: this,
      getCurrentColumn: (t) => t.id,
      referencedTable: $db.sparePartsHistory,
      getReferencedColumn: (t) => t.sparePartId,
      builder:
          (
            joinBuilder, {
            $addJoinBuilderToRootComposer,
            $removeJoinBuilderFromRootComposer,
          }) => $$SparePartsHistoryTableFilterComposer(
            $db: $db,
            $table: $db.sparePartsHistory,
            $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
            joinBuilder: joinBuilder,
            $removeJoinBuilderFromRootComposer:
                $removeJoinBuilderFromRootComposer,
          ),
    );
    return f(composer);
  }
}

class $$SparePartsTableOrderingComposer
    extends Composer<_$AppDatabase, $SparePartsTable> {
  $$SparePartsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get uuid => $composableBuilder(
    column: $table.uuid,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get partName => $composableBuilder(
    column: $table.partName,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get partType => $composableBuilder(
    column: $table.partType,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get price => $composableBuilder(
    column: $table.price,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get mileageLimit => $composableBuilder(
    column: $table.mileageLimit,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get initialMileage => $composableBuilder(
    column: $table.initialMileage,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get installationDate => $composableBuilder(
    column: $table.installationDate,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get currentMileage => $composableBuilder(
    column: $table.currentMileage,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<bool> get warningStatus => $composableBuilder(
    column: $table.warningStatus,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get replacementCount => $composableBuilder(
    column: $table.replacementCount,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get notes => $composableBuilder(
    column: $table.notes,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
    column: $table.createdAt,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get updatedAt => $composableBuilder(
    column: $table.updatedAt,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get deletedAt => $composableBuilder(
    column: $table.deletedAt,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get syncStatus => $composableBuilder(
    column: $table.syncStatus,
    builder: (column) => ColumnOrderings(column),
  );
}

class $$SparePartsTableAnnotationComposer
    extends Composer<_$AppDatabase, $SparePartsTable> {
  $$SparePartsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get uuid =>
      $composableBuilder(column: $table.uuid, builder: (column) => column);

  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get partName =>
      $composableBuilder(column: $table.partName, builder: (column) => column);

  GeneratedColumn<String> get partType =>
      $composableBuilder(column: $table.partType, builder: (column) => column);

  GeneratedColumn<double> get price =>
      $composableBuilder(column: $table.price, builder: (column) => column);

  GeneratedColumn<int> get mileageLimit => $composableBuilder(
    column: $table.mileageLimit,
    builder: (column) => column,
  );

  GeneratedColumn<int> get initialMileage => $composableBuilder(
    column: $table.initialMileage,
    builder: (column) => column,
  );

  GeneratedColumn<DateTime> get installationDate => $composableBuilder(
    column: $table.installationDate,
    builder: (column) => column,
  );

  GeneratedColumn<int> get currentMileage => $composableBuilder(
    column: $table.currentMileage,
    builder: (column) => column,
  );

  GeneratedColumn<bool> get warningStatus => $composableBuilder(
    column: $table.warningStatus,
    builder: (column) => column,
  );

  GeneratedColumn<int> get replacementCount => $composableBuilder(
    column: $table.replacementCount,
    builder: (column) => column,
  );

  GeneratedColumn<String> get notes =>
      $composableBuilder(column: $table.notes, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  GeneratedColumn<DateTime> get updatedAt =>
      $composableBuilder(column: $table.updatedAt, builder: (column) => column);

  GeneratedColumn<DateTime> get deletedAt =>
      $composableBuilder(column: $table.deletedAt, builder: (column) => column);

  GeneratedColumnWithTypeConverter<SyncStatus, String> get syncStatus =>
      $composableBuilder(
        column: $table.syncStatus,
        builder: (column) => column,
      );

  Expression<T> sparePartsHistoryRefs<T extends Object>(
    Expression<T> Function($$SparePartsHistoryTableAnnotationComposer a) f,
  ) {
    final $$SparePartsHistoryTableAnnotationComposer composer =
        $composerBuilder(
          composer: this,
          getCurrentColumn: (t) => t.id,
          referencedTable: $db.sparePartsHistory,
          getReferencedColumn: (t) => t.sparePartId,
          builder:
              (
                joinBuilder, {
                $addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer,
              }) => $$SparePartsHistoryTableAnnotationComposer(
                $db: $db,
                $table: $db.sparePartsHistory,
                $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
                joinBuilder: joinBuilder,
                $removeJoinBuilderFromRootComposer:
                    $removeJoinBuilderFromRootComposer,
              ),
        );
    return f(composer);
  }
}

class $$SparePartsTableTableManager
    extends
        RootTableManager<
          _$AppDatabase,
          $SparePartsTable,
          SparePart,
          $$SparePartsTableFilterComposer,
          $$SparePartsTableOrderingComposer,
          $$SparePartsTableAnnotationComposer,
          $$SparePartsTableCreateCompanionBuilder,
          $$SparePartsTableUpdateCompanionBuilder,
          (SparePart, $$SparePartsTableReferences),
          SparePart,
          PrefetchHooks Function({bool sparePartsHistoryRefs})
        > {
  $$SparePartsTableTableManager(_$AppDatabase db, $SparePartsTable table)
    : super(
        TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$SparePartsTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$SparePartsTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$SparePartsTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback:
              ({
                Value<String> uuid = const Value.absent(),
                Value<int> id = const Value.absent(),
                Value<String> partName = const Value.absent(),
                Value<String> partType = const Value.absent(),
                Value<double> price = const Value.absent(),
                Value<int> mileageLimit = const Value.absent(),
                Value<int> initialMileage = const Value.absent(),
                Value<DateTime> installationDate = const Value.absent(),
                Value<int> currentMileage = const Value.absent(),
                Value<bool> warningStatus = const Value.absent(),
                Value<int> replacementCount = const Value.absent(),
                Value<String> notes = const Value.absent(),
                Value<DateTime> createdAt = const Value.absent(),
                Value<DateTime> updatedAt = const Value.absent(),
                Value<DateTime?> deletedAt = const Value.absent(),
                Value<SyncStatus> syncStatus = const Value.absent(),
              }) => SparePartsCompanion(
                uuid: uuid,
                id: id,
                partName: partName,
                partType: partType,
                price: price,
                mileageLimit: mileageLimit,
                initialMileage: initialMileage,
                installationDate: installationDate,
                currentMileage: currentMileage,
                warningStatus: warningStatus,
                replacementCount: replacementCount,
                notes: notes,
                createdAt: createdAt,
                updatedAt: updatedAt,
                deletedAt: deletedAt,
                syncStatus: syncStatus,
              ),
          createCompanionCallback:
              ({
                Value<String> uuid = const Value.absent(),
                Value<int> id = const Value.absent(),
                required String partName,
                required String partType,
                required double price,
                required int mileageLimit,
                required int initialMileage,
                Value<DateTime> installationDate = const Value.absent(),
                Value<int> currentMileage = const Value.absent(),
                Value<bool> warningStatus = const Value.absent(),
                Value<int> replacementCount = const Value.absent(),
                Value<String> notes = const Value.absent(),
                Value<DateTime> createdAt = const Value.absent(),
                Value<DateTime> updatedAt = const Value.absent(),
                Value<DateTime?> deletedAt = const Value.absent(),
                Value<SyncStatus> syncStatus = const Value.absent(),
              }) => SparePartsCompanion.insert(
                uuid: uuid,
                id: id,
                partName: partName,
                partType: partType,
                price: price,
                mileageLimit: mileageLimit,
                initialMileage: initialMileage,
                installationDate: installationDate,
                currentMileage: currentMileage,
                warningStatus: warningStatus,
                replacementCount: replacementCount,
                notes: notes,
                createdAt: createdAt,
                updatedAt: updatedAt,
                deletedAt: deletedAt,
                syncStatus: syncStatus,
              ),
          withReferenceMapper: (p0) => p0
              .map(
                (e) => (
                  e.readTable(table),
                  $$SparePartsTableReferences(db, table, e),
                ),
              )
              .toList(),
          prefetchHooksCallback: ({sparePartsHistoryRefs = false}) {
            return PrefetchHooks(
              db: db,
              explicitlyWatchedTables: [
                if (sparePartsHistoryRefs) db.sparePartsHistory,
              ],
              addJoins: null,
              getPrefetchedDataCallback: (items) async {
                return [
                  if (sparePartsHistoryRefs)
                    await $_getPrefetchedData<
                      SparePart,
                      $SparePartsTable,
                      SparePartsHistoryData
                    >(
                      currentTable: table,
                      referencedTable: $$SparePartsTableReferences
                          ._sparePartsHistoryRefsTable(db),
                      managerFromTypedResult: (p0) =>
                          $$SparePartsTableReferences(
                            db,
                            table,
                            p0,
                          ).sparePartsHistoryRefs,
                      referencedItemsForCurrentItem: (item, referencedItems) =>
                          referencedItems.where(
                            (e) => e.sparePartId == item.id,
                          ),
                      typedResults: items,
                    ),
                ];
              },
            );
          },
        ),
      );
}

typedef $$SparePartsTableProcessedTableManager =
    ProcessedTableManager<
      _$AppDatabase,
      $SparePartsTable,
      SparePart,
      $$SparePartsTableFilterComposer,
      $$SparePartsTableOrderingComposer,
      $$SparePartsTableAnnotationComposer,
      $$SparePartsTableCreateCompanionBuilder,
      $$SparePartsTableUpdateCompanionBuilder,
      (SparePart, $$SparePartsTableReferences),
      SparePart,
      PrefetchHooks Function({bool sparePartsHistoryRefs})
    >;
typedef $$SparePartsHistoryTableCreateCompanionBuilder =
    SparePartsHistoryCompanion Function({
      Value<String> uuid,
      Value<int> id,
      required String partName,
      required String partType,
      required double price,
      required DateTime replacementDate,
      required int mileageAtReplacement,
      required int sparePartId,
      required DateTime installationDate,
      required int initialMileage,
      Value<String> replacementReason,
      Value<int?> replacedByPartId,
      Value<int> replacementCount,
      Value<int> usageDays,
      Value<int> usageMileage,
      Value<String> notes,
      Value<DateTime> createdAt,
      Value<DateTime> updatedAt,
      Value<DateTime?> deletedAt,
      Value<SyncStatus> syncStatus,
    });
typedef $$SparePartsHistoryTableUpdateCompanionBuilder =
    SparePartsHistoryCompanion Function({
      Value<String> uuid,
      Value<int> id,
      Value<String> partName,
      Value<String> partType,
      Value<double> price,
      Value<DateTime> replacementDate,
      Value<int> mileageAtReplacement,
      Value<int> sparePartId,
      Value<DateTime> installationDate,
      Value<int> initialMileage,
      Value<String> replacementReason,
      Value<int?> replacedByPartId,
      Value<int> replacementCount,
      Value<int> usageDays,
      Value<int> usageMileage,
      Value<String> notes,
      Value<DateTime> createdAt,
      Value<DateTime> updatedAt,
      Value<DateTime?> deletedAt,
      Value<SyncStatus> syncStatus,
    });

final class $$SparePartsHistoryTableReferences
    extends
        BaseReferences<
          _$AppDatabase,
          $SparePartsHistoryTable,
          SparePartsHistoryData
        > {
  $$SparePartsHistoryTableReferences(
    super.$_db,
    super.$_table,
    super.$_typedResult,
  );

  static $SparePartsTable _sparePartIdTable(_$AppDatabase db) =>
      db.spareParts.createAlias(
        $_aliasNameGenerator(
          db.sparePartsHistory.sparePartId,
          db.spareParts.id,
        ),
      );

  $$SparePartsTableProcessedTableManager get sparePartId {
    final $_column = $_itemColumn<int>('spare_part_id')!;

    final manager = $$SparePartsTableTableManager(
      $_db,
      $_db.spareParts,
    ).filter((f) => f.id.sqlEquals($_column));
    final item = $_typedResult.readTableOrNull(_sparePartIdTable($_db));
    if (item == null) return manager;
    return ProcessedTableManager(
      manager.$state.copyWith(prefetchedData: [item]),
    );
  }
}

class $$SparePartsHistoryTableFilterComposer
    extends Composer<_$AppDatabase, $SparePartsHistoryTable> {
  $$SparePartsHistoryTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get uuid => $composableBuilder(
    column: $table.uuid,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get partName => $composableBuilder(
    column: $table.partName,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get partType => $composableBuilder(
    column: $table.partType,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get price => $composableBuilder(
    column: $table.price,
    builder: (column) => ColumnFilters(column),
  );

  ColumnWithTypeConverterFilters<DateTime, DateTime, DateTime>
  get replacementDate => $composableBuilder(
    column: $table.replacementDate,
    builder: (column) => ColumnWithTypeConverterFilters(column),
  );

  ColumnFilters<int> get mileageAtReplacement => $composableBuilder(
    column: $table.mileageAtReplacement,
    builder: (column) => ColumnFilters(column),
  );

  ColumnWithTypeConverterFilters<DateTime, DateTime, DateTime>
  get installationDate => $composableBuilder(
    column: $table.installationDate,
    builder: (column) => ColumnWithTypeConverterFilters(column),
  );

  ColumnFilters<int> get initialMileage => $composableBuilder(
    column: $table.initialMileage,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get replacementReason => $composableBuilder(
    column: $table.replacementReason,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get replacedByPartId => $composableBuilder(
    column: $table.replacedByPartId,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get replacementCount => $composableBuilder(
    column: $table.replacementCount,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get usageDays => $composableBuilder(
    column: $table.usageDays,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get usageMileage => $composableBuilder(
    column: $table.usageMileage,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get notes => $composableBuilder(
    column: $table.notes,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
    column: $table.createdAt,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<DateTime> get updatedAt => $composableBuilder(
    column: $table.updatedAt,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<DateTime> get deletedAt => $composableBuilder(
    column: $table.deletedAt,
    builder: (column) => ColumnFilters(column),
  );

  ColumnWithTypeConverterFilters<SyncStatus, SyncStatus, String>
  get syncStatus => $composableBuilder(
    column: $table.syncStatus,
    builder: (column) => ColumnWithTypeConverterFilters(column),
  );

  $$SparePartsTableFilterComposer get sparePartId {
    final $$SparePartsTableFilterComposer composer = $composerBuilder(
      composer: this,
      getCurrentColumn: (t) => t.sparePartId,
      referencedTable: $db.spareParts,
      getReferencedColumn: (t) => t.id,
      builder:
          (
            joinBuilder, {
            $addJoinBuilderToRootComposer,
            $removeJoinBuilderFromRootComposer,
          }) => $$SparePartsTableFilterComposer(
            $db: $db,
            $table: $db.spareParts,
            $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
            joinBuilder: joinBuilder,
            $removeJoinBuilderFromRootComposer:
                $removeJoinBuilderFromRootComposer,
          ),
    );
    return composer;
  }
}

class $$SparePartsHistoryTableOrderingComposer
    extends Composer<_$AppDatabase, $SparePartsHistoryTable> {
  $$SparePartsHistoryTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get uuid => $composableBuilder(
    column: $table.uuid,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get partName => $composableBuilder(
    column: $table.partName,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get partType => $composableBuilder(
    column: $table.partType,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get price => $composableBuilder(
    column: $table.price,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get replacementDate => $composableBuilder(
    column: $table.replacementDate,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get mileageAtReplacement => $composableBuilder(
    column: $table.mileageAtReplacement,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get installationDate => $composableBuilder(
    column: $table.installationDate,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get initialMileage => $composableBuilder(
    column: $table.initialMileage,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get replacementReason => $composableBuilder(
    column: $table.replacementReason,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get replacedByPartId => $composableBuilder(
    column: $table.replacedByPartId,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get replacementCount => $composableBuilder(
    column: $table.replacementCount,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get usageDays => $composableBuilder(
    column: $table.usageDays,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get usageMileage => $composableBuilder(
    column: $table.usageMileage,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get notes => $composableBuilder(
    column: $table.notes,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
    column: $table.createdAt,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get updatedAt => $composableBuilder(
    column: $table.updatedAt,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get deletedAt => $composableBuilder(
    column: $table.deletedAt,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get syncStatus => $composableBuilder(
    column: $table.syncStatus,
    builder: (column) => ColumnOrderings(column),
  );

  $$SparePartsTableOrderingComposer get sparePartId {
    final $$SparePartsTableOrderingComposer composer = $composerBuilder(
      composer: this,
      getCurrentColumn: (t) => t.sparePartId,
      referencedTable: $db.spareParts,
      getReferencedColumn: (t) => t.id,
      builder:
          (
            joinBuilder, {
            $addJoinBuilderToRootComposer,
            $removeJoinBuilderFromRootComposer,
          }) => $$SparePartsTableOrderingComposer(
            $db: $db,
            $table: $db.spareParts,
            $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
            joinBuilder: joinBuilder,
            $removeJoinBuilderFromRootComposer:
                $removeJoinBuilderFromRootComposer,
          ),
    );
    return composer;
  }
}

class $$SparePartsHistoryTableAnnotationComposer
    extends Composer<_$AppDatabase, $SparePartsHistoryTable> {
  $$SparePartsHistoryTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get uuid =>
      $composableBuilder(column: $table.uuid, builder: (column) => column);

  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get partName =>
      $composableBuilder(column: $table.partName, builder: (column) => column);

  GeneratedColumn<String> get partType =>
      $composableBuilder(column: $table.partType, builder: (column) => column);

  GeneratedColumn<double> get price =>
      $composableBuilder(column: $table.price, builder: (column) => column);

  GeneratedColumnWithTypeConverter<DateTime, DateTime> get replacementDate =>
      $composableBuilder(
        column: $table.replacementDate,
        builder: (column) => column,
      );

  GeneratedColumn<int> get mileageAtReplacement => $composableBuilder(
    column: $table.mileageAtReplacement,
    builder: (column) => column,
  );

  GeneratedColumnWithTypeConverter<DateTime, DateTime> get installationDate =>
      $composableBuilder(
        column: $table.installationDate,
        builder: (column) => column,
      );

  GeneratedColumn<int> get initialMileage => $composableBuilder(
    column: $table.initialMileage,
    builder: (column) => column,
  );

  GeneratedColumn<String> get replacementReason => $composableBuilder(
    column: $table.replacementReason,
    builder: (column) => column,
  );

  GeneratedColumn<int> get replacedByPartId => $composableBuilder(
    column: $table.replacedByPartId,
    builder: (column) => column,
  );

  GeneratedColumn<int> get replacementCount => $composableBuilder(
    column: $table.replacementCount,
    builder: (column) => column,
  );

  GeneratedColumn<int> get usageDays =>
      $composableBuilder(column: $table.usageDays, builder: (column) => column);

  GeneratedColumn<int> get usageMileage => $composableBuilder(
    column: $table.usageMileage,
    builder: (column) => column,
  );

  GeneratedColumn<String> get notes =>
      $composableBuilder(column: $table.notes, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  GeneratedColumn<DateTime> get updatedAt =>
      $composableBuilder(column: $table.updatedAt, builder: (column) => column);

  GeneratedColumn<DateTime> get deletedAt =>
      $composableBuilder(column: $table.deletedAt, builder: (column) => column);

  GeneratedColumnWithTypeConverter<SyncStatus, String> get syncStatus =>
      $composableBuilder(
        column: $table.syncStatus,
        builder: (column) => column,
      );

  $$SparePartsTableAnnotationComposer get sparePartId {
    final $$SparePartsTableAnnotationComposer composer = $composerBuilder(
      composer: this,
      getCurrentColumn: (t) => t.sparePartId,
      referencedTable: $db.spareParts,
      getReferencedColumn: (t) => t.id,
      builder:
          (
            joinBuilder, {
            $addJoinBuilderToRootComposer,
            $removeJoinBuilderFromRootComposer,
          }) => $$SparePartsTableAnnotationComposer(
            $db: $db,
            $table: $db.spareParts,
            $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
            joinBuilder: joinBuilder,
            $removeJoinBuilderFromRootComposer:
                $removeJoinBuilderFromRootComposer,
          ),
    );
    return composer;
  }
}

class $$SparePartsHistoryTableTableManager
    extends
        RootTableManager<
          _$AppDatabase,
          $SparePartsHistoryTable,
          SparePartsHistoryData,
          $$SparePartsHistoryTableFilterComposer,
          $$SparePartsHistoryTableOrderingComposer,
          $$SparePartsHistoryTableAnnotationComposer,
          $$SparePartsHistoryTableCreateCompanionBuilder,
          $$SparePartsHistoryTableUpdateCompanionBuilder,
          (SparePartsHistoryData, $$SparePartsHistoryTableReferences),
          SparePartsHistoryData,
          PrefetchHooks Function({bool sparePartId})
        > {
  $$SparePartsHistoryTableTableManager(
    _$AppDatabase db,
    $SparePartsHistoryTable table,
  ) : super(
        TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$SparePartsHistoryTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$SparePartsHistoryTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$SparePartsHistoryTableAnnotationComposer(
                $db: db,
                $table: table,
              ),
          updateCompanionCallback:
              ({
                Value<String> uuid = const Value.absent(),
                Value<int> id = const Value.absent(),
                Value<String> partName = const Value.absent(),
                Value<String> partType = const Value.absent(),
                Value<double> price = const Value.absent(),
                Value<DateTime> replacementDate = const Value.absent(),
                Value<int> mileageAtReplacement = const Value.absent(),
                Value<int> sparePartId = const Value.absent(),
                Value<DateTime> installationDate = const Value.absent(),
                Value<int> initialMileage = const Value.absent(),
                Value<String> replacementReason = const Value.absent(),
                Value<int?> replacedByPartId = const Value.absent(),
                Value<int> replacementCount = const Value.absent(),
                Value<int> usageDays = const Value.absent(),
                Value<int> usageMileage = const Value.absent(),
                Value<String> notes = const Value.absent(),
                Value<DateTime> createdAt = const Value.absent(),
                Value<DateTime> updatedAt = const Value.absent(),
                Value<DateTime?> deletedAt = const Value.absent(),
                Value<SyncStatus> syncStatus = const Value.absent(),
              }) => SparePartsHistoryCompanion(
                uuid: uuid,
                id: id,
                partName: partName,
                partType: partType,
                price: price,
                replacementDate: replacementDate,
                mileageAtReplacement: mileageAtReplacement,
                sparePartId: sparePartId,
                installationDate: installationDate,
                initialMileage: initialMileage,
                replacementReason: replacementReason,
                replacedByPartId: replacedByPartId,
                replacementCount: replacementCount,
                usageDays: usageDays,
                usageMileage: usageMileage,
                notes: notes,
                createdAt: createdAt,
                updatedAt: updatedAt,
                deletedAt: deletedAt,
                syncStatus: syncStatus,
              ),
          createCompanionCallback:
              ({
                Value<String> uuid = const Value.absent(),
                Value<int> id = const Value.absent(),
                required String partName,
                required String partType,
                required double price,
                required DateTime replacementDate,
                required int mileageAtReplacement,
                required int sparePartId,
                required DateTime installationDate,
                required int initialMileage,
                Value<String> replacementReason = const Value.absent(),
                Value<int?> replacedByPartId = const Value.absent(),
                Value<int> replacementCount = const Value.absent(),
                Value<int> usageDays = const Value.absent(),
                Value<int> usageMileage = const Value.absent(),
                Value<String> notes = const Value.absent(),
                Value<DateTime> createdAt = const Value.absent(),
                Value<DateTime> updatedAt = const Value.absent(),
                Value<DateTime?> deletedAt = const Value.absent(),
                Value<SyncStatus> syncStatus = const Value.absent(),
              }) => SparePartsHistoryCompanion.insert(
                uuid: uuid,
                id: id,
                partName: partName,
                partType: partType,
                price: price,
                replacementDate: replacementDate,
                mileageAtReplacement: mileageAtReplacement,
                sparePartId: sparePartId,
                installationDate: installationDate,
                initialMileage: initialMileage,
                replacementReason: replacementReason,
                replacedByPartId: replacedByPartId,
                replacementCount: replacementCount,
                usageDays: usageDays,
                usageMileage: usageMileage,
                notes: notes,
                createdAt: createdAt,
                updatedAt: updatedAt,
                deletedAt: deletedAt,
                syncStatus: syncStatus,
              ),
          withReferenceMapper: (p0) => p0
              .map(
                (e) => (
                  e.readTable(table),
                  $$SparePartsHistoryTableReferences(db, table, e),
                ),
              )
              .toList(),
          prefetchHooksCallback: ({sparePartId = false}) {
            return PrefetchHooks(
              db: db,
              explicitlyWatchedTables: [],
              addJoins:
                  <
                    T extends TableManagerState<
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic
                    >
                  >(state) {
                    if (sparePartId) {
                      state =
                          state.withJoin(
                                currentTable: table,
                                currentColumn: table.sparePartId,
                                referencedTable:
                                    $$SparePartsHistoryTableReferences
                                        ._sparePartIdTable(db),
                                referencedColumn:
                                    $$SparePartsHistoryTableReferences
                                        ._sparePartIdTable(db)
                                        .id,
                              )
                              as T;
                    }

                    return state;
                  },
              getPrefetchedDataCallback: (items) async {
                return [];
              },
            );
          },
        ),
      );
}

typedef $$SparePartsHistoryTableProcessedTableManager =
    ProcessedTableManager<
      _$AppDatabase,
      $SparePartsHistoryTable,
      SparePartsHistoryData,
      $$SparePartsHistoryTableFilterComposer,
      $$SparePartsHistoryTableOrderingComposer,
      $$SparePartsHistoryTableAnnotationComposer,
      $$SparePartsHistoryTableCreateCompanionBuilder,
      $$SparePartsHistoryTableUpdateCompanionBuilder,
      (SparePartsHistoryData, $$SparePartsHistoryTableReferences),
      SparePartsHistoryData,
      PrefetchHooks Function({bool sparePartId})
    >;
typedef $$AppSettingsTableCreateCompanionBuilder =
    AppSettingsCompanion Function({
      Value<int> id,
      required DateTime dateRangeStart,
      required DateTime dateRangeEnd,
      Value<String?> backupDirectoryPath,
      Value<DateTime> updatedAt,
      Value<DateTime?> lastSyncTime,
    });
typedef $$AppSettingsTableUpdateCompanionBuilder =
    AppSettingsCompanion Function({
      Value<int> id,
      Value<DateTime> dateRangeStart,
      Value<DateTime> dateRangeEnd,
      Value<String?> backupDirectoryPath,
      Value<DateTime> updatedAt,
      Value<DateTime?> lastSyncTime,
    });

class $$AppSettingsTableFilterComposer
    extends Composer<_$AppDatabase, $AppSettingsTable> {
  $$AppSettingsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnFilters(column),
  );

  ColumnWithTypeConverterFilters<DateTime, DateTime, DateTime>
  get dateRangeStart => $composableBuilder(
    column: $table.dateRangeStart,
    builder: (column) => ColumnWithTypeConverterFilters(column),
  );

  ColumnWithTypeConverterFilters<DateTime, DateTime, DateTime>
  get dateRangeEnd => $composableBuilder(
    column: $table.dateRangeEnd,
    builder: (column) => ColumnWithTypeConverterFilters(column),
  );

  ColumnFilters<String> get backupDirectoryPath => $composableBuilder(
    column: $table.backupDirectoryPath,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<DateTime> get updatedAt => $composableBuilder(
    column: $table.updatedAt,
    builder: (column) => ColumnFilters(column),
  );

  ColumnWithTypeConverterFilters<DateTime?, DateTime, DateTime>
  get lastSyncTime => $composableBuilder(
    column: $table.lastSyncTime,
    builder: (column) => ColumnWithTypeConverterFilters(column),
  );
}

class $$AppSettingsTableOrderingComposer
    extends Composer<_$AppDatabase, $AppSettingsTable> {
  $$AppSettingsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get dateRangeStart => $composableBuilder(
    column: $table.dateRangeStart,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get dateRangeEnd => $composableBuilder(
    column: $table.dateRangeEnd,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get backupDirectoryPath => $composableBuilder(
    column: $table.backupDirectoryPath,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get updatedAt => $composableBuilder(
    column: $table.updatedAt,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get lastSyncTime => $composableBuilder(
    column: $table.lastSyncTime,
    builder: (column) => ColumnOrderings(column),
  );
}

class $$AppSettingsTableAnnotationComposer
    extends Composer<_$AppDatabase, $AppSettingsTable> {
  $$AppSettingsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumnWithTypeConverter<DateTime, DateTime> get dateRangeStart =>
      $composableBuilder(
        column: $table.dateRangeStart,
        builder: (column) => column,
      );

  GeneratedColumnWithTypeConverter<DateTime, DateTime> get dateRangeEnd =>
      $composableBuilder(
        column: $table.dateRangeEnd,
        builder: (column) => column,
      );

  GeneratedColumn<String> get backupDirectoryPath => $composableBuilder(
    column: $table.backupDirectoryPath,
    builder: (column) => column,
  );

  GeneratedColumn<DateTime> get updatedAt =>
      $composableBuilder(column: $table.updatedAt, builder: (column) => column);

  GeneratedColumnWithTypeConverter<DateTime?, DateTime> get lastSyncTime =>
      $composableBuilder(
        column: $table.lastSyncTime,
        builder: (column) => column,
      );
}

class $$AppSettingsTableTableManager
    extends
        RootTableManager<
          _$AppDatabase,
          $AppSettingsTable,
          AppSetting,
          $$AppSettingsTableFilterComposer,
          $$AppSettingsTableOrderingComposer,
          $$AppSettingsTableAnnotationComposer,
          $$AppSettingsTableCreateCompanionBuilder,
          $$AppSettingsTableUpdateCompanionBuilder,
          (
            AppSetting,
            BaseReferences<_$AppDatabase, $AppSettingsTable, AppSetting>,
          ),
          AppSetting,
          PrefetchHooks Function()
        > {
  $$AppSettingsTableTableManager(_$AppDatabase db, $AppSettingsTable table)
    : super(
        TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$AppSettingsTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$AppSettingsTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$AppSettingsTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback:
              ({
                Value<int> id = const Value.absent(),
                Value<DateTime> dateRangeStart = const Value.absent(),
                Value<DateTime> dateRangeEnd = const Value.absent(),
                Value<String?> backupDirectoryPath = const Value.absent(),
                Value<DateTime> updatedAt = const Value.absent(),
                Value<DateTime?> lastSyncTime = const Value.absent(),
              }) => AppSettingsCompanion(
                id: id,
                dateRangeStart: dateRangeStart,
                dateRangeEnd: dateRangeEnd,
                backupDirectoryPath: backupDirectoryPath,
                updatedAt: updatedAt,
                lastSyncTime: lastSyncTime,
              ),
          createCompanionCallback:
              ({
                Value<int> id = const Value.absent(),
                required DateTime dateRangeStart,
                required DateTime dateRangeEnd,
                Value<String?> backupDirectoryPath = const Value.absent(),
                Value<DateTime> updatedAt = const Value.absent(),
                Value<DateTime?> lastSyncTime = const Value.absent(),
              }) => AppSettingsCompanion.insert(
                id: id,
                dateRangeStart: dateRangeStart,
                dateRangeEnd: dateRangeEnd,
                backupDirectoryPath: backupDirectoryPath,
                updatedAt: updatedAt,
                lastSyncTime: lastSyncTime,
              ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ),
      );
}

typedef $$AppSettingsTableProcessedTableManager =
    ProcessedTableManager<
      _$AppDatabase,
      $AppSettingsTable,
      AppSetting,
      $$AppSettingsTableFilterComposer,
      $$AppSettingsTableOrderingComposer,
      $$AppSettingsTableAnnotationComposer,
      $$AppSettingsTableCreateCompanionBuilder,
      $$AppSettingsTableUpdateCompanionBuilder,
      (
        AppSetting,
        BaseReferences<_$AppDatabase, $AppSettingsTable, AppSetting>,
      ),
      AppSetting,
      PrefetchHooks Function()
    >;

class $AppDatabaseManager {
  final _$AppDatabase _db;
  $AppDatabaseManager(this._db);
  $$IncomeTableTableManager get income =>
      $$IncomeTableTableManager(_db, _db.income);
  $$OrdersTableTableManager get orders =>
      $$OrdersTableTableManager(_db, _db.orders);
  $$PerformanceTableTableManager get performance =>
      $$PerformanceTableTableManager(_db, _db.performance);
  $$LevelSettingsTableTableManager get levelSettings =>
      $$LevelSettingsTableTableManager(_db, _db.levelSettings);
  $$SparePartsTableTableManager get spareParts =>
      $$SparePartsTableTableManager(_db, _db.spareParts);
  $$SparePartsHistoryTableTableManager get sparePartsHistory =>
      $$SparePartsHistoryTableTableManager(_db, _db.sparePartsHistory);
  $$AppSettingsTableTableManager get appSettings =>
      $$AppSettingsTableTableManager(_db, _db.appSettings);
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../services/sync_service.dart';
import '../../utils/date_helper.dart';

/// A widget that displays the current sync status
class SyncStatusWidget extends ConsumerWidget {
  final bool mini;

  const SyncStatusWidget({super.key, this.mini = false});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final syncState = ref.watch(syncStateProvider);
    final lastSyncTime = ref.watch(lastSyncTimeProvider);
    final colorScheme = Theme.of(context).colorScheme;

    return syncState.when(
      data: (status) =>
          _buildStatusWidget(context, status, lastSyncTime, colorScheme),
      loading: () => _buildLoading(colorScheme),
      error: (error, stackTrace) => _buildError(colorScheme),
    );
  }

  Widget _buildStatusWidget(
    BuildContext context,
    String status,
    DateTime? lastSyncTime,
    ColorScheme colorScheme,
  ) {
    // Different icon and color based on status
    IconData icon;
    Color color;
    String tooltip;

    switch (status) {
      case 'synced':
        icon = Icons.cloud_done;
        color = colorScheme.primary;
        tooltip = 'Synced with cloud';
        break;
      case 'pendingUpload':
        icon = Icons.cloud_upload;
        color = colorScheme.tertiary;
        tooltip = 'Pending sync';
        break;
      case 'conflict':
        icon = Icons.cloud_off;
        color = colorScheme.error;
        tooltip = 'Sync conflict';
        break;
      default:
        icon = Icons.cloud_queue;
        color = colorScheme.secondary;
        tooltip = 'Unknown sync status';
    }

    if (mini) {
      return Tooltip(
        message: tooltip,
        child: Icon(icon, color: color, size: 16),
      );
    }

    return Tooltip(
      message: tooltip,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color),
          const SizedBox(width: 8),
          if (lastSyncTime != null) ...[
            Text(
              'Last sync: ${_formatSyncTime(lastSyncTime)}',
              style: TextStyle(
                fontSize: 12,
                color: colorScheme.onSurface.withAlpha(179),
              ),
            ),
          ] else ...[
            Text(
              'Not synced yet',
              style: TextStyle(
                fontSize: 12,
                color: colorScheme.onSurface.withAlpha(179),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildLoading(ColorScheme colorScheme) {
    if (mini) {
      return SizedBox(
        width: 16,
        height: 16,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          color: colorScheme.primary,
        ),
      );
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: 16,
          height: 16,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            color: colorScheme.primary,
          ),
        ),
        const SizedBox(width: 8),
        Text(
          'Syncing...',
          style: TextStyle(
            fontSize: 12,
            color: colorScheme.onSurface.withAlpha(179),
          ),
        ),
      ],
    );
  }

  Widget _buildError(ColorScheme colorScheme) {
    if (mini) {
      return Icon(Icons.sync_problem, color: colorScheme.error, size: 16);
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(Icons.sync_problem, color: colorScheme.error),
        const SizedBox(width: 8),
        Text(
          'Sync failed',
          style: TextStyle(fontSize: 12, color: colorScheme.error),
        ),
      ],
    );
  }

  String _formatSyncTime(DateTime time) {
    // For relative time (e.g., "5 minutes ago")
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inSeconds < 60) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      final minutes = difference.inMinutes;
      return '$minutes ${minutes == 1 ? 'minute' : 'minutes'} ago';
    } else if (difference.inHours < 24) {
      final hours = difference.inHours;
      return '$hours ${hours == 1 ? 'hour' : 'hours'} ago';
    } else if (difference.inDays < 7) {
      final days = difference.inDays;
      return '$days ${days == 1 ? 'day' : 'days'} ago';
    } else {
      // For older dates, use the DateHelper to format the date and time
      return DateHelper.formatDateTimeForDisplay(time);
    }
  }
}

import 'package:flutter/material.dart';
import '../../theme/design_tokens.dart';
import '../../utils/text_style_helper.dart';

/// Button types available in the application
enum AppButtonType {
  /// Primary action button with filled background
  primary,

  /// Secondary action button with filled background
  secondary,

  /// Outline button with border and transparent background
  outline,

  /// Text button with no background or border
  text,

  /// Danger button for destructive actions
  danger,
}

/// Button sizes available in the application
enum AppButtonSize {
  /// Small button for compact UI areas
  small,

  /// Medium button for standard UI areas (default)
  medium,

  /// Large button for prominent actions
  large,
}

/// A reusable button component that follows the design system
///
/// This button adapts its appearance based on the specified type and size,
/// ensuring consistency across the application.
class AppButton extends StatelessWidget {
  /// The text to display on the button
  final String text;

  /// The action to perform when the button is pressed
  final VoidCallback? onPressed;

  /// The type of button to display
  final AppButtonType type;

  /// The size of the button
  final AppButtonSize size;

  /// Optional icon to display before the text
  final IconData? icon;

  /// Whether the button should expand to fill its parent width
  final bool isFullWidth;

  /// Whether the button is in a loading state
  final bool isLoading;

  /// Custom padding for the button
  final EdgeInsetsGeometry? padding;

  /// Custom border radius for the button
  final double? borderRadius;

  /// Creates a button with the specified parameters
  const AppButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.type = AppButtonType.primary,
    this.size = AppButtonSize.medium,
    this.icon,
    this.isFullWidth = false,
    this.isLoading = false,
    this.padding,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    // Determine button style based on type
    final ButtonStyle buttonStyle = _getButtonStyle(context);

    // Determine button padding based on size
    final EdgeInsetsGeometry buttonPadding = padding ?? _getButtonPadding();

    // Create button content
    final Widget buttonContent = _createButtonContent(context);

    // Create the appropriate button widget based on type
    Widget button;
    switch (type) {
      case AppButtonType.outline:
        button = OutlinedButton(
          style: buttonStyle,
          onPressed: isLoading ? null : onPressed,
          child: Padding(padding: buttonPadding, child: buttonContent),
        );
        break;
      case AppButtonType.text:
        button = TextButton(
          style: buttonStyle,
          onPressed: isLoading ? null : onPressed,
          child: Padding(padding: buttonPadding, child: buttonContent),
        );
        break;
      case AppButtonType.primary:
      case AppButtonType.secondary:
      case AppButtonType.danger:
        button = ElevatedButton(
          style: buttonStyle,
          onPressed: isLoading ? null : onPressed,
          child: Padding(padding: buttonPadding, child: buttonContent),
        );
        break;
    }

    // Apply full width if needed
    if (isFullWidth) {
      return SizedBox(width: double.infinity, child: button);
    }

    return button;
  }

  /// Creates the content of the button (text, icon, loading indicator)
  Widget _createButtonContent(BuildContext context) {
    // If loading, show a loading indicator
    if (isLoading) {
      return SizedBox(
        height: _getIconSize(),
        width: _getIconSize(),
        child: CircularProgressIndicator(
          strokeWidth: 2.0,
          valueColor: AlwaysStoppedAnimation<Color>(_getContentColor()),
        ),
      );
    }

    // If there's an icon, show it with the text
    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: _getIconSize()),
          SizedBox(width: DesignTokens.spacingSm),
          Text(text, style: _getTextStyle(context)),
        ],
      );
    }

    // Otherwise, just show the text
    return Text(text, style: _getTextStyle(context));
  }

  /// Gets the appropriate button style based on the button type
  ButtonStyle _getButtonStyle(BuildContext context) {
    final double buttonRadius = borderRadius ?? DesignTokens.radiusButton;

    switch (type) {
      case AppButtonType.primary:
        return ElevatedButton.styleFrom(
          backgroundColor: DesignTokens.colorPrimary,
          foregroundColor: DesignTokens.colorTextLight,
          disabledBackgroundColor: DesignTokens.colorPrimary.withAlpha(128),
          disabledForegroundColor: DesignTokens.colorTextLight.withAlpha(179),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(buttonRadius),
          ),
          elevation: 1.0,
        );
      case AppButtonType.secondary:
        return ElevatedButton.styleFrom(
          backgroundColor: DesignTokens.colorSecondary,
          foregroundColor: DesignTokens.colorTextLight,
          disabledBackgroundColor: DesignTokens.colorSecondary.withAlpha(128),
          disabledForegroundColor: DesignTokens.colorTextLight.withAlpha(179),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(buttonRadius),
          ),
          elevation: 1.0,
        );
      case AppButtonType.outline:
        return OutlinedButton.styleFrom(
          foregroundColor: DesignTokens.colorPrimary,
          disabledForegroundColor: DesignTokens.colorPrimary.withAlpha(128),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(buttonRadius),
          ),
          side: BorderSide(
            color: onPressed == null
                ? DesignTokens.colorPrimary.withAlpha(128)
                : DesignTokens.colorPrimary,
            width: DesignTokens.widthBorder,
          ),
        );
      case AppButtonType.text:
        return TextButton.styleFrom(
          foregroundColor: DesignTokens.colorPrimary,
          disabledForegroundColor: DesignTokens.colorPrimary.withAlpha(128),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(buttonRadius),
          ),
        );
      case AppButtonType.danger:
        return ElevatedButton.styleFrom(
          backgroundColor: DesignTokens.colorError,
          foregroundColor: DesignTokens.colorTextLight,
          disabledBackgroundColor: DesignTokens.colorError.withAlpha(128),
          disabledForegroundColor: DesignTokens.colorTextLight.withAlpha(179),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(buttonRadius),
          ),
          elevation: 1.0,
        );
    }
  }

  /// Gets the appropriate padding based on the button size
  EdgeInsetsGeometry _getButtonPadding() {
    switch (size) {
      case AppButtonSize.small:
        return EdgeInsets.symmetric(
          horizontal: DesignTokens.spacingMd,
          vertical: DesignTokens.spacingXs,
        );
      case AppButtonSize.large:
        return EdgeInsets.symmetric(
          horizontal: DesignTokens.spacingXl,
          vertical: DesignTokens.spacingMd,
        );
      case AppButtonSize.medium:
        return DesignTokens.paddingButton;
    }
  }

  /// Gets the appropriate text style based on the button size
  TextStyle _getTextStyle(BuildContext context) {
    switch (size) {
      case AppButtonSize.small:
        return TextStyleHelper.buttonTextSmall(context);
      case AppButtonSize.large:
        return TextStyleHelper.buttonTextLarge(context);
      case AppButtonSize.medium:
        return TextStyleHelper.buttonText(context);
    }
  }

  /// Gets the appropriate icon size based on the button size
  double _getIconSize() {
    switch (size) {
      case AppButtonSize.small:
        return DesignTokens.sizeIconSmall;
      case AppButtonSize.large:
        return DesignTokens.sizeIconLarge;
      case AppButtonSize.medium:
        return DesignTokens.sizeIconMedium;
    }
  }

  /// Gets the appropriate content color based on the button type
  Color _getContentColor() {
    switch (type) {
      case AppButtonType.outline:
      case AppButtonType.text:
        return DesignTokens.colorPrimary;
      case AppButtonType.primary:
      case AppButtonType.secondary:
      case AppButtonType.danger:
        return DesignTokens.colorTextLight;
    }
  }
}

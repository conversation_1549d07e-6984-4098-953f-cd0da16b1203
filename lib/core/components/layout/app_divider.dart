import 'package:flutter/material.dart';
import '../../theme/design_tokens.dart';
import '../../utils/text_style_helper.dart';

/// A reusable divider component that follows the design system
///
/// This divider adapts its appearance based on the specified parameters,
/// providing a consistent way to separate content across the application.
class AppDivider extends StatelessWidget {
  /// Optional label to display in the divider
  final String? label;

  /// Optional color for the divider
  final Color? color;

  /// Optional thickness for the divider
  final double? thickness;

  /// Optional indent for the divider
  final double? indent;

  /// Optional end indent for the divider
  final double? endIndent;

  /// Optional height for the divider
  final double? height;

  /// Whether to show the divider as a vertical line
  final bool vertical;

  /// Optional width for the vertical divider
  final double? width;

  /// Optional label alignment
  final TextAlign? labelAlign;

  /// Optional label style
  final TextStyle? labelStyle;

  /// Optional label padding
  final EdgeInsetsGeometry? labelPadding;

  /// Optional label background color
  final Color? labelBackgroundColor;

  /// Creates a divider with the specified parameters
  const AppDivider({
    super.key,
    this.label,
    this.color,
    this.thickness,
    this.indent,
    this.endIndent,
    this.height,
    this.vertical = false,
    this.width,
    this.labelAlign,
    this.labelStyle,
    this.labelPadding,
    this.labelBackgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    // If there's a label, create a divider with a label
    if (label != null && label!.isNotEmpty && !vertical) {
      return _buildLabeledDivider(context);
    }

    // Otherwise, create a simple divider
    if (vertical) {
      return VerticalDivider(
        color: color ?? DesignTokens.colorDivider,
        thickness: thickness ?? 1.0,
        indent: indent,
        endIndent: endIndent,
        width: width ?? 16.0,
      );
    } else {
      return Divider(
        color: color ?? DesignTokens.colorDivider,
        thickness: thickness ?? 1.0,
        indent: indent,
        endIndent: endIndent,
        height: height ?? 16.0,
      );
    }
  }

  /// Builds a divider with a label
  Widget _buildLabeledDivider(BuildContext context) {
    return Row(
      children: [
        // Left divider
        Expanded(
          child: Divider(
            color: color ?? DesignTokens.colorDivider,
            thickness: thickness ?? 1.0,
            indent: indent,
            endIndent: 0,
            height: height ?? 16.0,
          ),
        ),

        // Label
        Container(
          padding:
              labelPadding ??
              EdgeInsets.symmetric(horizontal: DesignTokens.spacingMd),
          color: labelBackgroundColor,
          child: Text(
            label!,
            style:
                labelStyle ??
                TextStyleHelper.bodySmall(
                  context,
                ).copyWith(color: DesignTokens.colorTextSecondary),
            textAlign: labelAlign ?? TextAlign.center,
          ),
        ),

        // Right divider
        Expanded(
          child: Divider(
            color: color ?? DesignTokens.colorDivider,
            thickness: thickness ?? 1.0,
            indent: 0,
            endIndent: endIndent,
            height: height ?? 16.0,
          ),
        ),
      ],
    );
  }
}

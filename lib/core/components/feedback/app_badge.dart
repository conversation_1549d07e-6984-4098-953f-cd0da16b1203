import 'package:flutter/material.dart';
import '../../theme/design_tokens.dart';
import '../../utils/text_style_helper.dart';

/// Badge types available in the application
enum AppBadgeType {
  /// Primary badge with primary color
  primary,

  /// Secondary badge with secondary color
  secondary,

  /// Success badge with success color
  success,

  /// Warning badge with warning color
  warning,

  /// Error badge with error color
  error,

  /// Info badge with info color
  info,

  /// Custom badge with custom colors
  custom,
}

/// Badge sizes available in the application
enum AppBadgeSize {
  /// Small badge
  small,

  /// Medium badge (default)
  medium,

  /// Large badge
  large,
}

/// A reusable badge component that follows the design system
///
/// This badge adapts its appearance based on the specified parameters,
/// providing a consistent way to display badges across the application.
class AppBadge extends StatelessWidget {
  /// The text to display in the badge
  final String text;

  /// The type of badge to display
  final AppBadgeType type;

  /// The size of the badge
  final AppBadgeSize size;

  /// Optional background color for the badge (only used with custom type)
  final Color? backgroundColor;

  /// Optional text color for the badge (only used with custom type)
  final Color? textColor;

  /// Optional icon to display in the badge
  final IconData? icon;

  /// Optional border radius for the badge
  final double? borderRadius;

  /// Optional padding for the badge
  final EdgeInsetsGeometry? padding;

  /// Whether to show a filled badge or an outlined badge
  final bool filled;

  /// Optional callback when the badge is tapped
  final VoidCallback? onTap;

  /// Creates a badge with the specified parameters
  const AppBadge({
    super.key,
    required this.text,
    this.type = AppBadgeType.primary,
    this.size = AppBadgeSize.medium,
    this.backgroundColor,
    this.textColor,
    this.icon,
    this.borderRadius,
    this.padding,
    this.filled = true,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    // Determine badge colors
    final Color badgeBackgroundColor = _getBackgroundColor();
    final Color badgeTextColor = _getTextColor();

    // Determine badge padding
    final EdgeInsetsGeometry badgePadding = padding ?? _getPadding();

    // Determine badge border radius
    final double badgeBorderRadius = borderRadius ?? _getBorderRadius();

    // Create badge content
    final Widget badgeContent = Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (icon != null) ...[
          Icon(icon, color: badgeTextColor, size: _getIconSize()),
          SizedBox(width: DesignTokens.spacingXxs),
        ],
        Text(
          text,
          style: _getTextStyle(context).copyWith(color: badgeTextColor),
        ),
      ],
    );

    // Create badge container
    Widget badge = Container(
      padding: badgePadding,
      decoration: BoxDecoration(
        color: filled ? badgeBackgroundColor : Colors.transparent,
        borderRadius: BorderRadius.circular(badgeBorderRadius),
        border: filled
            ? null
            : Border.all(color: badgeBackgroundColor, width: 1.0),
      ),
      child: badgeContent,
    );

    // Add tap behavior if needed
    if (onTap != null) {
      badge = GestureDetector(onTap: onTap, child: badge);
    }

    return badge;
  }

  /// Gets the appropriate background color based on the badge type
  Color _getBackgroundColor() {
    if (type == AppBadgeType.custom && backgroundColor != null) {
      return backgroundColor!;
    }

    switch (type) {
      case AppBadgeType.primary:
        return filled
            ? DesignTokens.colorPrimary
            : DesignTokens.colorPrimary.withAlpha(26);
      case AppBadgeType.secondary:
        return filled
            ? DesignTokens.colorSecondary
            : DesignTokens.colorSecondary.withAlpha(26);
      case AppBadgeType.success:
        return filled
            ? DesignTokens.colorSuccess
            : DesignTokens.colorSuccessLight;
      case AppBadgeType.warning:
        return filled
            ? DesignTokens.colorWarning
            : DesignTokens.colorWarningLight;
      case AppBadgeType.error:
        return filled ? DesignTokens.colorError : DesignTokens.colorErrorLight;
      case AppBadgeType.info:
        return filled ? DesignTokens.colorInfo : DesignTokens.colorInfoLight;
      case AppBadgeType.custom:
        return filled
            ? DesignTokens.colorPrimary
            : DesignTokens.colorPrimary.withAlpha(26);
    }
  }

  /// Gets the appropriate text color based on the badge type
  Color _getTextColor() {
    if (type == AppBadgeType.custom && textColor != null) {
      return textColor!;
    }

    if (!filled) {
      switch (type) {
        case AppBadgeType.primary:
          return DesignTokens.colorPrimary;
        case AppBadgeType.secondary:
          return DesignTokens.colorSecondary;
        case AppBadgeType.success:
          return DesignTokens.colorSuccess;
        case AppBadgeType.warning:
          return DesignTokens.colorWarning;
        case AppBadgeType.error:
          return DesignTokens.colorError;
        case AppBadgeType.info:
          return DesignTokens.colorInfo;
        case AppBadgeType.custom:
          return DesignTokens.colorPrimary;
      }
    }

    return DesignTokens.colorTextLight;
  }

  /// Gets the appropriate padding based on the badge size
  EdgeInsetsGeometry _getPadding() {
    switch (size) {
      case AppBadgeSize.small:
        return EdgeInsets.symmetric(
          horizontal: DesignTokens.spacingXs,
          vertical: 2.0,
        );
      case AppBadgeSize.large:
        return EdgeInsets.symmetric(
          horizontal: DesignTokens.spacingMd,
          vertical: DesignTokens.spacingXs,
        );
      case AppBadgeSize.medium:
        return EdgeInsets.symmetric(
          horizontal: DesignTokens.spacingXs,
          vertical: 4.0,
        );
    }
  }

  /// Gets the appropriate border radius based on the badge size
  double _getBorderRadius() {
    switch (size) {
      case AppBadgeSize.small:
        return 4.0;
      case AppBadgeSize.large:
        return 8.0;
      case AppBadgeSize.medium:
        return 6.0;
    }
  }

  /// Gets the appropriate text style based on the badge size
  TextStyle _getTextStyle(BuildContext context) {
    switch (size) {
      case AppBadgeSize.small:
        return TextStyleHelper.bodySmall(
          context,
        ).copyWith(fontWeight: FontWeight.w500);
      case AppBadgeSize.large:
        return TextStyleHelper.bodyMedium(
          context,
        ).copyWith(fontWeight: FontWeight.w500);
      case AppBadgeSize.medium:
        return TextStyleHelper.bodySmall(
          context,
        ).copyWith(fontWeight: FontWeight.w500);
    }
  }

  /// Gets the appropriate icon size based on the badge size
  double _getIconSize() {
    switch (size) {
      case AppBadgeSize.small:
        return 10.0;
      case AppBadgeSize.large:
        return 16.0;
      case AppBadgeSize.medium:
        return 12.0;
    }
  }
}

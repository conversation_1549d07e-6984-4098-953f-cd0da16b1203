import 'package:flutter/material.dart';
import '../../theme/design_tokens.dart';

/// Card types available in the application
enum AppCardType {
  /// Standard card with default styling
  standard,

  /// Elevated card with shadow
  elevated,

  /// Outlined card with border
  outlined,

  /// Filled card with background color
  filled,
}

/// A reusable card component that follows the design system
///
/// This card adapts its appearance based on the specified type,
/// ensuring consistency across the application.
class AppCard extends StatelessWidget {
  /// The content to display inside the card
  final Widget child;

  /// The type of card to display
  final AppCardType type;

  /// Optional padding for the card content
  final EdgeInsetsGeometry? padding;

  /// Optional margin for the card
  final EdgeInsetsGeometry? margin;

  /// Optional border radius for the card
  final double? borderRadius;

  /// Optional background color for the card
  final Color? backgroundColor;

  /// Optional border color for the card (only used with outlined type)
  final Color? borderColor;

  /// Optional elevation for the card (only used with elevated type)
  final double? elevation;

  /// Optional onTap callback for the card
  final VoidCallback? onTap;

  /// Optional header widget to display at the top of the card
  final Widget? header;

  /// Optional footer widget to display at the bottom of the card
  final Widget? footer;

  /// Creates a card with the specified parameters
  const AppCard({
    super.key,
    required this.child,
    this.type = AppCardType.standard,
    this.padding,
    this.margin,
    this.borderRadius,
    this.backgroundColor,
    this.borderColor,
    this.elevation,
    this.onTap,
    this.header,
    this.footer,
  });

  @override
  Widget build(BuildContext context) {
    // Determine card style based on type
    final double cardRadius = borderRadius ?? DesignTokens.radiusCard;
    final double cardElevation = elevation ?? _getElevation();
    final Color cardBackgroundColor = backgroundColor ?? _getBackgroundColor();
    final BorderSide cardBorderSide = _getBorderSide();

    // Create the card shape
    final ShapeBorder cardShape = RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(cardRadius),
      side: cardBorderSide,
    );

    // Create the card content
    Widget cardContent = child;

    // Add header if provided
    if (header != null) {
      cardContent = Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          header!,
          if (padding != null)
            Padding(padding: padding!, child: child)
          else
            Padding(padding: DesignTokens.paddingCard, child: child),
        ],
      );
    }
    // Add footer if provided
    else if (footer != null) {
      cardContent = Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (padding != null)
            Padding(padding: padding!, child: child)
          else
            Padding(padding: DesignTokens.paddingCard, child: child),
          footer!,
        ],
      );
    }
    // Add padding if provided
    else if (padding != null) {
      cardContent = Padding(padding: padding!, child: child);
    }
    // Use default padding
    else {
      cardContent = Padding(padding: DesignTokens.paddingCard, child: child);
    }

    // Create the card
    Widget card = Card(
      elevation: cardElevation,
      margin: margin ?? EdgeInsets.zero,
      shape: cardShape,
      color: cardBackgroundColor,
      clipBehavior: Clip.antiAlias,
      child: cardContent,
    );

    // Add tap behavior if provided
    if (onTap != null) {
      card = InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(cardRadius),
        child: card,
      );
    }

    return card;
  }

  /// Gets the appropriate elevation based on the card type
  double _getElevation() {
    switch (type) {
      case AppCardType.elevated:
        return 4.0;
      case AppCardType.standard:
        return DesignTokens.elevationCard;
      case AppCardType.outlined:
      case AppCardType.filled:
        return 0.0;
    }
  }

  /// Gets the appropriate background color based on the card type
  Color _getBackgroundColor() {
    switch (type) {
      case AppCardType.filled:
        return DesignTokens.colorPrimaryLight.withAlpha(26);
      case AppCardType.standard:
      case AppCardType.elevated:
      case AppCardType.outlined:
        return DesignTokens.colorCardBackground;
    }
  }

  /// Gets the appropriate border side based on the card type
  BorderSide _getBorderSide() {
    switch (type) {
      case AppCardType.outlined:
        return BorderSide(
          color: borderColor ?? DesignTokens.colorBorder,
          width: DesignTokens.widthBorder,
        );
      case AppCardType.standard:
      case AppCardType.elevated:
      case AppCardType.filled:
        return BorderSide.none;
    }
  }
}

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../theme/design_tokens.dart';
import '../../utils/text_style_helper.dart';

/// A reusable text field component that follows the design system
///
/// This text field adapts its appearance based on the specified parameters,
/// ensuring consistency across the application.
class AppTextField extends StatelessWidget {
  /// The controller for the text field
  final TextEditingController controller;

  /// The label text to display above the text field
  final String labelText;

  /// Optional hint text to display when the text field is empty
  final String? hintText;

  /// Optional helper text to display below the text field
  final String? helperText;

  /// Optional error text to display below the text field
  final String? errorText;

  /// Optional prefix icon to display inside the text field
  final IconData? prefixIcon;

  /// Optional suffix icon to display inside the text field
  final IconData? suffixIcon;

  /// Optional suffix widget to display inside the text field
  final Widget? suffix;

  /// Whether to obscure the text (for passwords)
  final bool obscureText;

  /// The keyboard type to use for the text field
  final TextInputType? keyboardType;

  /// The text input action to use for the text field
  final TextInputAction? textInputAction;

  /// The focus node for the text field
  final FocusNode? focusNode;

  /// The focus node to move to when the text input action is triggered
  final FocusNode? nextFocusNode;

  /// Whether the text field is enabled
  final bool enabled;

  /// Whether the text field is read-only
  final bool readOnly;

  /// The maximum number of lines for the text field
  final int? maxLines;

  /// The minimum number of lines for the text field
  final int? minLines;

  /// The maximum length of the text field
  final int? maxLength;

  /// Optional input formatters for the text field
  final List<TextInputFormatter>? inputFormatters;

  /// Optional validator function for the text field
  final String? Function(String?)? validator;

  /// Optional callback when the text field value changes
  final Function(String)? onChanged;

  /// Optional callback when the text field is tapped
  final Function()? onTap;

  /// Optional callback when the suffix icon is tapped
  final Function()? onSuffixIconTap;

  /// Whether to auto-validate the text field
  final bool autoValidate;

  /// Whether to show a clear button when the text field has content
  final bool showClearButton;

  /// Whether to show a character counter
  final bool showCounter;

  /// Whether to auto-focus the text field when it appears
  final bool autofocus;

  /// Creates a text field with the specified parameters
  const AppTextField({
    super.key,
    required this.controller,
    required this.labelText,
    this.hintText,
    this.helperText,
    this.errorText,
    this.prefixIcon,
    this.suffixIcon,
    this.suffix,
    this.obscureText = false,
    this.keyboardType,
    this.textInputAction,
    this.focusNode,
    this.nextFocusNode,
    this.enabled = true,
    this.readOnly = false,
    this.maxLines = 1,
    this.minLines,
    this.maxLength,
    this.inputFormatters,
    this.validator,
    this.onChanged,
    this.onTap,
    this.onSuffixIconTap,
    this.autoValidate = false,
    this.showClearButton = false,
    this.showCounter = false,
    this.autofocus = false,
  });

  @override
  Widget build(BuildContext context) {
    // Create suffix icon if needed
    Widget? suffixWidget;
    if (showClearButton && controller.text.isNotEmpty) {
      suffixWidget = GestureDetector(
        onTap: () {
          controller.clear();
          if (onChanged != null) {
            onChanged!('');
          }
        },
        child: Icon(
          Icons.clear,
          color: DesignTokens.colorTextSecondary,
          size: DesignTokens.sizeIconSmall,
        ),
      );
    } else if (suffixIcon != null) {
      suffixWidget = GestureDetector(
        onTap: onSuffixIconTap,
        child: Icon(
          suffixIcon,
          color: DesignTokens.colorPrimary,
          size: DesignTokens.sizeIconMedium,
        ),
      );
    } else if (suffix != null) {
      suffixWidget = suffix;
    }

    // Create prefix icon if needed
    Widget? prefixWidget;
    if (prefixIcon != null) {
      prefixWidget = Icon(
        prefixIcon,
        color: DesignTokens.colorPrimary,
        size: DesignTokens.sizeIconMedium,
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label text
        if (labelText.isNotEmpty) ...[
          Text(labelText, style: TextStyleHelper.labelText(context)),
          SizedBox(height: DesignTokens.spacingXs),
        ],

        // Text field
        TextFormField(
          controller: controller,
          decoration: InputDecoration(
            hintText: hintText,
            errorText: errorText,
            helperText: helperText,
            prefixIcon: prefixWidget != null
                ? Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: DesignTokens.spacingMd,
                    ),
                    child: prefixWidget,
                  )
                : null,
            suffixIcon: suffixWidget != null
                ? Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: DesignTokens.spacingMd,
                    ),
                    child: suffixWidget,
                  )
                : null,
            filled: true,
            fillColor: enabled
                ? DesignTokens.colorSurface
                : DesignTokens.colorBackground,
            contentPadding: EdgeInsets.symmetric(
              horizontal: DesignTokens.spacingMd,
              vertical: DesignTokens.spacingMd,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(DesignTokens.radiusButton),
              borderSide: BorderSide(
                color: DesignTokens.colorBorder,
                width: DesignTokens.widthBorder,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(DesignTokens.radiusButton),
              borderSide: BorderSide(
                color: DesignTokens.colorBorder,
                width: DesignTokens.widthBorder,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(DesignTokens.radiusButton),
              borderSide: BorderSide(
                color: DesignTokens.colorPrimary,
                width: DesignTokens.widthBorderFocus,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(DesignTokens.radiusButton),
              borderSide: BorderSide(
                color: DesignTokens.colorError,
                width: DesignTokens.widthBorder,
              ),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(DesignTokens.radiusButton),
              borderSide: BorderSide(
                color: DesignTokens.colorError,
                width: DesignTokens.widthBorderFocus,
              ),
            ),
            disabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(DesignTokens.radiusButton),
              borderSide: BorderSide(
                color: DesignTokens.colorBorder.withAlpha(128),
                width: DesignTokens.widthBorder,
              ),
            ),
            counterText: showCounter ? null : '',
          ),
          style: TextStyleHelper.bodyMedium(context),
          obscureText: obscureText,
          keyboardType: keyboardType,
          textInputAction: textInputAction,
          focusNode: focusNode,
          enabled: enabled,
          readOnly: readOnly,
          maxLines: maxLines,
          minLines: minLines,
          maxLength: maxLength,
          inputFormatters: inputFormatters,
          validator: validator,
          onChanged: onChanged,
          onTap: onTap,
          onFieldSubmitted: (value) {
            if (nextFocusNode != null) {
              FocusScope.of(context).requestFocus(nextFocusNode);
            }
          },
          autovalidateMode: autoValidate
              ? AutovalidateMode.onUserInteraction
              : AutovalidateMode.disabled,
          autofocus: autofocus,
        ),
      ],
    );
  }
}

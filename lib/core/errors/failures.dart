import 'package:freezed_annotation/freezed_annotation.dart';

part 'failures.freezed.dart';

@freezed
sealed class Failure with _$Failure {
  const Failure._();

  const factory Failure.server({String? message}) = ServerFailure;
  const factory Failure.cache({String? message}) = CacheFailure;
  const factory Failure.network({String? message}) = NetworkFailure;
  const factory Failure.invalidInput({String? message}) = InvalidInputFailure;
  const factory Failure.database({String? message}) = DatabaseFailure;
  const factory Failure.businessLogic({String? message}) = BusinessLogicFailure;
  const factory Failure.notFound({String? message}) = NotFoundFailure;
  const factory Failure.unexpected({String? message}) = UnexpectedFailure;
}

import 'dart:developer' as developer;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../../main.dart';
import 'exceptions.dart';
import 'failures.dart';

/// Global error handler for uncaught exceptions
class AppErrorHandler {
  static void initialize() {
    // Set up Flutter error handling
    FlutterError.onError = (FlutterErrorDetails details) {
      _reportError(details.exception, details.stack);
      // Forward to Flutter for standard handling
      FlutterError.dumpErrorToConsole(details);
    };

    // Set up Dart error handling for async errors
    PlatformDispatcher.instance.onError = (error, stack) {
      _reportError(error, stack);
      return true; // Return true to prevent the error from being passed to other handlers
    };
  }

  /// Log error details and report to monitoring service if available
  static void _reportError(Object error, StackTrace? stackTrace) {
    developer.log(
      'Uncaught Exception',
      name: 'AppErrorHandler',
      error: error,
      stackTrace: stackTrace,
    );

    // In a production app, you would send this to a monitoring service like Sentry
    // Example: Sentry.captureException(error, stackTrace: stackTrace);
  }

  /// Show error dialog to user
  static void showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Show error snackbar to user
  ///
  /// This method uses the global ScaffoldMessengerKey if available,
  /// falling back to the provided context if needed.
  static void showErrorSnackBar(BuildContext context, String message) {
    // Try to use the global key first
    if (rootScaffoldMessengerKey.currentState != null) {
      showGlobalErrorSnackBar(message);
    } else {
      // Fall back to context-based approach if global key isn't available
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  /// Show error snackbar using the global key (can be called from anywhere)
  static void showGlobalErrorSnackBar(String message) {
    rootScaffoldMessengerKey.currentState?.showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// Show success snackbar using the global key (can be called from anywhere)
  static void showGlobalSuccessSnackBar(String message) {
    rootScaffoldMessengerKey.currentState?.showSnackBar(
      SnackBar(
        content: Text(message),
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 3),
      ),
    );
  }
}

/// Convert exception to failure for domain layer
Failure exceptionToFailure(Exception exception) {
  if (exception is DatabaseException) {
    return Failure.database(message: exception.message);
  } else if (exception is NetworkException) {
    return Failure.network(message: exception.message);
  } else if (exception is ValidationException) {
    return Failure.invalidInput(message: exception.message);
  } else if (exception is NotFoundException) {
    return Failure.notFound(message: exception.message);
  } else if (exception is BusinessLogicException) {
    return Failure.businessLogic(message: exception.message);
  } else {
    return Failure.unexpected(message: exception.toString());
  }
}

/// Get user-friendly error message from a failure
String getErrorMessage(Failure failure) {
  return failure.when(
    server: (message) => message ?? 'Server error occurred',
    cache: (message) => message ?? 'Cache error occurred',
    network: (message) =>
        message ?? 'Network error occurred, please check your connection',
    invalidInput: (message) => message ?? 'Invalid input',
    database: (message) => message ?? 'Database error occurred',
    businessLogic: (message) =>
        message ?? 'An error occurred in the application logic',
    notFound: (message) => message ?? 'The requested resource was not found',
    unexpected: (message) => message ?? 'An unexpected error occurred',
  );
}

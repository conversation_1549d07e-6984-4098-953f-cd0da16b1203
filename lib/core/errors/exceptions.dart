/// Custom exceptions for the application
/// These exceptions are used to identify specific error cases
library;

/// Base exception class
class AppException implements Exception {
  final String message;
  final String? code;

  AppException(this.message, {this.code});

  @override
  String toString() =>
      'AppException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// Database-related exceptions
class DatabaseException extends AppException {
  DatabaseException(super.message, {super.code});
}

/// Network-related exceptions
class NetworkException extends AppException {
  NetworkException(super.message, {super.code});
}

/// Input validation exceptions
class ValidationException extends AppException {
  ValidationException(super.message, {super.code});
}

/// Not found exceptions
class NotFoundException extends AppException {
  NotFoundException(super.message, {super.code});
}

/// Authentication exceptions
class AuthException extends AppException {
  AuthException(super.message, {super.code});
}

/// Business logic exceptions
class BusinessLogicException extends AppException {
  BusinessLogicException(super.message, {super.code});
}

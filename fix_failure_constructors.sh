#!/bin/bash

# Script to fix Failure constructor calls to use named parameters

echo "Fixing Failure constructor calls..."

# Find all Dart files and fix Failure constructor calls
find lib/ -name "*.dart" -type f -exec sed -i 's/Failure\.server(\([^)]*\))/Failure.server(message: \1)/g' {} \;
find lib/ -name "*.dart" -type f -exec sed -i 's/Failure\.cache(\([^)]*\))/Failure.cache(message: \1)/g' {} \;
find lib/ -name "*.dart" -type f -exec sed -i 's/Failure\.network(\([^)]*\))/Failure.network(message: \1)/g' {} \;
find lib/ -name "*.dart" -type f -exec sed -i 's/Failure\.invalidInput(\([^)]*\))/Failure.invalidInput(message: \1)/g' {} \;
find lib/ -name "*.dart" -type f -exec sed -i 's/Failure\.database(\([^)]*\))/Failure.database(message: \1)/g' {} \;
find lib/ -name "*.dart" -type f -exec sed -i 's/Failure\.businessLogic(\([^)]*\))/Failure.businessLogic(message: \1)/g' {} \;
find lib/ -name "*.dart" -type f -exec sed -i 's/Failure\.notFound(\([^)]*\))/Failure.notFound(message: \1)/g' {} \;
find lib/ -name "*.dart" -type f -exec sed -i 's/Failure\.unexpected(\([^)]*\))/Failure.unexpected(message: \1)/g' {} \;

# Fix cases where message: message: might have been created
find lib/ -name "*.dart" -type f -exec sed -i 's/message: message:/message:/g' {} \;

echo "Done fixing Failure constructor calls."
